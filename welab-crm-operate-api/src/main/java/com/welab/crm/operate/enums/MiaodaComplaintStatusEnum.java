package com.welab.crm.operate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 喵达投诉单状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Getter
@AllArgsConstructor
public enum MiaodaComplaintStatusEnum {

    /**
     * 刚提交待审核
     */
    PENDING_REVIEW(0, "待平台审核"),

    /**
     * 审核通过
     */
    APPROVED(1, "审核已通过"),

    /**
     * 审核不通过
     */
    REJECTED(2, "审核未通过"),

    /**
     * 待分配
     */
    PENDING_ASSIGN(3, "待分配商家"),

    /**
     * 处理中（分配成功）
     */
    PROCESSING(4, "处理中"),

    /**
     * 分配失败
     */
    ASSIGN_FAILED(5, "分配商家失败"),

    /**
     * 投诉对象已经回复
     */
    REPLIED(6, "已回复"),

    /**
     * 投诉已完成
     */
    COMPLETED(7, "投诉已完成"),

    /**
     * 投诉已关闭
     */
    CLOSED(8, "投诉已关闭"),

    /**
     * 投诉已移除
     */
    REMOVED(9, "投诉已移除");

    private final Integer code;
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static MiaodaComplaintStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiaodaComplaintStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        MiaodaComplaintStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }

    /**
     * 判断是否为有效的处理状态
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidProcessStatus(Integer code) {
        return code != null && (code == PROCESSING.getCode() || code == REPLIED.getCode());
    }

    /**
     * 判断是否为已完成状态
     *
     * @param code 状态码
     * @return 是否已完成
     */
    public static boolean isCompletedStatus(Integer code) {
        return code != null && code == COMPLETED.getCode();
    }

    /**
     * 判断是否为可操作状态（可回复、申诉、结案）
     *
     * @param code 状态码
     * @return 是否可操作
     */
    public static boolean isOperableStatus(Integer code) {
        return code != null && (code == PROCESSING.getCode() || code == REPLIED.getCode());
    }
}
