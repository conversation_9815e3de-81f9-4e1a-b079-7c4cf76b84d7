package com.welab.crm.operate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 喵达投诉单同步状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Getter
@AllArgsConstructor
public enum MiaodaSyncStatusEnum {

    /**
     * 待同步
     */
    PENDING(0, "待同步"),

    /**
     * 已同步
     */
    SUCCESS(1, "已同步"),

    /**
     * 同步失败
     */
    FAILED(2, "同步失败");

    private final Integer code;
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static MiaodaSyncStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiaodaSyncStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        MiaodaSyncStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
