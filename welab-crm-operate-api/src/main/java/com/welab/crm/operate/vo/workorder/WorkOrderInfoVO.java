package com.welab.crm.operate.vo.workorder;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title 
 * @description 查询工单列表响应参数
 * @date 2021/10/26
 */
@Data
@ApiModel(value = "查询工单列表响应对象")
public class WorkOrderInfoVO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = 2000193576151157095L;

	@ApiModelProperty(value = "工单主键ID", name = "id")
    private Long id;
	
	@ApiModelProperty(value = "任务ID", name = "taskId")
    private String taskId;
	
	@ApiModelProperty(value = "流程实例ID", name = "executionId")
    private String executionId;

    @ApiModelProperty(value = "当前任务节点", name = "nodeCode")
    private String nodeCode;

    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

	@ApiModelProperty(value = "是否催单", name = "reminderFlag")
    private String reminderFlag;

	@ApiModelProperty(value="分单时间", name="distributeTime")
	private Date distributeTime;
	
	@ApiModelProperty(value="提单时间", name="submitTime")
	private Date submitTime;

	@ApiModelProperty(value = "工单类型", name = "orderType")
    private String orderType;

    @ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private String orderOneClass;

    @ApiModelProperty(value = "工单二类", name = "orderTwoClass")
    private String orderTwoClass;

    @ApiModelProperty(value = "工单三类", name = "orderThreeClass")
    private String orderThreeClass;

    @ApiModelProperty(value = "用户类型", name = "custType")
    private String custType;
    
    @ApiModelProperty(value = "客户姓名", name = "custName")
    private String customerName;
    
    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;
    
    @ApiModelProperty(value = "是否标记", name = "sign")
    private String sign;
    
    @ApiModelProperty(value = "是否vip", name = "vip")
    private String vip;

    /**
     * 处理状态: 0-处理中,1-已完成
     */
    @ApiModelProperty(value = "催收处理状态", name = "approveStatus")
    private Integer approveStatus;

    /**
     * 紧急状态: 0-不紧急,1-紧急
     */
    @ApiModelProperty(value = "催收紧急状态", name = "urgeStatus")
    private Integer urgeStatus;

    @ApiModelProperty(value = "用户Id", name = "userId")
    private Integer userId;

    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    @ApiModelProperty(value = "加急标识,1-加急,0-未加急", name = "urgentFlag")
    private String urgentFlag;


    @ApiModelProperty(value = "new标识,1-new,0-非new", name = "newFlag")
    private String newFlag;
	
	@ApiModelProperty(value = "是否超时", name = "responseTimeOut")
	private Boolean responseTimeOut;

    @ApiModelProperty(value = "喵达订单号", name = "miaodaSn")
    private String miaodaSn;
}

