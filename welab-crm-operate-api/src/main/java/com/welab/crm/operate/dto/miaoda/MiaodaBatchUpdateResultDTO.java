package com.welab.crm.operate.dto.miaoda;

import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 喵达投诉单批量更新结果DTO
 * 包含成功和失败的更新结果信息
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@ApiModel(value = "喵达投诉单批量更新结果对象")
public class MiaodaBatchUpdateResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总处理数量
     */
    @ApiModelProperty(value = "总处理数量", name = "totalCount")
    private Integer totalCount = 0;

    /**
     * 成功更新数量
     */
    @ApiModelProperty(value = "成功更新数量", name = "successCount")
    private Integer successCount = 0;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量", name = "failureCount")
    private Integer failureCount = 0;

    /**
     * 成功更新的投诉单列表
     */
    @ApiModelProperty(value = "成功更新的投诉单列表", name = "successList")
    private List<MiaodaComplaintVO> successList = new ArrayList<>();

    /**
     * 失败的投诉单号及错误信息
     */
    @ApiModelProperty(value = "失败的投诉单号及错误信息", name = "failureList")
    private List<FailureInfo> failureList = new ArrayList<>();

    /**
     * 失败信息内部类
     */
    @Data
    @ApiModel(value = "失败信息")
    public static class FailureInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 投诉单号
         */
        @ApiModelProperty(value = "投诉单号", name = "sn")
        private String sn;

        /**
         * 错误信息
         */
        @ApiModelProperty(value = "错误信息", name = "errorMessage")
        private String errorMessage;

        /**
         * 错误类型
         */
        @ApiModelProperty(value = "错误类型", name = "errorType")
        private String errorType;

        public FailureInfo() {}

        public FailureInfo(String sn, String errorMessage, String errorType) {
            this.sn = sn;
            this.errorMessage = errorMessage;
            this.errorType = errorType;
        }
    }

    /**
     * 添加成功结果
     */
    public void addSuccess(MiaodaComplaintVO complaint) {
        this.successList.add(complaint);
        this.successCount++;
        this.totalCount++;
    }

    /**
     * 添加失败结果
     */
    public void addFailure(String sn, String errorMessage, String errorType) {
        this.failureList.add(new FailureInfo(sn, errorMessage, errorType));
        this.failureCount++;
        this.totalCount++;
    }

    /**
     * 添加失败结果（简化版本）
     */
    public void addFailure(String sn, String errorMessage) {
        addFailure(sn, errorMessage, "UNKNOWN");
    }

    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return failureCount == 0 && successCount > 0;
    }

    /**
     * 是否全部失败
     */
    public boolean isAllFailure() {
        return successCount == 0 && failureCount > 0;
    }

    /**
     * 是否部分成功
     */
    public boolean isPartialSuccess() {
        return successCount > 0 && failureCount > 0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 获取结果摘要
     */
    public String getSummary() {
        return String.format("总计: %d, 成功: %d, 失败: %d, 成功率: %.1f%%", 
                           totalCount, successCount, failureCount, getSuccessRate());
    }
}
