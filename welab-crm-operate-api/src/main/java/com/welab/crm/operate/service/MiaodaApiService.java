package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.dto.miaoda.*;
import com.welab.crm.operate.dto.workorder.WorkOrderSubmitReqDTO;
import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;

import java.util.List;

/**
 * 喵达API服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface MiaodaApiService {

    /**
     * 获取喵达Token
     *
     * @return Token字符串
     */
    String getToken();

    /**
     * 查询投诉单详情
     *
     * @param queryDTO 查询条件
     * @return 投诉单列表
     */
    List<MiaodaComplaintVO> queryComplaintDetails(MiaodaComplaintQueryDTO queryDTO);

    /**
     * 分页查询投诉单详情
     *
     * @param queryDTO 查询条件（包含分页参数）
     * @return 分页投诉单结果
     */
    Page<MiaodaComplaintVO> queryComplaintDetailsByPage(MiaodaComplaintQueryDTO queryDTO);

    /**
     * 更新投诉单状态
     *
     * @param sns 投诉单号列表
     * @param statusNos 状态版本号列表
     * @return 更新结果
     */
    List<MiaodaComplaintVO> updateComplaintStatus(List<String> sns, List<Integer> statusNos);

    /**
     * 回复投诉单
     *
     * @param replyDTO 回复请求
     * @return 是否成功
     */
    boolean replyComplaint(MiaodaReplyDTO replyDTO);

    /**
     * 申诉投诉单
     *
     * @param appealDTO 申诉请求
     * @return 是否成功
     */
    boolean appealComplaint(MiaodaAppealDTO appealDTO);

    /**
     * 查询申诉状态
     *
     * @param sns 投诉单号列表
     * @return 申诉状态列表
     */
    List<String> queryAppealStatus(List<String> sns);

    /**
     * 申请结案
     *
     * @param completeDTO 结案请求
     * @return 是否成功
     */
    boolean completeComplaint(MiaodaCompleteDTO completeDTO);

    /**
     * 查询快速解决商家信息
     *
     * @param month 月份（格式：2024-1）
     * @return 快速解决信息
     */
    String queryRapidSolveInfo(String month);

    /**
     * 查询套餐余量信息
     *
     * @return 套餐信息
     */
    String queryPlanInfo();

    /**
     * 处理自动回复
     * 查找需要自动回复的投诉单并执行自动回复
     *
     * @return 处理的投诉单数量
     */
    int processAutoReply();

    /**
     * 查询本地投诉单数据
     * 直接查询本地数据库，不调用喵达API，提供快速查询能力
     *
     * @param queryDTO 查询条件
     * @return 分页投诉单结果
     */
    Page<MiaodaComplaintVO> queryLocalComplaints(MiaodaComplaintQueryDTO queryDTO);

    /**
     * 投诉认领
     *
     * @param sn 投诉单号
     * @param content 认领说明
     * @param images 图片列表
     * @param videos 视频列表
     * @return 是否成功
     */
    boolean claimComplaint(String sn, String content, List<String> images, List<String> videos);

    /**
     * 同步投诉单到本地数据库
     *
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 同步数量
     */
    int syncComplaintsToLocal(String startTime, String endTime);

    /**
     * 检查并更新投诉单状态
     *
     * @return 更新数量
     */
    int checkAndUpdateComplaintStatus();

    /**
     * 自动创建工单
     *
     * @param complaintVO 投诉单信息
     * @return 工单编号
     */
    String autoCreateWorkOrder(MiaodaComplaintVO complaintVO);

    /**
     * 处理催单逻辑
     *
     * @param complaintVO    投诉单信息
     * @param userSupplement
     * @return 是否处理成功
     */
    boolean handleReminderLogic(MiaodaComplaintVO complaintVO, Integer userSupplement);

    /**
     * 检测用户补充投诉
     *
     * @param complaintVO 投诉单信息
     * @return 补充的条数
     */
    Integer detectUserSupplement(MiaodaComplaintVO complaintVO);

    /**
     * 根据工单编号查询投诉单
     *
     * @param workOrderNo 工单编号
     * @return 投诉单信息
     */
    List<MiaodaComplaintVO> queryComplaintsByWorkOrderNo(String workOrderNo);

    /**
     * 喵达工单结案判断
     * @param reqDTO
     */
    void handlerCompleteCheck(WorkOrderSubmitReqDTO reqDTO);
}
