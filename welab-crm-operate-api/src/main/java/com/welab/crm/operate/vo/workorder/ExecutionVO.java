package com.welab.crm.operate.vo.workorder;

import lombok.Data;

@Data
public class ExecutionVO {

	/**
	 * 流程id
	 */
	private String executionId;

	/**
	 * 工单号
	 */
	private String orderNo;

	/**
	 * wo_task 表id
	 */
	private Long woTaskId;

	/**
	 * 工单状态
	 */
	private String orderStatus;


	/**
	 * 工单类型
	 */
	private Long type;

	/**
	 * 工单大类编码
	 */
	private Long orderOneClass;

	/**
	 * 工单二类编码
	 */
	private Long orderTwoClass;

	/**
	 * 工单三类编码
	 */
	private Long orderThreeClass;
}
