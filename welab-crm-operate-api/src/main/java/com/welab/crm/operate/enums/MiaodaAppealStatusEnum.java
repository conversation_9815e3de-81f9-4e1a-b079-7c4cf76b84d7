package com.welab.crm.operate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 喵达申诉状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Getter
@AllArgsConstructor
public enum MiaodaAppealStatusEnum {

    /**
     * 未发起申诉
     */
    NOT_STARTED(0, "未发起申诉"),

    /**
     * 申诉中
     */
    PROCESSING(1, "申诉中"),

    /**
     * 申诉驳回
     */
    REJECTED(2, "申诉驳回"),

    /**
     * 申诉通过
     */
    APPROVED(3, "申诉通过");

    private final Integer code;
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static MiaodaAppealStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiaodaAppealStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        MiaodaAppealStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
