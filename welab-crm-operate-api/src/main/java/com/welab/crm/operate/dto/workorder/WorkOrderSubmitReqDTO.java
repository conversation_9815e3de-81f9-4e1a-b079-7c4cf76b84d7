package com.welab.crm.operate.dto.workorder;

import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigTransitionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.operate.annotation.AesDecryptParam;
import com.welab.crm.operate.vo.workorder.WoAttachmentVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description 工单提单参数
 * @date 2021/09/29
 */
@Data
@ApiModel(value = "工单提单请求对象")
public class WorkOrderSubmitReqDTO implements Serializable {

    private static final long serialVersionUID = 4398893436884293034L;
    
    @ApiModelProperty(value = "id", name = "id")
    private Long id;
    
    @ApiModelProperty(value = "流程定义编号")
    @NotBlank
    private String processCode;
    
    @ApiModelProperty(value = "标记", name = "flag")
    private String flag;

    @ApiModelProperty(value = "预约时间", name = "appointDate")
    private String appointTime;

    @ApiModelProperty(value = "预约标题", name = "appointTitle")
    private String appointTitle;

    @ApiModelProperty(value = "客户ID", name = "custId")
    private String custId;
    
    @ApiModelProperty(value = "备用号码", name = "mobileBak")
    @AesDecryptParam
    private String mobileBak;
    
    @ApiModelProperty(value = "备用号码列表")
    private List<String> mobileBakList;
    
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "工单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "工单类型", name = "type")
    private String type;

    @ApiModelProperty(value = "工单大类", name = "orderOneClass")
    private Long orderOneClass;

    @ApiModelProperty(value = "工单二类", name = "orderTwoClass")
    private Long orderTwoClass;

    @ApiModelProperty(value = "工单三类", name = "orderThreeClass")
    private Long orderThreeClass;
    
    @ApiModelProperty(value = "子工单分类", name = "orderCase")
    private Long orderCase;

    @ApiModelProperty(value = "投诉渠道", name = "complaintsChannel")
    private String complaintsChannel;

    @ApiModelProperty(value = "资方简称", name = "fundName")
    private String fundName;

    @ApiModelProperty(value = "投诉渠道与资金方映射关系", name = "matchUp")
    private String matchUp;

    @ApiModelProperty(value = "加急", name = "urgentFlag")
    private String urgentFlag;

    @ApiModelProperty(value = "回访", name = "callbackFlag")
    private String callbackFlag;
    
    @ApiModelProperty(value = "反馈内容", name = "description")
    private String description;
    
    @ApiModelProperty(value = "处理意见", name = "opinion")
    private String opinion;
    
    @ApiModelProperty(value = "回访结果", name = "callbackNote")
    private String callbackNote;
    
    @ApiModelProperty(value = "工单状态", name = "status")
    private String status;
    
    @ApiModelProperty(value = "任务指派信息", name = "assignedDTO")
    private WFConfigAssignedDTO assignedDTO;
    
    @ApiModelProperty(value = "任务流转信息", name = "transitionDTO")
    private WFConfigTransitionDTO transitionDTO;
    
    @ApiModelProperty(value = "任务处理页", name = "executionDTO")
    private WFRunExecutionDTO executionDTO;
    
    @ApiModelProperty(value = "贷款信息", name = "loanList")
    private List<WorkOrderLoanReqDTO> loanList;

    @ApiModelProperty(value = "附件名称列表", name = "fileNameList")
    private List<WoAttachmentVO> fileList;

    @ApiModelProperty(value = "成功贷款笔数", name = "sucLoanCount")
    private Integer sucLoanCount;



    @ApiModelProperty(value = "在途贷款笔数", name = "currentLoanCount")
    private Integer currentLoanCount;

    @ApiModelProperty(value = "手机号", name = "mobile")
    private String mobile;

}
