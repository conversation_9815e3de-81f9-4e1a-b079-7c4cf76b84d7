package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.enums.LoanStateEnum;
import com.welab.crm.interview.enums.PayCodeEnum;
import com.welab.crm.interview.service.FaSuService;
import com.welab.crm.interview.service.FinanceService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.vo.fasu.FaSuVO;
import com.welab.crm.interview.vo.loan.*;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.dto.loan.OutstandingEarlySettleBatchDTO;
import com.welab.crm.operate.dto.loan.OutstandingEarlySettleDTO;
import com.welab.crm.operate.dto.partner.PartnerInfoImportDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.EarlySettledService;
import com.welab.crm.operate.service.ExternalService;
import com.welab.crm.operate.service.LoanTransferService;
import com.welab.crm.operate.service.PartnerInfoService;
import com.welab.crm.operate.vo.loan.LoanImportLabelVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.annotation.LogSaveToDb;
import com.welab.crm.operate.web.constants.Constant;
import com.welab.crm.operate.web.constants.Urls.Loan;
import com.welab.crm.operate.web.util.ResponseUtil;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.finance.repayment.dto.WriteoffDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 * @module 客服项目
 */
@RestController
@Api(description = "客户贷款服务")
@RequestMapping(Loan.ROOT)
@Slf4j
public class LoanApplicationController {

    @Resource
    private LoanApplicationService loanApplicationService;

    @Resource
    private CustOperateService custOperateService;
    
    @Resource
    private LoanTransferService loanTransferService;

    @Resource
    private FaSuService faSuService;

    @Resource
    private EarlySettledService earlySettledService;

    @Resource
    private PartnerInfoService partnerInfoService;
    
    @Resource
    private FinanceService financeService;
    @Autowired
    private ExternalService externalService;


    @GetMapping(value = Loan.V1_LOAN_APPLICATION_HISTORY)
    @ApiOperation(value = Loan.V1_LOAN_APPLICATION_HISTORY_DESC, notes = Loan.V1_LOAN_APPLICATION_HISTORY_DESC)
    public Response<List<LoanApplicationVO>> getLoanList(@RequestHeader("X-Mobile") String mobile, @RequestParam @NotNull Long uuid) {
        List<LoanApplicationVO> loanApplicationList = loanApplicationService.getLoanApplicationList(uuid);
        if (CollectionUtils.isNotEmpty(loanApplicationList)) {
            for(LoanApplicationVO t : loanApplicationList){
                try {
                    PartnerInfoImportDTO partnerInfo = partnerInfoService.queryPartnerInfoByNameAndCode(t.getPartnerName());
                    if (Objects.nonNull(partnerInfo)) {
                        t.setCloseGet(partnerInfo.getCloseGet());
                        t.setInvoiceGet(partnerInfo.getInvoiceGet());
                        t.setCloseQuota(partnerInfo.getCloseQuota());
                    }
                }catch (Exception e){
                    log.info("partnerName is not match");
                }
                // 只查询已放款的单
                if (LoanApplicationStateEnum.DISBURSED.getText().equals(t.getState())){
                    WriteoffDTO writeoffDTO = financeService.getLoanTransferByApplicationId(t.getApplicationId());
                    if (Objects.nonNull(writeoffDTO)){
                        t.setTransferCompany(writeoffDTO.getCompanyName());
                    }
                }
                
            }
            // 如果是 呼入组、在线组、电销组 结清时间＞3年的贷款记录不展示
            if(externalService.isSpecificGroup(mobile, Constant.KEFU_GROUP)){
                loanApplicationList = loanApplicationList.stream().filter(item -> externalService.isClosedIn3Year(item.getApplicationId())).collect(Collectors.toList());
            }
            if(externalService.isSpecificGroup(mobile, Constant.TOUSU_GROUP)){
                loanApplicationList.forEach(loanApplicationVO -> {
                    if(!externalService.isClosedIn3Year(loanApplicationVO.getApplicationId())){
                        loanApplicationVO.setBackgroundColor("grey");
                    }
                });
            }
        }
        return Response.success(loanApplicationList);
    }

    @GetMapping(value = Loan.V1_LOAN_APPLICATION_DETAILS)
    @ApiOperation(value = Loan.V1_LOAN_APPLICATION_DETAILS_DESC, notes = Loan.V1_LOAN_APPLICATION_DETAILS_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "applicationId", value = "贷款号", paramType = "query"),
    })
    public Response<LoanDetailsVO> getLoanDetails(@RequestParam String applicationId) {
        return Response.success(loanApplicationService.getLoanDetails(applicationId));
    }

    @GetMapping(value = Loan.V1_LOAN_APPLICATION_URL)
    @ApiOperation(value = Loan.V1_LOAN_APPLICATION_URL_DESC, notes = Loan.V1_LOAN_APPLICATION_URL_DESC)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applicationId", value = "贷款号", paramType = "query"),
    })
    @LogSaveToDb
    public ResponseEntity<byte[]> getLoanAgreement(@RequestParam String applicationId, HttpServletRequest request, HttpServletResponse response) {
        return ResponseUtil.buildExportResponse(loanApplicationService.getApplicationAgreementUrl(applicationId).getResult(), "借款合同.pdf", request, response);
    }

    @GetMapping(value = Loan.V1_LOAN_APPLICATION_ORDER)
    @ApiOperation(value = Loan.V1_LOAN_APPLICATION_ORDER_DESC, notes = Loan.V1_LOAN_APPLICATION_ORDER_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "applicationId", value = "贷款号", paramType = "query"),
    })
    public Response<List<LoanRecordVO>> getOrderRecord(@RequestParam String applicationId) {
        return Response.success(loanApplicationService.getOrderRecord(applicationId));
    }

    @GetMapping(value = Loan.V1_LOAN_APPLICATION_REPAYMENT)
    @ApiOperation(value = Loan.V1_LOAN_APPLICATION_REPAYMENT_DESC, notes = Loan.V1_LOAN_APPLICATION_REPAYMENT_DESC)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "applicationId", value = "贷款号", paramType = "query"),
    })
    public Response<List<RepaymentPlanVO>> getRepaymentPlan(@RequestParam String applicationId) {
        return Response.success(loanApplicationService.getRepaymentPlan(applicationId));
    }

    @GetMapping(value = Loan.V1_OUTSTANDING_LIST)
    @ApiOperation(value = Loan.V1_OUTSTANDING_LIST_DESC, notes = Loan.V1_OUTSTANDING_LIST_DESC)
    public Response<List<LoanOutstandingVO>> getOutstandingList(@RequestParam @NotNull Integer userId) {
        List<LoanOutstandingVO> outstandingList = loanApplicationService.getOutstandingLoanList(userId);
        //设置名单标签信息
        if (CollectionUtils.isNotEmpty(outstandingList)) {
            List<String> aList = outstandingList.stream().filter(item -> StringUtils.isBlank(item.getType()))
                    .map(LoanOutstandingVO::getApplicationId).collect(Collectors.toList());
            Map<String, FaSuVO> faSuMap = faSuService.listFaSuData(aList);
            for (LoanOutstandingVO vo : outstandingList) {
                FaSuVO suVO = faSuMap.get(vo.getApplicationId());
                if (suVO != null) {
                    // 名单类型: 2-法诉
                    vo.setType("2");
                    vo.setCompanyName(suVO.getCompanyName());
                    vo.setCompanyTel(suVO.getCompanyTel());
                    vo.setTimeRange(suVO.getTimeRange());
                }
            }
            for (LoanOutstandingVO vo : outstandingList) {
                if (StringUtils.isNotBlank(vo.getType())) {
                    continue;
                }
                LoanImportLabelVO labelVO = loanTransferService.getImportLabelInfo(vo.getApplicationId());
                if (labelVO != null) {
                    vo.setType(labelVO.getType());
                    vo.setCompanyName(labelVO.getCompanyName());
                    vo.setCompanyTel(labelVO.getCompanyTel());
                    vo.setDeptDate(labelVO.getDeptDate());
                }
            }
        }
        return Response.success(outstandingList);
    }

    @PostMapping(value = Loan.V1_LOAN_ALLOW_EARLY_SETTLE)
    @ApiOperation(value = Loan.V1_LOAN_ALLOW_EARLY_SETTLE_DESC, notes = Loan.V1_LOAN_ALLOW_EARLY_SETTLE_DESC)
    public Response<Void> allowEarlySettle(@Validated @RequestBody OutstandingEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        loanApplicationService.allowEarlySettle(dto.getApplicationId());
        custOperateService.saveOperationHistory(dto, staffVO,
            OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING_ALLOW.getCode());
        earlySettledService.saveEarlySettledRecord(dto, staffVO);
        return Response.success();
    }

    @PostMapping(value = Loan.V1_LOAN_ALLOW_EARLY_SETTLE_BATCH)
    @ApiOperation(value = Loan.V1_LOAN_ALLOW_EARLY_SETTLE_BATCH_DESC, notes = Loan.V1_LOAN_ALLOW_EARLY_SETTLE_BATCH_DESC)
    public Response<Void> allowEarlySettleBatch(@Validated @RequestBody OutstandingEarlySettleBatchDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        List<String> failedList = loanApplicationService.allowEarlySettleBatch(dto.getApplicationId());
        if (CollectionUtils.isEmpty(failedList)) {
            saveOperation(dto, staffVO, OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING_ALLOW.getCode());
        } else {
            failedList.forEach(applicationId -> {
                dto.getApplicationId().remove(applicationId);
            });
            saveOperation(dto, staffVO, OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING_ALLOW.getCode());
            throw new CrmOperateException("有订单开启提前结清失败");
        }
        return Response.success();
    }

    @PostMapping(value = Loan.V1_LOAN_EARLY_SETTLE)
    @ApiOperation(value = Loan.V1_LOAN_EARLY_SETTLE_DESC, notes = Loan.V1_LOAN_EARLY_SETTLE_DESC)
    public Response<Void> earlySettle(@Validated @RequestBody OutstandingEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        loanApplicationService.earlySettle(staffVO.getStaffMobile(), dto.getApplicationId());
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING.getCode());
        return Response.success();
    }

    @PostMapping(value = Loan.V1_LOAN_EARLY_SETTLE_BATCH)
    @ApiOperation(value = Loan.V1_LOAN_EARLY_SETTLE_BATCH_DESC, notes = Loan.V1_LOAN_EARLY_SETTLE_BATCH_DESC)
    public Response<Void> earlySettleBatch(@Validated @RequestBody OutstandingEarlySettleBatchDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        List<String> failedList = loanApplicationService.earlySettleBatch(dto.getUserId(),
            staffVO.getStaffMobile(), dto.getApplicationId());
        if (CollectionUtils.isEmpty(failedList)) {
            saveOperation(dto, staffVO, OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING.getCode());
        } else {
            failedList.forEach(applicationId -> {
                dto.getApplicationId().remove(applicationId);
            });
            saveOperation(dto, staffVO, OperateTypeEnum.LOAN_EARLY_SETTLE_OUTSTANDING.getCode());
            throw new CrmOperateException("有订单开启提前结清失败");
        }
        return Response.success();
    }

    @GetMapping(value = Loan.V1_LOAN_PAYMENTS_DETAILS)
    @ApiOperation(value = Loan.V1_LOAN_PAYMENTS_DETAILS_DESC, notes = Loan.V1_LOAN_PAYMENTS_DETAILS_DESC)
    public Response<List<PaymentsDetailsVO>> getPaymentsDetails(@RequestHeader("X-Mobile") String mobile,
                                                                @RequestParam("userId") @NotNull Integer userId,
                                                                @RequestParam("businessType") @NotNull String loanType) {
        List<PaymentsDetailsVO> voList = loanApplicationService.getPaymentsDetails(userId);
        List<PaymentsDetailsVO> results;
        if ("wallet".equals(loanType)) {
            // 钱包列表过滤
            results = voList.stream().filter(this::filterWalletType).collect(Collectors.toList());
        } else {
            // 现金贷列表过滤
            results = voList.stream().filter(v -> !filterWalletType(v)).collect(Collectors.toList());
            if(externalService.isSpecificGroup(mobile, Constant.KEFU_GROUP)){
                results = results.stream().filter(item -> externalService.isClosedIn3Year(item.getApplicationId())).collect(Collectors.toList());
            }
            if(externalService.isSpecificGroup(mobile, Constant.TOUSU_GROUP)){
                results.forEach(paymentsDetailsVO -> {
                    if(!externalService.isClosedIn3Year(paymentsDetailsVO.getApplicationId())){
                        paymentsDetailsVO.setBackgroundColor("grey");
                    }
                });
            }
        }
        return Response.success(results);
    }

    /**
     * 钱包类型的贷款判断条件: 1.贷款号是以WL开头的 2.当贷款号为空时支付渠道是钱包国民信托渠道(分期)或通联-钱包
     */
    private boolean filterWalletType(PaymentsDetailsVO v) {
        return (StringUtils.isNotBlank(v.getApplicationId()) && v.getApplicationId().startsWith("WL")) ||
                (StringUtils.isBlank(v.getApplicationId()) &&
                        (PayCodeEnum.ALLINPAY_WALLET.getChannelname().equals(v.getChannel()) ||
                                PayCodeEnum.GMTRUST_INSTALLMENT.getChannelname().equals(v.getChannel())));
    }

    private void saveOperation(OutstandingEarlySettleBatchDTO dto, StaffVO staffVO, String operateType) {
        List<CustHisOperate> operates = new ArrayList<>();
        CustHisOperate custHisOperate = null;
        String comment = dto.getComment();
        Integer userId = dto.getUserId();
        String staffId = staffVO.getLoginName();
        String group = staffVO.getGroupCode();
        Date date = new Date();
        for (String applicationId : dto.getApplicationId()) {
            custHisOperate = new CustHisOperate();
            custHisOperate.setComment(comment);
            custHisOperate.setUserId(userId);
            custHisOperate.setStaffId(staffId);
            custHisOperate.setGroupCode(group);
            custHisOperate.setLoanId(applicationId);
            custHisOperate.setOperateType(operateType);
            custHisOperate.setOperateTime(date);
            operates.add(custHisOperate);
        }
        custOperateService.saveOperateHistoryBatch(operates);
    }
}
