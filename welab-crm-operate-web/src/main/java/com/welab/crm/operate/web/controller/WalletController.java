package com.welab.crm.operate.web.controller;

import com.welab.common.response.Response;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.interview.dto.wallet.WalletBillQueryDTO;
import com.welab.crm.interview.dto.wallet.WalletOrderRecordDTO;
import com.welab.crm.interview.dto.wechat.WalletLoanDTO;
import com.welab.crm.interview.service.FaSuService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.WalletService;
import com.welab.crm.interview.vo.fasu.FaSuVO;
import com.welab.crm.interview.vo.loan.LoanOutstandingVO;
import com.welab.crm.interview.vo.wallet.WalletGrantHistoryVO;
import com.welab.crm.interview.vo.wallet.WalletGrantQuotaVO;
import com.welab.crm.interview.vo.wallet.WalletLoanDueInfoVO;
import com.welab.crm.interview.vo.wallet.WalletMonthBillVO;
import com.welab.crm.interview.vo.wallet.WalletOrderRecordVO;
import com.welab.crm.interview.vo.wallet.WalletOutstandingVO;
import com.welab.crm.interview.vo.wallet.WalletRepayRecordVO;
import com.welab.crm.operate.domain.CustHisOperate;
import com.welab.crm.operate.dto.operate.HistoryOperationDTO;
import com.welab.crm.operate.dto.wallet.WalletAllowBatchDTO;
import com.welab.crm.operate.dto.wallet.WalletEarlySettleDTO;
import com.welab.crm.operate.enums.OperateTypeEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.CustOperateService;
import com.welab.crm.operate.service.EarlySettledService;
import com.welab.crm.operate.service.LoanTransferService;
import com.welab.crm.operate.vo.loan.LoanImportLabelVO;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.Wallet;
import com.welab.support.credit.dto.cs.GetGrantHistoryReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @version v1.0
 * @title
 * @description
 * @date 2021/10/29
 * @module 客服项目
 */
@RestController
@Api(description = "钱包服务")
@RequestMapping(Wallet.ROOT)
public class WalletController {

    @Resource
    private WalletService walletService;

    @Resource
    private CustOperateService custOperateService;

    @Resource
    private LoanApplicationService loanApplicationService;
    
    @Resource
    private LoanTransferService loanTransferService;

    @Resource
    private FaSuService faSuService;

    @Resource
    private EarlySettledService earlySettledService;

    @GetMapping(value = Wallet.V1_WALLET_QUOTA_INFO_LIST)
    @ApiOperation(value = Wallet.V1_WALLET_QUOTA_INFO_LIST_DESC, notes = Wallet.V1_WALLET_QUOTA_INFO_LIST_DESC)
    public Response<List<WalletGrantQuotaVO>> getQuotaInfoList(@RequestParam Long userId) {
        return Response.success(walletService.getGrantQuotaInfo(userId));
    }

    @GetMapping(value = Wallet.V1_WALLET_QUOTA_HISTORY)
    @ApiOperation(value = Wallet.V1_WALLET_QUOTA_HISTORY_DESC, notes = Wallet.V1_WALLET_QUOTA_HISTORY_DESC)
    public Response<List<WalletGrantHistoryVO>> getQuotaHistory(@ModelAttribute GetGrantHistoryReq req) {
        return Response.success(walletService.getGrantQuotaHistory(req));
    }

    @GetMapping(value = Wallet.V1_WALLET_LOAN_DUE)
    @ApiOperation(value = Wallet.V1_WALLET_LOAN_DUE_DESC, notes = Wallet.V1_WALLET_LOAN_DUE_DESC)
    public Response<WalletLoanDueInfoVO> getLoanDue(@RequestParam Integer userId, @RequestParam String month) {
        return Response.success(walletService.getWalletLoanDue(userId, month));
    }

    @GetMapping(value = Wallet.V1_WALLET_MONTH_BILL)
    @ApiOperation(value = Wallet.V1_WALLET_MONTH_BILL_DESC, notes = Wallet.V1_WALLET_MONTH_BILL_DESC)
    public Response<List<WalletMonthBillVO>> getMonthBill(@ModelAttribute WalletBillQueryDTO queryDTO) {
        return Response.success(walletService.getMonthBill(queryDTO));
    }

    @GetMapping(value = Wallet.V1_WALLET_ORDER_RECORD)
    @ApiOperation(value = Wallet.V1_WALLET_ORDER_RECORD_DESC, notes = Wallet.V1_WALLET_ORDER_RECORD_DESC)
    public Response<List<WalletOrderRecordVO>> getOrderRecords(@ModelAttribute WalletOrderRecordDTO dto) {
        return Response.success(walletService.getWalletOrderRecords(dto));
    }

    @GetMapping(value = Wallet.V1_WALLET_REPAY_RECORD)
    @ApiOperation(value = Wallet.V1_WALLET_REPAY_RECORD_DESC, notes = Wallet.V1_WALLET_REPAY_RECORD_DESC)
    public Response<List<WalletRepayRecordVO>> getRepayRecords(@RequestParam @NotNull Long userId) {
        return Response.success(walletService.getWalletRepayRecords(userId));
    }

    @PostMapping(value = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE)
    @ApiOperation(value = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE_DESC, notes = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE_DESC)
    public Response<Void> allowEarlySettle(@Validated @RequestBody WalletEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        if (!walletService.updateLoanYBStatus(dto.getApplicationId())) {
            throw new CrmOperateException("开启提前结清失败");
        }
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.WALLET_EARLY_SETTLE_ALLOW.getCode());
        earlySettledService.saveEarlySettledRecord(dto,staffVO);
        return Response.success();
    }

    @PostMapping(value = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE_BATCH)
    @ApiOperation(value = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE_BATCH_DESC, notes = Wallet.V1_WALLET_ALLOW_EARLY_SETTLE_BATCH_DESC)
    public Response<Void> allowEarlySettleBatch(@Validated @RequestBody WalletAllowBatchDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        List<WalletEarlySettleDTO> dtoList = dto.getApplicationIdList();
        List<String> appList = dtoList.stream().map(HistoryOperationDTO::getApplicationId).collect(Collectors.toList());
        List<String> failedList = walletService.allowEarlyRepayBatch(appList);
        if (CollectionUtils.isEmpty(failedList)) {
            saveOperation(dto, staffVO, OperateTypeEnum.WALLET_EARLY_SETTLE_ALLOW.getCode());
            for (WalletEarlySettleDTO settleDTO : dtoList) {
                earlySettledService.saveEarlySettledRecord(settleDTO, staffVO);
            }
        } else {
            failedList.forEach(failedAppNo -> dtoList.removeIf(settleDTO -> failedAppNo.equals(settleDTO.getApplicationId())));
            saveOperation(dto, staffVO, OperateTypeEnum.WALLET_EARLY_SETTLE_ALLOW.getCode());
            for (WalletEarlySettleDTO settleDTO : dtoList) {
                earlySettledService.saveEarlySettledRecord(settleDTO, staffVO);
            }
            throw new CrmOperateException("有订单开启提前结清失败");
        }
        return Response.success();
    }

    @PostMapping(value = Wallet.V1_WALLET_EARLY_SETTLE)
    @ApiOperation(value = Wallet.V1_WALLET_EARLY_SETTLE_DESC, notes = Wallet.V1_WALLET_EARLY_SETTLE_DESC)
    public Response<Void> earlySettle(@RequestBody @Validated WalletEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        walletService.earlyRepay(dto.getUserId().longValue(), dto.getApplicationId());
        custOperateService.saveOperationHistory(dto, staffVO, OperateTypeEnum.WALLET_EARLY_SETTLE.getCode());
        return Response.success();
    }

    @PostMapping(value = Wallet.V1_WALLET_EARLY_SETTLE_BATCH)
    @ApiOperation(value = Wallet.V1_WALLET_EARLY_SETTLE_BATCH_DESC, notes = Wallet.V1_WALLET_EARLY_SETTLE_BATCH_DESC)
    public Response<Void> earlySettleBatch(@Validated @RequestBody WalletAllowBatchDTO dto,
                                                @ApiIgnore @GetStaff StaffVO staffVO) {
        List<String> applicationIdList =
            dto.getApplicationIdList().stream().map(HistoryOperationDTO::getApplicationId).collect(Collectors.toList());
        for (String applicationId : applicationIdList) {
            walletService.earlyRepay(dto.getUserId().longValue(), applicationId);
        }
        saveOperation(dto, staffVO, OperateTypeEnum.WALLET_EARLY_SETTLE_BATCH.getCode());
        return Response.success();
    }

    @GetMapping(value = Wallet.V1_WALLET_OUTSTANDING_LIST)
    @ApiOperation(value = Wallet.V1_WALLET_OUTSTANDING_LIST_DESC, notes = Wallet.V1_WALLET_OUTSTANDING_LIST_DESC)
    public Response<List<WalletOutstandingVO>> getOutstandingList(@RequestParam @NotNull Integer userId) {
        List<WalletOutstandingVO> outstandingList = walletService.getOutstandingLoanList(userId);

        if (CollectionUtils.isNotEmpty(outstandingList)) {
            List<String> aList = outstandingList.stream().filter(item -> StringUtils.isBlank(item.getType()))
                    .map(WalletOutstandingVO::getApplicationId).collect(Collectors.toList());
            Map<String, FaSuVO> faSuMap = faSuService.listFaSuData(aList);
            for (WalletOutstandingVO vo : outstandingList) {
                FaSuVO suVO = faSuMap.get(vo.getApplicationId());
                if (suVO != null) {
                    // 名单类型: 2-法诉
                    vo.setType("2");
                    vo.setCompanyName(suVO.getCompanyName());
                    vo.setCompanyTel(suVO.getCompanyTel());
                    vo.setTimeRange(suVO.getTimeRange());
                }
            }

            //设置名单标签信息
            for (WalletOutstandingVO vo : outstandingList) {
                if (StringUtils.isNotBlank(vo.getType())) {
                    continue;
                }
                LoanImportLabelVO labelVO = loanTransferService.getImportLabelInfo(vo.getApplicationId());
                if (labelVO != null) {
                    vo.setType(labelVO.getType());
                    vo.setCompanyName(labelVO.getCompanyName());
                    vo.setCompanyTel(labelVO.getCompanyTel());
                    vo.setDeptDate(labelVO.getDeptDate());
                }
            }

        }
        return Response.success(outstandingList);
    }

    @PostMapping(value = Wallet.V1_WALLET_OUTSTANDING_ALLOW_EARLY_SETTLE)
    @ApiOperation(value = Wallet.V1_WALLET_OUTSTANDING_ALLOW_EARLY_SETTLE_DESC, notes = Wallet.V1_WALLET_OUTSTANDING_ALLOW_EARLY_SETTLE_DESC)
    public Response<Void> allowEarlySettleOutstanding(@Validated @RequestBody WalletEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        loanApplicationService.allowEarlySettle(dto.getApplicationId());
        custOperateService.saveOperationHistory(dto, staffVO,
            OperateTypeEnum.WALLET_EARLY_SETTLE_OUTSTANDING_ALLOW.getCode());
        return Response.success();
    }

    @PostMapping(value = Wallet.V1_WALLET_OUTSTANDING_EARLY_SETTLE)
    @ApiOperation(value = Wallet.V1_WALLET_OUTSTANDING_EARLY_SETTLE_DESC, notes = Wallet.V1_WALLET_OUTSTANDING_EARLY_SETTLE_DESC)
    public Response<Void> earlySettleOutstanding(@Validated @RequestBody WalletEarlySettleDTO dto,
        @ApiIgnore @GetStaff StaffVO staffVO) {
        loanApplicationService.earlySettle(staffVO.getStaffMobile(), dto.getApplicationId());
        custOperateService.saveOperationHistory(dto, staffVO,
            OperateTypeEnum.WALLET_EARLY_SETTLE_OUTSTANDING.getCode());
        return Response.success();
    }

    @GetMapping(value = Wallet.V1_WALLET_LOAN_DETAIL)
    @ApiOperation(value = Wallet.V1_WALLET_LOAN_DETAIL_DESC, notes = Wallet.V1_WALLET_LOAN_DETAIL_DESC)
    public Response<WalletLoanDTO> earlySettleOutstanding(@RequestParam String applicationId) {
        return Response.success(walletService.getWalletRepayPlan(applicationId));
    }

    private void saveOperation(WalletAllowBatchDTO dto, StaffVO staffVO, String type) {
        List<CustHisOperate> operates = new ArrayList<>();
        String comment = dto.getComment();
        Integer userId = dto.getUserId();
        String staffId = staffVO.getLoginName();
        String group = staffVO.getGroupCode();
        Date date = new Date();
        CustHisOperate custHisOperate = null;
        for (WalletEarlySettleDTO settleDTO : dto.getApplicationIdList()) {
            custHisOperate = new CustHisOperate();
            custHisOperate.setComment(dto.getReason() + comment);
            custHisOperate.setUserId(userId);
            custHisOperate.setStaffId(staffId);
            custHisOperate.setGroupCode(group);
            custHisOperate.setLoanId(settleDTO.getApplicationId());
            custHisOperate.setOperateType(type);
            custHisOperate.setOperateTime(date);
            operates.add(custHisOperate);
        }
        custOperateService.saveOperateHistoryBatch(operates);
    }
}
