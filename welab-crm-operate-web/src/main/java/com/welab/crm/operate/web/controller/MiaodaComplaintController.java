package com.welab.crm.operate.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.crm.operate.dto.BatchInfoReqDTO;
import com.welab.crm.operate.dto.miaoda.*;
import com.welab.crm.operate.service.ComplaintTypeMappingService;
import com.welab.crm.operate.service.MiaodaApiService;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.miaoda.ComplaintTypeMappingVO;
import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;
import com.welab.crm.operate.web.common.ResponseUtil;
import com.welab.crm.operate.web.constants.Urls;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 喵达投诉管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@RestController
@RequestMapping(Urls.MiaodaComplaint.V1_MIAODA)
@Api(tags = "喵达投诉管理")
@Validated
public class MiaodaComplaintController {

    @Resource
    private MiaodaApiService miaodaApiService;

    @Resource
    private ComplaintTypeMappingService complaintTypeMappingService;

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_LIST_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_LIST_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_COMPLAINT_LIST)
    public Response<List<MiaodaComplaintVO>> queryComplaintList(@Valid MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("查询喵达投诉单列表，参数：{}", queryDTO);
            List<MiaodaComplaintVO> result = miaodaApiService.queryComplaintDetails(queryDTO);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询投诉单列表失败", e);
            return ResponseUtil.error("查询投诉单列表失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询投诉单列表", notes = "根据条件分页查询喵达投诉单列表，支持分页参数")
    @GetMapping(Urls.MiaodaComplaint.V1_COMPLAINT_LIST + "/page")
    public Response<Page<MiaodaComplaintVO>> queryComplaintListByPage(@Valid MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("分页查询喵达投诉单列表，参数：{}", queryDTO);
            Page<MiaodaComplaintVO> result = miaodaApiService.queryComplaintDetailsByPage(queryDTO);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("分页查询投诉单列表失败", e);
            return ResponseUtil.error("分页查询投诉单列表失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "查询本地投诉单数据",
                  notes = "直接查询本地数据库中的投诉单数据，不调用喵达API，提供快速查询能力。" +
                         "注意：起始时间(st)和结束时间(et)为必填参数，时间范围不能超过31天，格式：yyyy-MM-dd HH:mm:ss")
    @GetMapping(Urls.MiaodaComplaint.V1_COMPLAINT_LIST + "/local")
    public Response<Page<MiaodaComplaintVO>> queryLocalComplaints(@Valid MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("查询本地投诉单数据，参数：{}", queryDTO);
            Page<MiaodaComplaintVO> result = miaodaApiService.queryLocalComplaints(queryDTO);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询本地投诉单数据失败", e);
            return ResponseUtil.error("查询本地投诉单数据失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_REPLY_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_REPLY_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_REPLY)
    public Response<Void> replyComplaint(@Valid @RequestBody MiaodaReplyDTO replyDTO, HttpServletRequest request) {
        try {
            log.info("回复投诉单，参数：{}", replyDTO);
            boolean success = miaodaApiService.replyComplaint(replyDTO);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("回复投诉单失败");
            }
        } catch (Exception e) {
            log.error("回复投诉单失败", e);
            return ResponseUtil.error("回复投诉单失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL)
    public Response<Void> appealComplaint(@Valid @RequestBody MiaodaAppealDTO appealDTO, HttpServletRequest request) {
        try {
            log.info("申诉投诉单，参数：{}", appealDTO);
            boolean success = miaodaApiService.appealComplaint(appealDTO);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("申诉投诉单失败");
            }
        } catch (Exception e) {
            log.error("申诉投诉单失败", e);
            return ResponseUtil.error("申诉投诉单失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL_STATUS_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL_STATUS_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_COMPLAINT_APPEAL_STATUS)
    public Response<List<String>> queryAppealStatus(@RequestParam List<String> sns) {
        try {
            log.info("查询申诉状态，投诉单号：{}", sns);
            List<String> result = miaodaApiService.queryAppealStatus(sns);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询申诉状态失败", e);
            return ResponseUtil.error("查询申诉状态失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_COMPLETE_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_COMPLETE_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_COMPLETE)
    public Response<Void> completeComplaint(@Valid @RequestBody MiaodaCompleteDTO completeDTO, HttpServletRequest request) {
        try {
            log.info("申请结案，参数：{}", completeDTO);
            boolean success = miaodaApiService.completeComplaint(completeDTO);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("申请结案失败");
            }
        } catch (Exception e) {
            log.error("申请结案失败", e);
            return ResponseUtil.error("申请结案失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_CLAIM_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_CLAIM_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_CLAIM)
    public Response<Void> claimComplaint(@RequestParam String sn,
                                        @RequestParam String content,
                                        @RequestParam(required = false) List<String> images,
                                        @RequestParam(required = false) List<String> videos,
                                        HttpServletRequest request) {
        try {
            log.info("投诉认领，投诉单号：{}", sn);
            boolean success = miaodaApiService.claimComplaint(sn, content, images, videos);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("投诉认领失败");
            }
        } catch (Exception e) {
            log.error("投诉认领失败", e);
            return ResponseUtil.error("投诉认领失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_SYNC_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_SYNC_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_SYNC)
    public Response<Integer> syncComplaints(@RequestParam String startTime,
                                           @RequestParam String endTime,
                                           HttpServletRequest request) {
        try {
            log.info("手动同步投诉单，时间范围：{} - {}", startTime, endTime);
            int count = miaodaApiService.syncComplaintsToLocal(startTime, endTime);
            return ResponseUtil.success(count);
        } catch (Exception e) {
            log.error("手动同步投诉单失败", e);
            return ResponseUtil.error("手动同步投诉单失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_STATUS_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_STATUS_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_STATUS)
    public Response<Integer> updateComplaintStatus(HttpServletRequest request) {
        try {
            log.info("更新投诉单状态");
            int count = miaodaApiService.checkAndUpdateComplaintStatus();
            return ResponseUtil.success(count);
        } catch (Exception e) {
            log.error("更新投诉单状态失败", e);
            return ResponseUtil.error("更新投诉单状态失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_BY_SNS_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_BY_SNS_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_COMPLAINT_UPDATE_BY_SNS)
    public Response<List<MiaodaComplaintVO>> updateComplaintBySns(@RequestParam List<String> sns) {
        try {
            log.info("根据投诉单号更新投诉单状态，投诉单号：{}", sns);
            List<MiaodaComplaintVO> resultList = miaodaApiService.updateComplaintBySns(sns);
            return ResponseUtil.success(resultList);
        } catch (Exception e) {
            log.error("根据投诉单号更新投诉单状态失败", e);
            return ResponseUtil.error("根据投诉单号更新投诉单状态失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_COMPLAINT_QUERY_BY_WORK_ORDER_NO_DESC, notes = Urls.MiaodaComplaint.V1_COMPLAINT_QUERY_BY_WORK_ORDER_NO_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_COMPLAINT_QUERY_BY_WORK_ORDER_NO)
    public Response<List<MiaodaComplaintVO>> queryComplaintByWorkOrderNo(@RequestParam String workOrderNo) {
        try {
            log.info("根据工单编号查询投诉单，工单编号：{}", workOrderNo);
            List<MiaodaComplaintVO> resultList = miaodaApiService.queryComplaintsByWorkOrderNo(workOrderNo);
            return ResponseUtil.success(resultList);
        } catch (Exception e) {
            log.error("根据工单编号查询投诉单失败", e);
            return ResponseUtil.error("根据工单编号查询投诉单失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_RAPID_SOLVE_DESC, notes = Urls.MiaodaComplaint.V1_RAPID_SOLVE_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_RAPID_SOLVE)
    public Response<String> queryRapidSolveInfo(@RequestParam(required = false) String month) {
        try {
            log.info("查询快速解决商家信息，月份：{}", month);
            String result = miaodaApiService.queryRapidSolveInfo(month);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询快速解决商家信息失败", e);
            return ResponseUtil.error("查询快速解决商家信息失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_PLAN_INFO_DESC, notes = Urls.MiaodaComplaint.V1_PLAN_INFO_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_PLAN_INFO)
    public Response<String> queryPlanInfo() {
        try {
            log.info("查询套餐余量信息");
            String result = miaodaApiService.queryPlanInfo();
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询套餐余量信息失败", e);
            return ResponseUtil.error("查询套餐余量信息失败：" + e.getMessage());
        }
    }

    // ==================== 投诉类型映射管理 ====================

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_LIST_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_LIST_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_MAPPING_LIST)
    public Response<Page<ComplaintTypeMappingVO>> queryMappingList(@Valid ComplaintTypeMappingQueryDTO queryDTO) {
        try {
            log.info("分页查询投诉类型映射，参数：{}", queryDTO);
            Page<ComplaintTypeMappingVO> result = complaintTypeMappingService.queryMappingPage(queryDTO);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("分页查询投诉类型映射失败", e);
            return ResponseUtil.error("查询投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_GET_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_GET_DESC)
    @GetMapping(Urls.MiaodaComplaint.V1_MAPPING_GET)
    public Response<ComplaintTypeMappingVO> getMappingById(@PathVariable Long id) {
        try {
            log.info("根据ID查询投诉类型映射，ID：{}", id);
            ComplaintTypeMappingVO result = complaintTypeMappingService.getMappingById(id);
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("根据ID查询投诉类型映射失败", e);
            return ResponseUtil.error("查询投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_SAVE_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_SAVE_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_MAPPING_SAVE)
    public Response<Void> saveMapping(@Valid @RequestBody ComplaintTypeMappingDTO mappingDTO, HttpServletRequest request) {
        try {
            log.info("保存投诉类型映射，参数：{}", mappingDTO);
            String operator = getCurrentUser(request);
            boolean success = complaintTypeMappingService.saveMapping(mappingDTO, operator);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("保存投诉类型映射失败");
            }
        } catch (Exception e) {
            log.error("保存投诉类型映射失败", e);
            return ResponseUtil.error("保存投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_UPDATE_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_UPDATE_DESC)
    @PutMapping(Urls.MiaodaComplaint.V1_MAPPING_UPDATE)
    public Response<Void> updateMapping(@Valid @RequestBody ComplaintTypeMappingDTO mappingDTO, HttpServletRequest request) {
        try {
            log.info("更新投诉类型映射，参数：{}", mappingDTO);
            String operator = getCurrentUser(request);
            boolean success = complaintTypeMappingService.updateMapping(mappingDTO, operator);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("更新投诉类型映射失败");
            }
        } catch (Exception e) {
            log.error("更新投诉类型映射失败", e);
            return ResponseUtil.error("更新投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_DELETE_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_DELETE_DESC)
    @DeleteMapping(Urls.MiaodaComplaint.V1_MAPPING_DELETE)
    public Response<Void> deleteMapping(@PathVariable Long id, HttpServletRequest request) {
        try {
            log.info("删除投诉类型映射，ID：{}", id);
            boolean success = complaintTypeMappingService.deleteMapping(id);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("删除投诉类型映射失败");
            }
        } catch (Exception e) {
            log.error("删除投诉类型映射失败", e);
            return ResponseUtil.error("删除投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_BATCH_DELETE_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_BATCH_DELETE_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_MAPPING_BATCH_DELETE)
    public Response<Void> batchDeleteMapping(@RequestBody BatchInfoReqDTO dto, HttpServletRequest request) {
        try {
            log.info("批量删除投诉类型映射，IDs：{}", dto.getIds());
            boolean success = complaintTypeMappingService.batchDeleteMapping(dto.getIds());
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("批量删除投诉类型映射失败");
            }
        } catch (Exception e) {
            log.error("批量删除投诉类型映射失败", e);
            return ResponseUtil.error("批量删除投诉类型映射失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_TOGGLE_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_TOGGLE_DESC)
    @PutMapping(Urls.MiaodaComplaint.V1_MAPPING_TOGGLE)
    public Response<Void> toggleMappingStatus(@PathVariable Long id,
                                             @RequestParam Integer isActive,
                                             HttpServletRequest request) {
        try {
            log.info("切换投诉类型映射状态，ID：{}，状态：{}", id, isActive);
            String operator = getCurrentUser(request);
            boolean success = complaintTypeMappingService.toggleMappingStatus(id, isActive, operator);
            if (success) {
                return ResponseUtil.success();
            } else {
                return ResponseUtil.error("切换投诉类型映射状态失败");
            }
        } catch (Exception e) {
            log.error("切换投诉类型映射状态失败", e);
            return ResponseUtil.error("切换投诉类型映射状态失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = Urls.MiaodaComplaint.V1_MAPPING_INIT_DESC, notes = Urls.MiaodaComplaint.V1_MAPPING_INIT_DESC)
    @PostMapping(Urls.MiaodaComplaint.V1_MAPPING_INIT)
    public Response<Integer> initDefaultMappings(HttpServletRequest request) {
        try {
            log.info("初始化默认映射配置");
            String operator = getCurrentUser(request);
            int count = complaintTypeMappingService.initDefaultMappings(operator);
            return ResponseUtil.success(count);
        } catch (Exception e) {
            log.error("初始化默认映射配置失败", e);
            return ResponseUtil.error("初始化默认映射配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser(HttpServletRequest request) {
        return CommonUtils.getCurrentlogged();
    }
}
