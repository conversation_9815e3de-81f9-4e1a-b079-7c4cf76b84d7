package com.welab.crm.operate.web.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.util.Date;

/**
 * Date类型的JSON序列化配置
 * 将Date对象序列化为时间戳（毫秒），反序列化时将时间戳转换为Date对象
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@JsonComponent
public class DateSerializationConfig {

    /**
     * Date序列化器 - 将Date转换为时间戳
     */
    public static class DateSerializer extends JsonSerializer<Date> {
        @Override
        public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) 
                throws IOException {
            if (date != null) {
                jsonGenerator.writeNumber(date.getTime());
            } else {
                jsonGenerator.writeNull();
            }
        }
    }

    /**
     * Date反序列化器 - 将时间戳转换为Date
     */
    public static class DateDeserializer extends JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) 
                throws IOException {
            long timestamp = jsonParser.getValueAsLong();
            return timestamp > 0 ? new Date(timestamp) : null;
        }
    }
}
