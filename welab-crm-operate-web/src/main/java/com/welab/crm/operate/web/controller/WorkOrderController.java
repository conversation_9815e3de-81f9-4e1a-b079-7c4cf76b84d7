package com.welab.crm.operate.web.controller;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.common.response.Response;
import com.welab.common.response.enums.ResponsCodeTypeEnum;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigNodeDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunTaskDTO;
import com.welab.crm.interview.dto.FileDTO;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.operate.annotation.AesDecrypt;
import com.welab.crm.operate.anotation.NoRepeatSubmit;
import com.welab.crm.operate.dto.InitCodeConfigDTO;
import com.welab.crm.operate.dto.workorder.*;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.service.MiaodaApiService;
import com.welab.crm.operate.service.WorkOrderService;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.CommonUtils;
import com.welab.crm.operate.vo.online.OnlineFileRetVO;
import com.welab.crm.operate.vo.workorder.*;
import com.welab.crm.operate.web.annotation.GetStaff;
import com.welab.crm.operate.web.constants.Urls.WorkOrder;
import com.welab.crm.operate.web.util.ResponseUtil;
import com.welab.domain.vo.ResponseVo;
import com.welab.exception.FastRuntimeException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.BeanParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title
 * @description
 * @date 2021/9/28
 */
@RestController
@Api(description = "工单服务")
@RequestMapping(WorkOrder.ROOT)
@Slf4j
public class WorkOrderController extends BaseController{

    @Resource
    private WorkOrderService workOrderService;
    @Resource
	private IUploadService uploadService;
	@Resource
	private MiaodaApiService miaodaApiService;



    /**
     * 进入流程任务发起页面
     *
     * @param reqDTO
	 * @param staffVO
	 * @return
     */
    @PostMapping(WorkOrder.V1_WORKORDER_INITNODE)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_INITNODE_DESC, notes = WorkOrder.V1_WORKORDER_INITNODE_DESC)
    public ResponseVo<Object> getInitNodeConfig(@Validated @RequestBody InitCodeConfigDTO reqDTO,
		@ApiIgnore @GetStaff StaffVO staffVO) {
    	try {
			//提交工单
    		WFConfigNodeDTO wFConfigNodeDTO = workOrderService.getInitNodeConfig(reqDTO, staffVO);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), wFConfigNodeDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }
    
    /**
     * 进入流程任务处理页
     *
     * @param reqDTO
	 * @return
     */
    @PostMapping(WorkOrder.V1_WORKORDER_TASKNODE)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_TASKNODE_DESC, notes = WorkOrder.V1_WORKORDER_TASKNODE_DESC)
    public ResponseVo<Object> getTaskNodeInfo(@Validated @RequestBody WFRunTaskDTO reqDTO) {
    	try {
			//提交工单
    		WFRunExecutionDTO wFRunExecutionDTO = workOrderService.getTaskNodeInfo(reqDTO);
			if (wFRunExecutionDTO == null) {
				return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), "无数据", null);
			}
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), wFRunExecutionDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }
    
    /**
     * 获取目标任务的指派信息
     *
     * @param reqDTO
	 * @return
     */
    @PostMapping(WorkOrder.V1_WORKORDER_ASSIGNEDSTAFFID)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_ASSIGNEDSTAFFID_DESC, notes = WorkOrder.V1_WORKORDER_ASSIGNEDSTAFFID_DESC)
    public ResponseVo<Object> getAssignedStaffId(@Validated @RequestBody AssignedStaffIdDTO reqDTO) {
    	try {
			//提交工单
    		WFConfigAssignedDTO wFConfigAssignedDTO = workOrderService.getAssignedStaffId(reqDTO);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), wFConfigAssignedDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }
    
    /**
     * 查询工单列表
     *
     * @return
     */
    @PostMapping(value = WorkOrder.V1_WORKORDER_SEARCH)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_SEARCH_DESC, notes = WorkOrder.V1_WORKORDER_SEARCH_DESC)
    public ResponseVo<?> queryWorkOrderList(@Validated @RequestBody WorkOrderSearchReqDTO reqDTO) {
    	Page<WorkOrderInfoVO> page = null;
		try {
			page = workOrderService.queryWorkOrderList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<Page<WorkOrderInfoVO>>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(page);
    }
    
    /**
     * 统计工单数量
     *
     * @return
     */
    @PostMapping(value = WorkOrder.V1_WORKORDER_TOTAL)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_TOTAL_DESC, notes = WorkOrder.V1_WORKORDER_TOTAL_DESC)
    public ResponseVo<?> totalWorkOrder(@Validated @RequestBody WorkOrderTotalReqDTO reqDTO) {
    	WorkOrderTotalVO vo = null;
		try {
			vo = workOrderService.totalWorkOrder(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<WorkOrderTotalVO>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(vo);
    }

    /**
     * 提交工单
     *
     * @param reqDTO
	 * @param staffVO
	 * @return
     */
    @PostMapping(WorkOrder.V1_WORKORDER_SUBMIT)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_SUBMIT_DESC, notes = WorkOrder.V1_WORKORDER_SUBMIT_DESC)
	@AesDecrypt
    public ResponseVo<Object> submitWorkOrder(@Validated @RequestBody WorkOrderSubmitReqDTO reqDTO,
		@ApiIgnore @GetStaff StaffVO staffVO) {
		WFConfigAssignedDTO assignedDTO = reqDTO.getAssignedDTO();
		if (Boolean.TRUE.equals(assignedDTO.getIsRequired()) && StringUtils.isBlank(assignedDTO.getStaffId())) {
			throw new CrmOperateException("必须手动指派一名员工");
		}

		if (StringUtils.isBlank(reqDTO.getDescription())){
			throw new CrmOperateException("反馈内容不能为空");
		}
    	try {

    		//  工单判重
			workOrderService.isCustSameOrder(reqDTO);
			// 提交工单
			workOrderService.submitWorkOrder(reqDTO, staffVO);
			// 保存可能存在的客户投诉
			workOrderService.saveWorkOrderComplain(reqDTO, CommonUtils.getCurrentloggedStaffId(),
					CommonUtils.getCurrentloggedName());
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }

	/**
	 * 工单投诉批量处理状态催促接口
	 */
	@PostMapping(WorkOrder.V1_WORKORDER_COMPLAIN_URGE)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_COMPLAIN_URGE_DESC, notes = WorkOrder.V1_WORKORDER_COMPLAIN_URGE_DESC)
	public ResponseVo<Void> workOrderComplainUrge(@Validated @RequestBody WorkOrderCollectionUrgeDTO reqDTO) {
		workOrderService.updateComplainUrgeStatus(reqDTO.getOrderNoList());
		return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
	}

	/**
	 * 工单涉及到的推送到催收投诉历史数据查询
	 */
	@GetMapping(WorkOrder.V1_WORKORDER_COMPLAIN)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_COMPLAIN_DESC, notes = WorkOrder.V1_WORKORDER_COMPLAIN_DESC)
	public ResponseVo<Object> queryWorkOrderComplain(@BeanParam WorkOrderTypeReqDTO reqDTO) {
		String orderNo = reqDTO.getCode();
		if (StringUtils.isBlank(orderNo)) {
			throw new CrmOperateException("工单编号不能为空");
		}
		List<WorkOrderComplainVO> complains = workOrderService.getWorkOrderComplains(orderNo);
		return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), complains);
	}
    
    /**
     * 处理工单
     *
     * @param reqDTO
	 * @return
     */
    @PostMapping(WorkOrder.V1_WORKORDER_EXECUTE)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_EXECUTE_DESC, notes = WorkOrder.V1_WORKORDER_EXECUTE_DESC)
	@AesDecrypt
    public ResponseVo<Object> executeWorkOrder(@Validated @RequestBody WorkOrderSubmitReqDTO reqDTO,
		@ApiIgnore @GetStaff StaffVO staffVO) {
    	try {

			// 结案判断
			miaodaApiService.handlerCompleteCheck(reqDTO);
			//处理工单
			workOrderService.executeWorkOrder(reqDTO, staffVO);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
            log.warn("executeWorkOrder,", e);
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }

    /**
     * 保存工单
     */
    @PostMapping(WorkOrder.V1_WORKORDER_SAVE)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_SAVE_DESC, notes = WorkOrder.V1_WORKORDER_SAVE_DESC)
	@AesDecrypt
    public ResponseVo<Object> saveWorkOrder(@RequestBody @Validated WorkOrderSubmitReqDTO reqDTO,
		@ApiIgnore @GetStaff StaffVO staffVO) {
    	try {
			//保存工单
    		workOrderService.saveWorkOrder(reqDTO, staffVO);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }
    
    /**
     * 未分配工单个数查询
     *
     * @return
     */
    @PostMapping(value = WorkOrder.V1_WORKORDER_COUNT)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_COUNT_DESC, notes = WorkOrder.V1_WORKORDER_COUNT_DESC)
    public ResponseVo<?> queryWorkOrderCountList(@Validated @RequestBody WorkOrderSearchReqDTO reqDTO) {
    	List<WorkOrderCountVO> page = null;
		try {
			page = workOrderService.queryWorkCountList(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<Page<WorkOrderInfoVO>>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>(page);
    }
    
    /**
     * 工单详情
     *
     * @return
     */
	@PostMapping(value = WorkOrder.V1_WORKORDER_DETAIL)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_DETAIL_DESC, notes = WorkOrder.V1_WORKORDER_DETAIL_DESC)
	public Response<WorkOrderDetailVO> queryWorkOrderDetail(@Validated @RequestBody WorkOrderSearchReqDTO dto) {
		if(Objects.isNull(dto.getId())){
			throw new CrmOperateException("工单id不能为空");
		}
		if(StringUtils.isBlank(dto.getExecutionId())){
			throw new CrmOperateException("executionId不能为空");
		}
		if(StringUtils.isBlank(dto.getStaffId())){
			throw new CrmOperateException("staffId不能为空");
		}
		return Response.success(workOrderService.queryWorkOrderDetail(dto.getId(), dto.getExecutionId(),
			dto.getStaffId()));
	}


	/**
	 * 过河兵工单详情
	 *
	 * @return
	 */
	@PostMapping(value = WorkOrder.V1_ELITE_WORKORDER_DETAIL)
	@ApiOperation(value = WorkOrder.V1_ELITE_WORKORDER_DETAIL_DESC, notes = WorkOrder.V1_ELITE_WORKORDER_DETAIL_DESC)
	public Response<WorkOrderDetailVO> queryEliteWorkOrderDetail(@Validated @RequestBody WorkOrderSearchReqDTO dto) {
		try {
			if (Objects.isNull(dto.getId())) {
				throw new CrmOperateException("工单id不能为空");
			}
			return Response.success(workOrderService.queryEliteWorkOrderDetail(dto.getId()));
		} catch (Exception e){
			return new Response<>(ResponsCodeTypeEnum.FAILURE.getCode(), e.getMessage(), null);
		}
	}
    
    /**
     * 工单标记
     *
     * @return
     */
    @PostMapping(value = WorkOrder.V1_WORKORDER_SIGN)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_SIGN_DESC, notes = WorkOrder.V1_WORKORDER_SIGN_DESC)
    public ResponseVo<Object> signWorkOrder(@Validated @RequestBody WorkOrderSignReqDTO reqDTO) {
		try {
			workOrderService.signWorkOrder(reqDTO);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<Object>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
		return new ResponseVo<>();
    }
    
	@GetMapping(value = WorkOrder.V1_WORKORDER_HISTORY)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_HISTORY_DESC, notes = WorkOrder.V1_WORKORDER_HISTORY_DESC)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "orderNo", value = "工单编号，客户id和工单编号只需填一个", paramType = "query"),
		@ApiImplicitParam(name = "processCode", value = "流程定义编号", paramType = "query"),
		@ApiImplicitParam(name = "mobile", value = "手机号", paramType = "query"),
		@ApiImplicitParam(name = "uuid", value = "uuid", paramType = "query")
	})
	public Response<List<WorkOrderHisVO>> getWorkOrderHistory(
		@RequestParam(required = false) String uuid,
		@RequestParam(required = false) String orderNo,
		@RequestParam(required = false) String mobile,
		@RequestParam String processCode) {
		return Response.success(workOrderService.queryWorkOrderHistory(uuid, mobile, orderNo, processCode));
	}
    
    /**
     * 工单催单
     */
    @PostMapping(WorkOrder.V1_WORKORDER_REMINDER)
    @ApiOperation(value = WorkOrder.V1_WORKORDER_REMINDER_DESC, notes = WorkOrder.V1_WORKORDER_REMINDER_DESC)
    public ResponseVo<Object> reminderWorkOrder(@RequestBody @Validated WorkOrderSubmitReqDTO reqDTO,
		@ApiIgnore @GetStaff StaffVO staffVO) {
    	try {
			//保存工单
    		workOrderService.reminderWorkOrder(reqDTO, String.valueOf(staffVO.getId()));
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
    }

	@PostMapping(value = WorkOrder.V1_WORKORDER_BATCH_BACK)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_BATCH_BACK_DESC, notes = WorkOrder.V1_WORKORDER_BATCH_BACK_DESC)
	public Response<Void> batchBack(@Validated @RequestBody WorkOrderBatchOperateDTO dto,
		@ApiIgnore @GetStaff StaffVO staffVO) {
		workOrderService.batchBack(dto.getExecutionIds(), staffVO.getId().toString());
		return Response.success();
	}

	@PostMapping(value = WorkOrder.V1_WORKORDER_BATCH_RETURN)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_BATCH_RETURN_DESC, notes = WorkOrder.V1_WORKORDER_BATCH_RETURN_DESC)
	public Response<Void> batchReturn(@Validated @RequestBody WorkOrderBatchOperateDTO dto,
		@ApiIgnore @GetStaff StaffVO staffVO) {
		workOrderService.batchReturn(dto.getExecutionIds(), staffVO.getId().toString());
		return Response.success();
	}

	@PostMapping(value = WorkOrder.V1_WORKORDER_BATCH_CLOSE)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_BATCH_CLOSE_DESC, notes = WorkOrder.V1_WORKORDER_BATCH_CLOSE_DESC)
	public Response<Void> batchClose(@Validated @RequestBody WorkOrderBatchOperateDTO dto,
		@ApiIgnore @GetStaff StaffVO staffVO) {
		workOrderService.batchClose(dto.getExecutionIds(), staffVO.getId().toString());
		return Response.success();
	}

	@PostMapping(value = WorkOrder.V1_WORKORDER_BATCH_SAVE)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_BATCH_SAVE_DESC, notes = WorkOrder.V1_WORKORDER_BATCH_SAVE_DESC)
	public Response<Void> batchSave(@Validated @RequestBody WorkOrderBatchSaveDTO dto,
		@ApiIgnore @GetStaff StaffVO staffVO) {
		workOrderService.batchSave(dto, staffVO.getId().toString());
		return Response.success();
	}


	@PostMapping(WorkOrder.V1_WORKORDER_SAVE_ATTACHMENT)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_SAVE_ATTACHMENT_DESC, notes = WorkOrder.V1_WORKORDER_SAVE_ATTACHMENT_DESC)
	@NoRepeatSubmit(expireSeconds = 60, times = 6)
	public ResponseVo<List<WoAttachmentVO>> saveAttachment(MultipartFile[] file, Long id) {
		try {
			List<MultipartFile> fileList = Arrays.asList(file);
			List<String> fileNameList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(fileList)) {
				for (MultipartFile multipartFile : fileList) {
					String fileName = workOrderService.uploadFile(multipartFile);
					if (StringUtils.isNotBlank(fileName)) {
						fileNameList.add(fileName);
					}
				}
			}
			List<WoAttachmentVO> woAttachmentVOList = workOrderService.getDownLoadUrl(fileNameList, id, null);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(),
					woAttachmentVOList);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}

	@GetMapping(WorkOrder.V1_WORKORDER_DEL_ATTCHMENT)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_DEL_ATTCHMENT_DESC, notes = WorkOrder.V1_WORKORDER_DEL_ATTCHMENT_DESC)
	public ResponseVo<Object> saveAttachment(@RequestParam Long id) {
		try {
			workOrderService.deleteWoAttachment(id);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(), null);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}

	@PostMapping(WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT)
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT_DESC, notes = WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT_DESC)
	public ResponseEntity<byte[]> noticeAttachDownloadDirect(@RequestBody FileDTO fileDTO, HttpServletRequest request, HttpServletResponse response) {
		if (!workOrderService.fileNameIsExist(fileDTO.getFileName())){
			throw new FastRuntimeException("文件名不存在");
		}
		return ResponseUtil
				.buildExportResponse(
						workOrderService.downloadWatermarkFile(fileDTO.getPath(), fileDTO.getFileName()).getResult(),
						fileDTO.getFileName(), request, response);
	}


	@GetMapping(value = WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT_NEW)
	@ApiOperation(value = WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT_NEW_DESC, notes = WorkOrder.V1_WORKORDER_DOWNLOAD_ATTCHMENT_NEW_DESC)
	public ResponseEntity<byte[]> getFileUrlById(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response) {
		String fileName = workOrderService.getFileNameById(id);
		return ResponseUtil
				.buildExportResponse(
						workOrderService.downloadWatermarkFile("", fileName).getResult(),
						fileName, request, response);
	}
	@GetMapping(value = WorkOrder.V1_TASK_OVER_ORDER)
	@ApiOperation(value = WorkOrder.V1_TASK_OVER_ORDER_DESC, notes = WorkOrder.V1_TASK_OVER_ORDER_DESC)
	public Response<Void> takeOverOrder(@RequestParam String executionId) {
		workOrderService.takeOverOrder(executionId);
		return Response.success();
	}


	@GetMapping(value = WorkOrder.V1_GET_FILE_URL)
	@ApiOperation(value = WorkOrder.V1_GET_FILE_URL_DESC, notes = WorkOrder.V1_GET_FILE_URL_DESC)
    public Response<String> getFileUrl(@RequestParam String fileName) {
        if (!workOrderService.fileNameIsExist(fileName)) {
            throw new FastRuntimeException("文件名不存在");
        }
        Map<String, Object> uploadFile = uploadService.getUploadFile(Collections.singletonList(fileName));
        return Response.success((String)uploadFile.get(fileName));
    }

	@GetMapping(value = WorkOrder.V1_GET_FILE_URL_NEW)
	@ApiOperation(value = WorkOrder.V1_GET_FILE_URL_NEW_DESC, notes = WorkOrder.V1_GET_FILE_URL_NEW_DESC)
	public Response<String> getFileUrlById(@RequestParam Long id) {
		String fileName = workOrderService.getFileNameById(id);
		Map<String, Object> uploadFile = uploadService.getUploadFile(Collections.singletonList(fileName));
		return Response.success((String) uploadFile.get(fileName));
	}

	@GetMapping(value = WorkOrder.V1_STATISTIC_SUBMIT_TIME)
	@ApiOperation(value = WorkOrder.V1_STATISTIC_SUBMIT_TIME_DESC, notes = WorkOrder.V1_STATISTIC_SUBMIT_TIME_DESC)
	public Response<Void> statisticSubmitTime(@RequestParam String status) {
		workOrderService.statisticsSubmitOrderTime(status);
		return new Response<>();
	}

	@PostMapping(value = WorkOrder.V1_FUND_COMPLAINT)
	@ApiOperation(value = WorkOrder.V1_FUND_COMPLAINT_DESC, notes = WorkOrder.V1_FUND_COMPLAINT_DESC)
	public Response<List<FundNameResDTO>> queryFundName(@Validated @RequestBody FundAndRegulatoryComplaintDTO dto) {
		return Response.success(workOrderService.queryFundName(dto));
	}

	@PostMapping(value = WorkOrder.V1_REGULATORY_COMPLAINT)
	@ApiOperation(value = WorkOrder.V1_REGULATORY_COMPLAINT_DESC, notes = WorkOrder.V1_REGULATORY_COMPLAINT_DESC)
	public Response<List<RegulatoryComplaintResDTO>> queryComplaintsChannel(@Validated @RequestBody FundAndRegulatoryComplaintDTO dto) {
		return Response.success(workOrderService.queryComplaint(dto));
	}


	@PostMapping(value = WorkOrder.V1_SAVE_MOBILE_BAK)
	@ApiOperation(value = WorkOrder.V1_SAVE_MOBILE_BAK_DESC, notes = WorkOrder.V1_SAVE_MOBILE_BAK_DESC)
	public Response<WoMobileBakVO> saveMobileBak(@Validated @RequestBody saveDTO dto) {
		return Response.success(workOrderService.saveMobileBak(dto));
	}

	@PostMapping(value = WorkOrder.V1_SAVE_EMAIL)
	@ApiOperation(value = WorkOrder.V1_SAVE_EMAIL_DESC, notes = WorkOrder.V1_SAVE_EMAIL_DESC)
	public Response<WoEmailVO> saveEmail(@Validated @RequestBody saveDTO dto) {
		return Response.success(workOrderService.saveEmail(dto));
	}


	@GetMapping(value = WorkOrder.V1_QUERY_ONLINE_FILE)
	@ApiOperation(value = WorkOrder.V1_QUERY_ONLINE_FILE_DESC, notes = WorkOrder.V1_QUERY_ONLINE_FILE_DESC)
	public Response<List<OnlineFileRetVO>> queryOnlineFile(@RequestParam Integer userId, @RequestParam Integer interval) {
		return Response.success(workOrderService.queryOnlineFile(userId, interval));
	}


	@PostMapping(WorkOrder.V1_SAVE_ONLINE_FILE)
	@ApiOperation(value = WorkOrder.V1_SAVE_ONLINE_FILE_DESC, notes = WorkOrder.V1_SAVE_ONLINE_FILE_DESC)
	@NoRepeatSubmit(expireSeconds = 60)
	public ResponseVo<List<WoAttachmentVO>> saveOnlineAttachment(@RequestBody BatchUploadUrlDTO dto) {
		try {
			List<String> fileNameList = new ArrayList<>();
			for (String url : dto.getUrlList()) {
				byte[] fileBytes = HttpUtil.downloadBytes(url);
				String fileName = CommonUtil.getOnlineBase64FileName(url);
				Response<String> res = uploadService.uploadFile(fileBytes, fileName);
				fileNameList.add(res.getResult());
			}

			List<WoAttachmentVO> woAttachmentVOList = workOrderService.getDownLoadUrl(fileNameList, dto.getId(), null);
			return new ResponseVo<>(String.valueOf(HttpStatus.OK.value()), ResponseVo.ok().toString(),
					woAttachmentVOList);
		} catch (Exception e) {
			String message = e.getMessage();
			return new ResponseVo<>(String.valueOf(HttpStatus.EXPECTATION_FAILED.value()), message, null);
		}
	}


	@PostMapping(value = WorkOrder.V1_CUSTOMER_UPDATE)
	@ApiOperation(value = WorkOrder.V1_CUSTOMER_UPDATE_DESC, notes = WorkOrder.V1_CUSTOMER_UPDATE_DESC)
	public Response<Void> customerUpdate(@RequestBody WorkOrderSubmitReqDTO reqDTO) {
		workOrderService.customerUpdate(reqDTO.getMobile(), reqDTO.getOrderNo());
		return Response.success();
	}


	@PostMapping(value = WorkOrder.V1_ADD_APPLICATION)
	@ApiOperation(value = WorkOrder.V1_ADD_APPLICATION_DESC, notes = WorkOrder.V1_ADD_APPLICATION_DESC)
	public Response<Void> addApplication(@RequestBody WorkOrderSubmitReqDTO reqDTO) {
		workOrderService.addApplication(reqDTO.getOrderNo(), reqDTO.getLoanList());
		return Response.success();
	}

	@PostMapping(value = WorkOrder.V1_UPDATE_ORDER_THREE_TYPE)
	@ApiOperation(value = WorkOrder.V1_UPDATE_ORDER_THREE_TYPE_DESC, notes = WorkOrder.V1_UPDATE_ORDER_THREE_TYPE_DESC)
	public Response<Void> updateOrderThreeType(@RequestBody WorkOrderSubmitReqDTO reqDTO) {
		workOrderService.updateOrderThreeType(reqDTO);
		return Response.success();
	}


}
