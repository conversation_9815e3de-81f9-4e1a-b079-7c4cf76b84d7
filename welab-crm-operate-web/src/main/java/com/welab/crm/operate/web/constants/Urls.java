/*
 * @Title: ConfigConsts.java
 * @Copyright: © 2017 我来贷
 * @Company: 深圳卫盈智信科技有限公司
 */
package com.welab.crm.operate.web.constants;

/**
 * 接口路径描述类
 * @description 所有请求URL
 * @version v1.0
 */
public interface Urls {

	/**
	 * 1.0版本接口
	 */
	String V1 = "/v1";

	String V2 = "/v2";

	interface DictInfo {
	    String V1_DICTINFO = V1 + "/dictinfos";

	    String V1_DICTINFO_QUERY = "/query";
	    String V1_DICTINFO_QUERY_DESC = "查询字典(树结构返回)";
	    String V1_DICTINFO_ADD = "/add";
	    String V1_DICTINFO_ADD_DESC = "添加字典";
	    String V1_DICTINFO_UPDATE = "/modify";
        String V1_DICTINFO_UPDATE_DESC = "更新字典";
        String V1_DICTINFO_DEL = "/del";
        String V1_DICTINFO_DEL_DESC = "删除字典";

        // 电话小结
		String V1_DICTINFO_CALL_SUMMARY_QUERY = "/query/callSummary";
		String V1_DICTINFO_CALL_SUMMARY_QUERY_DESC = "电话小结收藏查询";
        String V1_DICTINFO_CALL_SUMMARY_ADD = "/add/callSummary";
		String V1_DICTINFO_CALL_SUMMARY_ADD_DESC = "收藏电话小结";
		String V1_DICTINFO_CALL_SUMMARY_DEL = "/del/callSummary";
		String V1_DICTINFO_CALL_SUMMARY_DEL_DESC = "删除电话小结收藏";
		String V1_DICTINFO_CALL_SUMMARY_TOP = "/top";
		String V1_DICTINFO_CALL_SUMMARY_TOP_DESC = "置顶/取消置顶";

	}
	
    interface DictInfoConf {
        String V1_DICTINFOCONF = V1 + "/workgroup";

        String V1_DICTINFOCONF_QUERY = "/query";
        String V1_DICTINFOCONF_QUERY_DESC = "查询工单组合";
        String V1_DICTINFOCONF_ADD = "/add";
        String V1_DICTINFOCONF_ADD_DESC = "添加工单组合";
        String V1_DICTINFOCONF_UPDATE = "/modify";
        String V1_DICTINFOCONF_UPDATE_DESC = "更新工单组合";
        String V1_DICTINFOCONF_DEL = "/del";
        String V1_DICTINFOCONF_DEL_DESC = "删除工单组合";
    }

    interface Staff {
        String V1_STAFF = V1 + "/staff";

        String V1_STAFF_QUERY = "/query";
        String V1_STAFF_QUERY_DESC = "查询员工";
        String V1_STAFF_ADD = "/add";
        String V1_STAFF_ADD_DESC = "添加员工";
        String V1_STAFF_UPDATE = "/modify";
        String V1_STAFF_UPDATE_DESC = "更新员工";
		String V1_STAFF_UPDATES = "/modifys";
		String V1_STAFF_UPDATES_DESC = "批量更新员工";
        String V1_STAFF_DEL = "/del";
        String V1_STAFF_DEL_DESC = "删除员工";
        String V1_STAFF_QUERY_PAGE = "/page";
        String V1_STAFF_QUERY_PAGE_DESC = "分页查询员工";
		
		String V1_SYNC_STAFF_TO_EXAM_SYSTEM = "/sync-staff";
		String V1_SYNC_STAFF_TO_EXAM_SYSTEM_DESC = "同步staff";

		String V1_STAFF_DEL_BY_ID = "/del/{id}";
		String V1_STAFF_DEL_BY_ID_DESC = "根据id删除员工";
    }

    interface OrgInfo {
        String V1_ORG = V1 + "/orgInfo";

        String V1_ORG_QUERY = "/list";
        String V1_ORG_QUERY_DESC = "查询";
        String V1_ORG_SELECT_QUERY = "/listSelector/{code}";
        String V1_ORG_SELECT_QUERY_DESC = "组织下拉查询";
        String V1_ORG_ADD = "/add";
        String V1_ORG_ADD_DESC = "添加";
        String V1_ORG_DELETE = "/delete";
        String V1_ORG_DELETE_DESC = "删除";
        String V1_ORG_UPDATE = "/update";
        String V1_ORG_UPDATE_DESC = "更新";
    }

	interface User {
		String ROOT = V1 + "/user";

		String USER_DETAIL_QUERY = "/userDetailQuery";
		String USER_DETAIL_QUERY_DESC = "现金贷界面查询客户信息";

		String LOGOUT_USER_QUERY = "/logoutUserQuery";
		String LOGOUT_USER_QUERY_DESC = "注销用户信息查询";

		String WALLET_USER_DETAIL_QUERY = "/walletDetailQuery";
		String WALLET_USER_DETAIL_QUERY_DESC = "钱包用户查询";


		String SAVE_USER_INFO = "/saveCashUserInfo";
		String SAVE_USER_INFO_DESC = "保存用户信息,用户主界面新增按钮";

		String QUERY_USER_INFO_CASH = "/queryUserInfoCash";
		String QUERY_USER_INFO_CASH_DESC = "查询客服自己数据库里的现金贷用户信息";

		String QUERY_USER_INFO_WALLET = "/queryUserInfoWallet";
		String QUERY_USER_INFO_WALLET_DESC = "查询客服自己数据库里的钱包用户信息";

		String MOBILE_QUERY = "/mobileQuery";
		String MOBILE_QUERY_DESC = "根据手机号或者身份证查询用户信息";


		String MOBILE_QUERY_MANUAL = "/mobileQuery-manual";
		String MOBILE_QUERY_MANUAL_DESC = "根据手机号或者身份证查询用户信息-手动";

		String CARD_QUERY = "/privilegeCard";
		String CARD_QUERY_DESC = "根据客户uuid查询用户特权卡信息";

		String BLACK_LIST_QUERY = "/blacklist";
		String BLACK_LIST_QUERY_DESC = "根据客户userId查询用户黑名单信息";

		String DECODE = "/decode";
		String DECODE_DESC = "根据密文解密";

		String COLLECTIONS = "/collections";
		String COLLECTIONS_DESC = "催记查询";

		String APPLICATION_HISTORY ="/findApplications";
		String APPLICATION_HISTORY_DESC ="已注销用户从审批获取用户贷款号";
	}

	interface Operate {
		String ROOT = V1 + "/Operate";

		String USER_INFO_UPDATE = "/userInfoUpdate";
		String USER_INFO_UPDATE_DESC = "/更新用户信息(手机号、姓名、身份证)";

		String USER_LOGOUT = "/userLogout";
		String USER_LOGOUT_DESC = "用户注销";

		String APPLICATION = "/application";
		String APPLICATION_DESC = "/贷款操作(退回订单、取消订单、修改期数、修改金额)";

		String URGENT_APPROVAL = "urgent_approval";
		String URGENT_APPROVAL_DESC = "加急审批";

		String HISTORY_QUERY = "/historyQuery";
		String HISTORY_QUERY_DESC = "操作历史查询";
	}

	interface Phone {
		String ROOT = V1 + "/phone";

		String ADD_SOFT_PHONE_RECORD = "/addSoftPhoneRecord";
		String ADD_SOFT_PHONE_RECORD_DESC = "添加软电话通话记录";

		String QUERY_SOFT_PHONE_RECORD = "/querySoftPhoneRecord";
		String QUERY_SOFT_PHONE_RECORD_DESC = "查询软电话记录";


		String SAVE_PHONE_SUMMARY_DETAIL = "/savePhoneSummaryDetail";
		String SAVE_PHONE_SUMMARY_DETAIL_DESC = "保存电话小结";

		String QUERY_PHONE_SUMMARY_DETAIL = "/queryPhoneSummaryDetail";
		String QUERY_PHONE_SUMMARY_DETAIL_DESC = "查询电话小结";

		String PHONE_LOGIN_INFO_QUERY = "/login_info_query";
		String PHONE_LOGIN_INFO_QUERY_DESC = "座机登陆信息查询";

		String PHONE_LOGIN_INFO_QUERY_PAGE = "login_info_query_page";
		String PHONE_LOGIN_INFO_QUERY_PAGE_DESC = "座机登陆信息分页查询";

		String PHONE_LOGIN_INFO_ADD = "login_info_add";
		String PHONE_LOGIN_INFO_ADD_DESC = "增加座机登陆信息";

		String PHONE_LOGIN_INFO_UPDATE = "login_info_update";
		String PHONE_LOGIN_INFO_UPDATE_DESC = "更新座机登陆信息";

		String PHONE_LOGIN_INFO_DELETE = "login_info_delete";
		String PHONE_LOGIN_INFO_DELETE_DESC = "删除座机登陆信息";

		String QUERY_IS_SUMMARY = "isSummary";
		String QUERY_IS_SUMMARY_DESC = "是否做了电话小结";

		String LAST_PHONE_IS_SUMMARY = "lastPhoneIsSummary";
		String LAST_PHONE_IS_SUMMARY_DESC = "用户最后一通电话是否做了小结";
		
		
		String AGENT_MONITOR = "/agent-monitor";
		String AGENT_MONITOR_DESC = "坐席状态监控";
		
		
		String UPDATE_SKILL = "/update-skill";
		String UPDATE_SKILL_DESC = "更新技能组";
		
		
		String GET_AI_SUMMARY = "/ai-summary";
		String GET_AI_SUMMARY_DESC = "获取ai小结";


		String GET_DIALOGUE_TEXT = "/dialogue-text";
		String GET_DIALOGUE_TEXT_DESC = "获取对话文本";

		String GET_AI_SUMMARY_CHECK = "/ai-summary-check";
		String GET_AI_SUMMARY_CHECK_DESC = "获取ai小结(会校验录音是否存在)";


		String GET_AI_SUMMARY_RETRY = "/ai-summary-retry";
		String GET_AI_SUMMARY_RETRY_DESC = "重试获取ai小结";

	}

	interface Loan {

		String ROOT = V1 + "/loan";

		String V1_LOAN_APPLICATION_HISTORY = "/application/list";
		String V1_LOAN_APPLICATION_HISTORY_DESC = "查看贷款历史列表";

		String V1_LOAN_APPLICATION_DETAILS = "/application/details";
		String V1_LOAN_APPLICATION_DETAILS_DESC = "查询贷款详情";

		String V1_LOAN_APPLICATION_URL = "/application/agreement";
		String V1_LOAN_APPLICATION_URL_DESC = "查询贷款合同pdf";

		String V1_LOAN_APPLICATION_ORDER = "/application/order";
		String V1_LOAN_APPLICATION_ORDER_DESC = "交易记录";

		String V1_LOAN_APPLICATION_REPAYMENT = "/application/repayment";
		String V1_LOAN_APPLICATION_REPAYMENT_DESC = "还款明细";

		String V1_OUTSTANDING_LIST = "/outstanding/list";
		String V1_OUTSTANDING_LIST_DESC = "在途贷款查询";

		String V1_LOAN_ALLOW_EARLY_SETTLE = "/allowEarlySettle";
		String V1_LOAN_ALLOW_EARLY_SETTLE_DESC = "全额结清开关";

		String V1_LOAN_ALLOW_EARLY_SETTLE_BATCH = "/allowEarlySettle/batch";
		String V1_LOAN_ALLOW_EARLY_SETTLE_BATCH_DESC = "批量全额结清开关";

		String V1_LOAN_EARLY_SETTLE = "/earlySettle";
		String V1_LOAN_EARLY_SETTLE_DESC = "提前结清";

		String V1_LOAN_EARLY_SETTLE_BATCH = "/earlySettle/batch";
		String V1_LOAN_EARLY_SETTLE_BATCH_DESC = "批量提前结清";

		String V1_LOAN_PAYMENTS_DETAILS = "/payments/details";
		String V1_LOAN_PAYMENTS_DETAILS_DESC = "查询收支明细";
	}

	interface Label {

		String ROOT = V1 + "/label";
		String V1_LABEL_NAME_ALL = "/labelName/all";
		String V1_LABEL_NAME_ALL_DESC = "查看客户所有标签码值";
	}

	interface BankCard {

		String ROOT = V1 + "/bankcard";

		String V1_BANK_CARD_LIST = "/list";
		String V1_BANK_CARD_LIST_DESC = "查询银行卡列表";

		String V1_BANK_CARD_MATCH = "/match";
		String V1_BANK_CARD_MATCH_DESC = "绑卡查询";

		String V1_BANK_CARD_RELEASE = "/release";
		String V1_BANK_CARD_RELEASE_DESC = "银行卡解除授权";

		String V1_BANK_CARD_CHANGE = "/change";
		String V1_BANK_CARD_CHANGE_DESC = "银行卡换卡";
		
		String V1_BANK_OPEN_UNPIN = "/open-unpin";
		String V1_BANK_OPEN_UNPIN_DESC = "打开解绑开关";

		String V1_BANK_CHANNEL_VERIFY = "/channel-verify";
		String V1_BANK_CHANNEL_VERIFY_DESC = "绑卡详情";
	}

	interface Vip {

		String ROOT = V1 + "/vip";

		String V1_VIP_ORDER_LIST = "/order/list";
		String V1_VIP_ORDER_LIST_DESC = "会员订单列表";

		String V1_VIP_RIGHT_LIST = "/right/list";
		String V1_VIP_RIGHT_LIST_DESC = "会员权益列表";

		String V1_VIP_OPERATE_LOG_LIST = "/operateLog/list";
		String V1_VIP_OPERATE_LOG_LIST_DESC = "操作日志查询";

		String V1_VIP_BENEFIT_LIST = "/benefit/list";
		String V1_VIP_BENEFIT_LIST_DESC = "生活权益使用查询";

		String V1_VIP_LOCK = "/order/lock";
		String V1_VIP_LOCK_DESC = "会员订单冻结";

		String V1_VIP_LOCK_TIPS = "/order/lock/tips";
		String V1_VIP_LOCK_TIPS_DESC = "会员订单冻结提示";

		String V1_VIP_UNLOCK = "/order/unlock";
		String V1_VIP_UNLOCK_DESC = "会员订单解冻";
	}

	interface Wallet {

		String ROOT = V1 + "/wallet";

		String V1_WALLET_QUOTA_INFO_LIST = "/quota/info";
		String V1_WALLET_QUOTA_INFO_LIST_DESC = "授信申请信息";

		String V1_WALLET_QUOTA_HISTORY = "/quota/history";
		String V1_WALLET_QUOTA_HISTORY_DESC = "授信申请历史";

		String V1_WALLET_LOAN_DUE = "/loanDue";
		String V1_WALLET_LOAN_DUE_DESC = "分期模式订单查询";

		String V1_WALLET_MONTH_BILL = "/monthBill";
		String V1_WALLET_MONTH_BILL_DESC = "账单模式订单查询";

		String V1_WALLET_ORDER_RECORD = "/orderRecord";
		String V1_WALLET_ORDER_RECORD_DESC = "交易记录查询";

		String V1_WALLET_REPAY_RECORD = "/repayRecord";
		String V1_WALLET_REPAY_RECORD_DESC = "还款记录查询";

		String V1_WALLET_ALLOW_EARLY_SETTLE = "/allowEarlySettle";
		String V1_WALLET_ALLOW_EARLY_SETTLE_DESC = "开启结清开关";

		String V1_WALLET_ALLOW_EARLY_SETTLE_BATCH = "/allowEarlySettle/batch";
		String V1_WALLET_ALLOW_EARLY_SETTLE_BATCH_DESC = "批量开启结清开关";

		String V1_WALLET_EARLY_SETTLE = "/earlySettle";
		String V1_WALLET_EARLY_SETTLE_DESC = "提前结清";

		String V1_WALLET_EARLY_SETTLE_BATCH = "/earlySettle/batch";
		String V1_WALLET_EARLY_SETTLE_BATCH_DESC = "批量提前结清";

		String V1_WALLET_OUTSTANDING_LIST = "/outstanding/list";
		String V1_WALLET_OUTSTANDING_LIST_DESC = "钱包在途贷款查询";

		String V1_WALLET_OUTSTANDING_ALLOW_EARLY_SETTLE = "/outstanding/allowEarlySettle";
		String V1_WALLET_OUTSTANDING_ALLOW_EARLY_SETTLE_DESC = "钱包在途贷款开启结清开关";

		String V1_WALLET_OUTSTANDING_EARLY_SETTLE = "/outstanding/earlySettle";
		String V1_WALLET_OUTSTANDING_EARLY_SETTLE_DESC = "钱包在途贷款结清";

		String V1_WALLET_LOAN_DETAIL = "/loanDue/detail";
		String V1_WALLET_LOAN_DETAIL_DESC = "分期模式代扣详情" ;
	}

	interface Coupon {

		String V1_ROOT = V1 + "/coupon";

		String V1_COUPON_GIVEN_LIST = "/given/list";
		String V1_COUPON_GIVEN_LIST_DESC = "查询已赠送的红包";
		String V1_COUPON_CARD_LIST = "/card/list";
		String V1_COUPON_CARD_LIST_DESC = "查询可送给用户的红包";
		String V1_COUPON_SEND = "/send";
		String V1_COUPON_SEND_DESC = "发送红包";
		String V1_COUPON_MONITOR = "/monitor";
		String V1_COUPON_MONITOR_DESC = "卡券全局监控";
		String V1_COUPON_MONITOR_EXPORT = "/monitor/export";
		String V1_COUPON_MONITOR_EXPORT_DESC = "卡券全局监控导出";
		String V1_COUPON_SENDING_RECORD = "/sending/record";
		String V1_COUPON_SENDING_RECORD_DESC = "查询卡券发送记录";
		String V1_COUPON_SENDING_RECORD_EXPORT = "/sending/record/export";
		String V1_COUPON_SENDING_RECORD_EXPORT_DESC = "卡券发送记录导出";
		String V1_COUPON_WITHDRAW = "/withdraw";
		String V1_COUPON_WITHDRAW_DESC = "查询我的返现";
	}

	interface Message {

		String ROOT = V1 + "/message";

		String V1_MESSAGE_SEND = "/send";
		String V1_MESSAGE_SEND_DESC = "发送短信";

		String V1_MESSAGE_HISTORY_QUERY = "/history";
		String V1_MESSAGE_HISTORY_QUERY_DESC = "短信发送历史查询";

		String V1_MESSAGE_TEMPLATE_ADD = "/template/add";
		String V1_MESSAGE_TEMPLATE_ADD_DESC = "添加短信模板";

		String V1_MESSAGE_TEMPLATE_UPDATE = "/template/update";
		String V1_MESSAGE_TEMPLATE_UPDATE_DESC = "修改短信模板";

		String V1_MESSAGE_TEMPLATE_DELETE = "/template/delete/{id}";
		String V1_MESSAGE_TEMPLATE_DELETE_DESC = "删除短信模板";

		String V1_MESSAGE_TEMPLATE_QUERY = "/template/query";
		String V1_MESSAGE_TEMPLATE_QUERY_DESC = "查询短信模板";
		
		String V1_MESSAGE_TEMPLATE_TOP = "/template/top";
		String V1_MESSAGE_TEMPLATE_TOP_DESC = "置顶短信模板";

		String V1_MESSAGE_VALIDATE_QUERY = "/validate/query";
		String V1_MESSAGE_VALIDATE_QUERY_DESC = "查询人脸验证的核验记录";
	}

	interface Face {

		String ROOT = V1 + "/face";

		String V1_FACE_DETAIL = "/report/detail";
		String V1_FACE_DETAIL_DESC = "人脸认证明细";
		String V1_FACE_DETAIL_EXPORT = "/report/detail/export";
		String V1_FACE_DETAIL_EXPORT_DESC = "人脸认证明细导出";

		String V1_FACE_DAY = "/report/day";
		String V1_FACE_DAY_DESC = "人脸认证每日报表";
		String V1_FACE_DAY_EXPORT = "/report/day/export";
		String V1_FACE_DAY_EXPORT_DESC = "人脸认证每日报表导出";

		String V1_FACE_CAUSE = "/report/cause";
		String V1_FACE_CAUSE_DESC = "人脸认证失败原因统计报表";
		String V1_FACE_CAUSE_EXPORT = "/report/cause/export";
		String V1_FACE_CAUSE_EXPORT_DESC = "人脸认证失败原因统计报表导出";
	}

	interface TableConfig {

		String ROOT = V1 + "/tableTitle";

		String V1_TABLE_TITLE_QUERY = "/query";
		String V1_TABLE_TITLE_QUERY_DESC = "查询当前表格的不展示表头";

		String V1_TABLE_TITLE_UPDATE = "/update";
		String V1_TABLE_TITLE_UPDATE_DESC = "修改当前表格的不展示表头";
	}

    interface Notice {
		String ROOT = V1 + "/notice";

		String NOTICE_PUBLISH = "/publish";
		String NOTICE_PUBLISH_DESC = "发布消息";

		String NOTICE_DELETE = "/delete";
		String NOTICE_DELETE_DESC = "删除";

		String NOTICE_QUERY_RECEIVE = "/queryReceive";
		String NOTICE_QUERY_RECEIVE_DESC = "查询接受到的消息";

		String NOTICE_QUERY_SEND = "/querySend";
		String NOTICE_QUERY_SEND_DESC = "查询发送的消息";

		String NOTICE_QUERY_DETAIL = "/queryDetail";
		String NOTICE_QUERY_DETAIL_DESC = "查询消息详情";

		String NOTICE_REPLY = "/reply";
		String NOTICE_REPLY_DESC = "回复消息";

		String NOTICE_READ = "read";
		String NOTICE_READ_DESC = "消息设置为已读";

		String NOTICE_READ_QUERY = "queryRead";
		String NOTICE_READ_QUERY_DESC = "查询已读消息";

		String NOTICE_UNREAD_QUERY = "queryUnRead";
		String NOTICE_UNREAD_QUERY_DESC = "查询未读消息";

		String NOTICE_ATTACHMENT_UPLOAD = "/attachmentUpload";
		String NOTICE_ATTACHMENT_UPLOAD_DESC = "附件上传";

		String V1_NOTICE_ATTACHMENT_DOWNLOAD = "/attachmentDownload";
		String V1_NOTICE_ATTACHMENT_DOWNLOAD_DESC = "下载附件，返回一个map，key为文件名，value为文件下载路径";

		String V1_NOTICE_ATTACHMENT_DOWNLOAD_DIRECT = "/attachmentDownloadDirect";
		String V1_NOTICE_ATTACHMENT_DOWNLOAD_DIRECT_DESC = "下载附件";


		String V1_NOTICE_ALL_TYPE_COUNT = "/count";
		String V1_NOTICE_ALL_TYPE_COUNT_DESC = "查询各类型消息数量";

		String V1_NOTICE_ALL_READ = "/allRead";
		String V1_NOTICE_ALL_READ_DESC = "全部已读";

		String V1_NOTICE_BANNER = "banner";
		String V1_NOTICE_BANNER_DESC = "查询跑马灯消息";
	}

    interface WorkOrder {
        String ROOT = V1 + "/workOrder";
        String V1_WORKORDER_SEARCH = "/query";
        String V1_WORKORDER_SEARCH_DESC = "工单查询";
        String V1_WORKORDER_DETAIL = "/detail";
        String V1_WORKORDER_DETAIL_DESC = "工单详情";
        String V1_WORKORDER_INITNODE = "/getInitNodeConfig";
        String V1_WORKORDER_INITNODE_DESC = "进入流程任务发起页面";
        String V1_WORKORDER_TASKNODE = "/getTaskNodeInfo";
        String V1_WORKORDER_TASKNODE_DESC = "进入流程任务处理页";
        String V1_WORKORDER_ASSIGNEDSTAFFID = "/getAssignedStaffId";
        String V1_WORKORDER_ASSIGNEDSTAFFID_DESC = "获取目标任务的指派信息";
        String V1_WORKORDER_SUBMIT = "/submit";
        String V1_WORKORDER_SUBMIT_DESC = "工单提交";
		String V1_WORKORDER_COMPLAIN = "/complain/history";
		String V1_WORKORDER_COMPLAIN_DESC = "工单投诉历史数据查询";
		String V1_WORKORDER_COMPLAIN_URGE = "/complain/urge";
		String V1_WORKORDER_COMPLAIN_URGE_DESC = "工单投诉处理状态催促";
        String V1_WORKORDER_EXECUTE = "/excute";
        String V1_WORKORDER_EXECUTE_DESC = "工单处理";
        String V1_WORKORDER_SAVE = "/save";
        String V1_WORKORDER_SAVE_DESC = "工单保存";
        String V1_WORKORDER_COUNT = "/count";
        String V1_WORKORDER_COUNT_DESC = "未分配工单个数查询";
        String V1_WORKORDER_TOTAL = "/total";
        String V1_WORKORDER_TOTAL_DESC = "工单统计";
        String V1_WORKORDER_SIGN = "/sign";
        String V1_WORKORDER_SIGN_DESC = "工单标记";
		String V1_WORKORDER_HISTORY = "/history";
		String V1_WORKORDER_HISTORY_DESC = "查询工单历史";
        String V1_WORKORDER_REMINDER = "/reminder";
        String V1_WORKORDER_REMINDER_DESC = "工单催单";

		String V1_WORKORDER_BATCH_BACK = "/batch/back";
		String V1_WORKORDER_BATCH_BACK_DESC = "批量返回结果";
		String V1_WORKORDER_BATCH_RETURN = "/batch/return";
		String V1_WORKORDER_BATCH_RETURN_DESC = "批量退回";
		String V1_WORKORDER_BATCH_CLOSE = "/batch/close";
		String V1_WORKORDER_BATCH_CLOSE_DESC = "批量结案";
		String V1_WORKORDER_BATCH_SAVE = "/batch/save";
		String V1_WORKORDER_BATCH_SAVE_DESC = "批量回复意见";

        String V1_WORKORDER_SAVE_ATTACHMENT = "/saveAttachment";
        String V1_WORKORDER_SAVE_ATTACHMENT_DESC = "保存工单附件";
        String V1_WORKORDER_DEL_ATTCHMENT = "/delAttachment";
        String V1_WORKORDER_DEL_ATTCHMENT_DESC = "删除工单附件";

        String V1_WORKORDER_DOWNLOAD_ATTCHMENT = "/downloadAttachment";
        String V1_WORKORDER_DOWNLOAD_ATTCHMENT_DESC = "下载附件";

	    String V1_WORKORDER_DOWNLOAD_ATTCHMENT_NEW = "/downloadAttachment-new";
	    String V1_WORKORDER_DOWNLOAD_ATTCHMENT_NEW_DESC = "下载附件通过id";


		String V1_ELITE_WORKORDER_DETAIL = "/eliteDetail";
		String V1_ELITE_WORKORDER_DETAIL_DESC = "过河兵工单详情";

		String V1_TASK_OVER_ORDER = "/takeOver";
		String V1_TASK_OVER_ORDER_DESC = "接管工单";

		String V1_GET_FILE_URL = "/fileUrl";
		String V1_GET_FILE_URL_DESC = "获取文件下载地址";

	    String V1_GET_FILE_URL_NEW = "/fileUrl-new";
	    String V1_GET_FILE_URL_NEW_DESC = "获取文件下载地址根据id";
		
		String V1_STATISTIC_SUBMIT_TIME = "/statistics-submit-time";
		String V1_STATISTIC_SUBMIT_TIME_DESC = "统计工单提交时间";

		String V1_FUND_COMPLAINT = "/fund/complaint";
		String V1_FUND_COMPLAINT_DESC = "资金投诉统计";

		String V1_REGULATORY_COMPLAINT = "/regulatory/complaint";
		String V1_REGULATORY_COMPLAINT_DESC = "监管投诉统计";
		
		String V1_SAVE_MOBILE_BAK = "/mobile-bak/save";
		String V1_SAVE_MOBILE_BAK_DESC = "保存备用手机号";
		
		
		String V1_SAVE_EMAIL = "/email/save";
		String V1_SAVE_EMAIL_DESC = "保存邮箱号";
		
		String V1_QUERY_ONLINE_FILE = "/online-file/query";
		String V1_QUERY_ONLINE_FILE_DESC = "查询在线系统文件";

	    String V1_SAVE_ONLINE_FILE = "/online-file/save";
	    String V1_SAVE_ONLINE_FILE_DESC = "保存在线系统文件";

		String V1_CUSTOMER_UPDATE = "/customer/update";
		String V1_CUSTOMER_UPDATE_DESC = "客户信息更新";

		String V1_ADD_APPLICATION = "/add/application";
		String V1_ADD_APPLICATION_DESC = "新增贷款";


		String V1_UPDATE_ORDER_THREE_TYPE = "/update/order-three-type";
		String V1_UPDATE_ORDER_THREE_TYPE_DESC = "更新工单三级分类";
    }

    interface WorkOrderType {
        String ROOT = V1 + "/workOrderType";
        String V1_WORKORDER_TYPE_QUERY = "/query";
        String V1_WORKORDER_TYPE_QUERY_DESC = "工单分类查询";
        String V1_WORKORDER_TYPE_UPDATE = "/update";
        String V1_WORKORDER_TYPE_UPDATE_DESC = "工单分类更新";
        String V1_WORKORDER_TYPE_ADD = "/add";
        String V1_WORKORDER_TYPE_ADD_DESC = "工单分类新增";
    }

    interface WoRule {
        String ROOT = V1 + "/rule";
        String V1_WO_RULE_QUERY = "/query";
        String V1_WO_RULE_QUERY_DESC = "分单规则查询";
        String V1_WO_RULE_UPDATE = "/update";
        String V1_WO_RULE_UPDATE_DESC = "分单规则更新";
        String V1_WO_RULE_ADD = "/add";
        String V1_WO_RULE_ADD_DESC = "分段规则新增";
        String V1_WO_RULE_DELETE = "/delete";
        String V1_WO_RULE_DELETE_DESC = "分段规则删除";
        String V1_WO_RULE_UPDATE_ALL = "/updateAll";
        String V1_WO_RULE_UPDATE_ALL_DESC = "分单规则总开关";
        
        String V1_WO_RULE_ADJUST_QUERY = "/adjust/query";
        String V1_WO_RULE_ADJUST_QUERY_DESC = "分单调剂规则查询";
        
        String V1_WO_RULE_ADJUST = "/adjust";
        String V1_WO_RULE_ADJUST_DESC = "分单调剂";
        
        String V1_WO_RULE_MANUAL_CONFIG = "/manual/config";
        String V1_WO_RULE_MANUAL_CONFIG_DESC = "手动领取配置";
        String V1_WO_RULE_MANUAL_QUERY = "/manual/query";
        String V1_WO_RULE_MANUAL_QUERY_DESC = "手动领取查询";
        
        String V1_WO_RULE_TYPETOTAL = "/typeTotal";
        String V1_WO_RULE_TYPETOTAL_DESC = "工单类型个数统计";
    }

	interface WorkOrderReport {

		String ROOT = V1 + "/workOrder/report";
		String V1_WO_REPORT_SUMMARY = "/summary/{reportType:complaint|product|all}";
		String V1_WO_REPORT_SUMMARY_DESC = "工单统计报表";

		String V1_WO_REPORT_SUMMARY_EXPORT = "/summary/{reportType:complaint|product|all}/export";
		String V1_WO_REPORT_SUMMARY_EXPORT_DESC = "工单统计报表导出";

		String V1_WO_REPORT_SUMMARY_TYPE = "/summary/type";
		String V1_WO_REPORT_SUMMARY_TYPE_DESC = "工单统计报表明细";

		String V1_WO_REPORT_SUMMARY_TYPE_EXPORT = "/summary/type/export";
		String V1_WO_REPORT_SUMMARY_TYPE_EXPORT_DESC = "工单统计报表明细导出";

		String V1_WO_REPORT_DETAILS = "/details";
		String V1_WO_REPORT_DETAILS_DESC = "工单明细报表";
		String V1_WO_REPORT_DETAILS_EXPORT = "/details/export";
		String V1_WO_REPORT_DETAILS_EXPORT_DESC = "工单明细报表导出";

		String V1_WO_REPORT_ASSIGNMENT_SUMMARY = "/assignment/summary";
		String V1_WO_REPORT_ASSIGNMENT_SUMMARY_DESC = "分单统计报表";
		String V1_WO_REPORT_ASSIGNMENT_SUMMARY_EXPORT = "/assignment/summary/export";
		String V1_WO_REPORT_ASSIGNMENT_SUMMARY_EXPORT_DESC = "分单统计报表导出";
		String V1_WO_REPORT_ASSIGNMENT_DETAILS = "/assignment/details";
		String V1_WO_REPORT_ASSIGNMENT_DETAILS_DESC = "分单明细报表";
		String V1_WO_REPORT_ASSIGNMENT_DETAILS_EXPORT = "/assignment/details/export";
		String V1_WO_REPORT_ASSIGNMENT_DETAILS_EXPORT_DESC = "分单明细报表导出";
		String V1_WO_REPORT_EFFICIENCY_SUMMARY = "/efficiency/summary";
		String V1_WO_REPORT_EFFICIENCY_SUMMARY_DESC = "工单效能统计报表";
		String V1_WO_REPORT_EFFICIENCY_SUMMARY_EXPORT = "/efficiency/summary/export";
		String V1_WO_REPORT_EFFICIENCY_SUMMARY_EXPORT_DESC = "工单效能统计报表导出";
		String V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY = "/outbound/efficiency/summary";
		String V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_DESC = "外呼效能统计报表";
		String V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_EXPORT = "/outbound/efficiency/summary/export";
		String V1_WO_REPORT_OUTBOUND_EFFICIENCY_SUMMARY_EXPORT_DESC = "外呼效能统计报表导出";
		String V1_WO_REPORT_CENTRAL_MONITORING = "/central/monitoring";
		String V1_WO_REPORT_CENTRAL_MONITORING_DESC = "工单中央监控报表";
		String V1_WO_REPORT_RESOLUTION_RATE = "/resolution/rate";
		String V1_WO_REPORT_RESOLUTION_RATE_DESC = "坐席首次问题解决率报表";
		String V1_WO_REPORT_RESOLUTION_RATE_EXPORT = "/resolution/rate/export";
		String V1_WO_REPORT_RESOLUTION_RATE_EXPORT_DESC = "坐席首次问题解决率报表导出";
		String V1_WO_REPORT_SATISFACTION = "/satisfaction";
		String V1_WO_REPORT_SATISFACTION_DESC = "满意度调查报表";
		String V1_WO_REPORT_SATISFACTION_EXPORT = "/satisfaction/export";
		String V1_WO_REPORT_SATISFACTION_EXPORT_DESC = "满意度调查报表导出";
		String V1_WORKORDER_COMPLAIN_LIST = "/complain/list";
		String V1_WORKORDER_COMPLAIN_LIST_DESC = "工单客户投诉历史数据列表";
		String V1_WORKORDER_COMPLAIN_EXPORT = "/complain/export";
		String V1_WORKORDER_COMPLAIN_EXPORT_DESC = "导出工单客户投诉历史数据列表";

		String V1_FUND_COMPLAINT_EXPORT = "/fund/complaint/export";
		String V1_FUND_COMPLAINT_EXPORT_DESC = "资金投诉统计导出";

		String V1_REGULATORY_COMPLAINT_EXPORT = "/regulatory/complaint/export";
		String V1_REGULATORY_COMPLAINT_EXPORT_DESC = "监管投诉统计导出";
		
		String V1_TRANSFER_COMPLAINT_REPORT = "/transfer/complaint/range-report";
		String V1_TRANSFER_COMPLAINT_REPORT_DESC = "债转投诉统计表-周期";

		String V1_TRANSFER_COMPLAINT_REPORT_EXPORT = "/transfer/complaint/range-report/export";
		String V1_TRANSFER_COMPLAINT_REPORT_EXPORT_DESC = "债转投诉统计表-周期导出";

		String V1_TRANSFER_COMPLAINT_MONTH_REPORT = "/transfer/complaint/month-report";
		String V1_TRANSFER_COMPLAINT_MONTH_REPORT_DESC = "债转投诉统计表-月度";

		String V1_TRANSFER_COMPLAINT_MONTH_REPORT_EXPORT = "/transfer/complaint/month-report/export";
		String V1_TRANSFER_COMPLAINT_MONTH_REPORT_EXPORT_DESC = "债转投诉统计表-月度导出";


		String V1_TRANSFER_COMPLAINT_DETAIL_REPORT = "/transfer/complaint/detail-report";
		String V1_TRANSFER_COMPLAINT_DETAIL_REPORT_DESC = "债转投诉明细表";


		String V1_TRANSFER_COMPLAINT_DETAIL_REPORT_EXPORT = "/transfer/complaint/detail-report/export";
		String V1_TRANSFER_COMPLAINT_DETAIL_REPORT_EXPORT_DESC = "债转投诉明细表导出";
		
		
		String V1_COMPLAINT_ESCALATION_REPORT = "/complaint-escalation-report";
		String V1_COMPLAINT_ESCALATION_REPORT_DESC = "投诉升级明细报表";
		
		String V1_COMPLAINT_ESCALATION_REPORT_EXPORT = "/complaint-escalation-report/export";
		String V1_COMPLAINT_ESCALATION_REPORT_EXPORT_DESC = "投诉升级明细报表导出";


		String V1_COMPLAINT_ESCALATION_STATISTICS_REPORT = "/complaint-escalation-report/statistics";
		String V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_DESC = "投诉升级统计报表";
		
		String V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_EXPORT = "/complaint-escalation-report/statistics/export";
		
		String V1_COMPLAINT_ESCALATION_STATISTICS_REPORT_EXPORT_DESC = "投诉升级统计报表导出";
		
		String V1_COMPLAINT_ESCALATION_REASON_REPORT = "/complaint-escalation-report/reason";
		String V1_COMPLAINT_ESCALATION_REASON_REPORT_DESC = "投诉升级原因统计报表";
		
		String V1_COMPLAINT_ESCALATION_REASON_REPORT_EXPORT = "/complaint-escalation-report/reason/export";
		String V1_COMPLAINT_ESCALATION_REASON_REPORT_EXPORT_DESC = "投诉升级原因统计报表导出";
	}

	interface WorkStatusReport {
		String ROOT = V1 + "/workStatus/report";
		String V1_WO_REPORT_WORK_STATUS_SUMMARY = "/summary";
		String V1_WO_REPORT_WORK_STATUS_SUMMARY_DESC = "工作状态统计报表";
		String V1_WO_REPORT_WORK_STATUS_SUMMARY_EXPORT = "/summary/export";
		String V1_WO_REPORT_WORK_STATUS_SUMMARY_EXPORT_DESC = "工作状态统计报表导出";
		String V1_WO_REPORT_WORK_STATUS_CALL_IN = "/call/in";
		String V1_WO_REPORT_WORK_STATUS_CALL_IN_DESC = "客服呼入工作状态清单";
		String V1_WO_REPORT_WORK_STATUS_CALL_IN_EXPORT = "/call/in/export";
		String V1_WO_REPORT_WORK_STATUS_CALL_IN_EXPORT_DESC = "客服呼入工作状态清单导出";
		String V1_WO_REPORT_EFFICIENCY_STAFF = "/efficiency/staff";
		String V1_WO_REPORT_EFFICIENCY_STAFF_DESC = "员工效能报表";
		String V1_WO_REPORT_EFFICIENCY_STAFF_EXPORT = "/efficiency/staff/export";
		String V1_WO_REPORT_EFFICIENCY_STAFF_EXPORT_DESC = "员工效能报表导出";
	}

	interface TmkReport{
		String ROOT = V1 + "/tmkReport";

		String V1_TMK_REPORT_MONITOR_ASSETS = "/monitor/assets";
		String V1_TMK_REPORT_MONITOR_ASSETS_DESC = "中央监控资产看板";
		String V1_TMK_REPORT_MONITOR_REDISTRIBUTED_OUTBOUND_EFFICIENCY = "/monitor/redistributed/outbound/efficiency";
		String V1_TMK_REPORT_MONITOR_REDISTRIBUTED_OUTBOUND_EFFICIENCY_DESC = "中央监控再分配数据外呼效能报表";
	}

    interface StaffStatus{
		String ROOT = V1 + "/staffStatus";

		String V1_STAFF_STATUS_REPORT = "/report";
		String V1_STAFF_STATUS_REPORT_DESC = "上报状态信息";
	}
    
    interface Template {
        String ROOT = V1 + "/template";
        String V1_TEMPLATE_QUERY = "/query";
        String V1_TEMPLATE_QUERY_DESC = "模板查询";
        String V1_TEMPLATE_UPDATE = "/update";
        String V1_TEMPLATE_UPDATE_DESC = "模板更新";
        String V1_TEMPLATE_ADD = "/add";
        String V1_TEMPLATE_ADD_DESC = "模板新增";
        String V1_TEMPLATE_DELETE = "/delete";
        String V1_TEMPLATE_DELETE_DESC = "模板删除";
    }

	interface Transfer {
		String ROOT = V1 + "/transfer";
		String V1_TRANSFER_QUERY = "/query";
		String V1_TRANSFER_QUERY_DESC = "债转结清查询";
		String V1_TRANSFER_IMPORT = "/import";
		String V1_TRANSFER_IMPORT_DESC = "导入债转结清数据";
		String V1_TRANSFER_TEMPLATE = "/template";
		String V1_TRANSFER_TEMPLATE_DESC = "下载债转结清导入模板";
		String V1_TRANSFER_DOWNLOAD = "/download";
		String V1_TRANSFER_DOWNLOAD_DESC = "债转结清附件下载";
		String V1_TRANSFER_UPLOAD = "/upload";
		String V1_TRANSFER_UPLOAD_DESC = "债转结清附件上传";
		String V1_TRANSFER_DETAIL = "/detail";
		String V1_TRANSFER_DETAIL_DESC = "债转结清查询贷款详情";
		String V1_TRANSFER_DETAIL_EXPORT = "/detail/export";
		String V1_TRANSFER_DETAIL_EXPORT_DESC = "债转结清查询贷款详情导出";
		String V1_TRANSFER_IMPORT_QUERY = "/import/query";
		String V1_TRANSFER_IMPORT_QUERY_DESC = "导入文件查询";
		String V1_TRANSFER_OUTCASE_IMPORT = "/outcase/import";
		String V1_TRANSFER_OUTCASE_IMPORT_DESC = "导入委外数据";
		String V1_TRANSFER_OUTCASE_TEMPLATE = "/outcase/template";
		String V1_TRANSFER_OUTCASE_TEMPLATE_DESC = "下载委外导入模板";
		String V1_TRANSFER_DEPT_IMPORT = "/dept/import";
		String V1_TRANSFER_DEPT_IMPORT_DESC = "导入债转数据";
		String V1_TRANSFER_DEPT_TEMPLATE = "/dept/template";
		String V1_TRANSFER_DEPT_TEMPLATE_DESC = "下载债转导入模板";
		
		String V1_TRANSFER_ORDER_DETAIL = "/order-detail";
		String V1_TRANSFER_ORDER_DETAIL_DESC = "查询工单债转结清订单明细";


		String V1_TRANSFER_ORDER_DETAIL_EXPORT = "/order-detail/export";
		String V1_TRANSFER_ORDER_DETAIL_EXPORT_DESC = "导出工单债转结清订单明细";

		String V1_SEND_SMS = "/send-sms";
		String V1_SEND_SMS_DESC = "发送短信";
	}

	interface Cancel {
		String ROOT = V1 + "/cancel";
		String V1_CANCEL_QUERY = "/query";
		String V1_CANCEL_QUERY_DESC = "贷款取消申请查询";
		String V1_CANCEL_IMPORT = "/import";
		String V1_CANCEL_IMPORT_DESC = "导入贷款取消数据";
		String V1_CANCEL_TEMPLATE = "/template";
		String V1_CANCEL_TEMPLATE_DESC = "下载贷款取消导入模板";
		String V1_CANCEL_APPROVE = "/approve";
		String V1_CANCEL_APPROVE_DESC = "审核贷款取消申请数据";
	}

	interface ReportPhoneResult {
		String ROOT = V1 + "/reportPhoneResult";

		String V1_REPORTPHONERESULT_QUERY_DETAIL = "/query/detail";
		String V1_REPORTPHONERESULT_QUERY_DETAIL_DESC = "查询电话小结明细";
		String V1_REPORTPHONERESULT_QUERY_EXCEL_DETAIL = "/query/detail/excel";
		String V1_REPORTPHONERESULT_QUERY_EXCEL_DETAIL_DESC = "导出查询电话小结明细";
		String V1_REPORTPHONERESULT_QUERY = "/query";
		String V1_REPORTPHONERESULT_QUERY_DESC = "查询电话小结统计";
		String V1_REPORTPHONERESULT_EXCEL_QUERY = "/query/excel";
		String V1_REPORTPHONERESULT_EXCEL_QUERY_DESC = "导出查询电话小结统计";
	}

	interface ReportPhone {
		String ROOT = V1 + "/reportPhone";

		String V1_REPORTPHONE_QUERY_DETAIL = "/query/detail";
		String V1_REPORTPHONE_QUERY_DETAIL_DESC = "查询来电明细";
		String V1_REPORTPHONE_QUERY_DETAIL_EXCEL = "/query/detail/excel";
		String V1_REPORTPHONE_QUERY_DETAIL_EXCEL_DESC = "导出来电明细";
		String V1_REPORTPHONE_QUERY = "/query";
		String V1_REPORTPHONE_QUERY_DESC = "查询来电统计";
		String V1_REPORTPHONE_QUERY_EXCEL = "/query/excel";
		String V1_REPORTPHONE_QUERY_EXCEL_DESC = "导出查询来电统计";
	}

	interface SuDict {
		String ROOT = V1 + "/sale/summary/dict";
		String V1_SALE_SUMMARY_QUERY = "/query";
		String V1_SALE_SUMMARY_QUERY_DESC = "电销话务小结查询";
		String V1_SALE_SUMMARY_ADD = "/add";
		String V1_SALE_SUMMARY_ADD_DESC = "电销话务小结新增";
		String V1_SALE_SUMMARY_UPDATE = "/update";
		String V1_SALE_SUMMARY_UPDATE_DESC = "电销话务小结修改";
	}

	interface SettlementProof {
		String ROOT = V1 + "/settlementProof";

		String V1_SETTLEMENTPROOF_APPLY = "/apply";
		String V1_SETTLEMENTPROOF_APPLY_DESC = "获取兰州、网商结清证明";

		String V1_LZ_BANK_SETTLEMENTPROOF_APPLY = "/lzSettlementProofApply";
		String V1_LZ_BANK_SETTLEMENTPROOF_APPLY_DESC = "兰州银行结清文件申请";

		String V1_SETTLEMENTPROOF_DETAIL = "/detail";
		String V1_SETTLEMENTPROOF_DETAIL_DESC = "结清证明数据";

		String V1_SETTLEMENTPROOF_DOWNLOAD_URL = "/url";
		String V1_SETTLEMENTPROOF_DOWNLOAD_URL_DESC = "获取结清证明下载链接";

	}

	interface AI {
		String ROOT = V1 + "/sale/ai";
		String V1_SALE_AI_QUERY = "/query";
		String V1_SALE_AI_QUERY_DESC = "ai外呼推送查询";
		String V1_SALE_AI_ADD = "/add";
		String V1_SALE_AI_ADD_DESC = "ai外呼推送新增";
		String V1_SALE_AI_UPDATE = "/update";
		String V1_SALE_AI_UPDATE_DESC = "ai外呼推送修改";
		String V1_SALE_AI_DELETE = "/delete";
		String V1_SALE_AI_DELETE_DESC = "ai外呼推送删除";
		String V1_SALE_AI_HISTORY = "/history/query";
		String V1_SALE_AI_HISTORY_DESC = "ai外呼推送历史查询";
		String V1_SALE_AI_STATE_UPDATE = "/update/state";
		String V1_SALE_AI_STATE_UPDATE_DESC = "ai外呼推送数据启用状态更新";
	}


	interface Telemarketing {
		String ROOT = V1 + "/telemarketing";

		String V1_TASK_LIST = "/list/query";
		String V1_TASK_LIST_DESC = "查询电销任务列表";

		String V1_TASK_DETAIL = "/detail/query";
		String V1_TASK_DETAIL_DESC = "查询电销任务详情";

		String V1_TASK_COUNT = "/count";
		String V1_TASK_COUNT_DESC = "查询各类型任务数量";

		String V1_SUMMARY_SAVE = "/summary/save";
		String V1_SUMMARY_SAVE_DESC = "保存电话小结";

		String V1_TASK_APPOINT = "/appoint";
		String V1_TASK_APPOINT_DESC = "预约电销联系时间";

		String V1_CALL_OUT_REPORT = "/report/callOut";
		String V1_CALL_OUT_REPORT_DESC = "电销外呼统计报表";

		String V1_CALL_OUT_REPORT_EXPORT = "/report/callOut/export";
		String V1_CALL_OUT_REPORT_EXPORT_DESC = "电销外呼统计报表导出";

		String V1_TRANSFORM_REPORT = "/report/transform";
		String V1_TRANSFORM_REPORT_DESC = "电销转化报表";

		String V1_TRANSFORM_REPORT_EXPORT = "/report/transform/export";
		String V1_TRANSFORM_REPORT_EXPORT_DESC = "电销转化报表导出";

		String V1_STAFF_EFFICIENCY_REPORT = "/report/efficiency";
		String V1_STAFF_EFFICIENCY_REPORT_DESC = "员工效能报表(实时)";

		String V1_STAFF_EFFICIENCY_REPORT_EXPORT = "/report/efficiency/export";
		String V1_STAFF_EFFICIENCY_REPORT_EXPORT_DESC = "员工效能报表导出";

		String V1_AI_PUSH_REPORT = "/report/aiPush";
		String V1_AI_PUSH_REPORT_DESC = "AI外呼营销小结报表";

		String V1_AI_PUSH_REPORT_EXPORT = "/report/aiPush/export";
		String V1_AI_PUSH_REPORT_EXPORT_DESC = "AI外呼影响小结报表导出";

		String V1_AI_CONFIG_ALL = "/aiConfig/all";
		String V1_AI_CONFIG_ALL_DESC = "查询全部AI推送配置，包括已删除的，供AI转化报表筛选项使用";

		String V1_AI_TRANSFORM_REPORT = "/report/transform/ai";
		String V1_AI_TRANSFORM_REPORT_DESC = "AI转化报表";

		String V1_AI_TRANSFORM_REPORT_EXPORT = "/report/transform/ai/export";
		String V1_AI_TRANSFORM_REPORT_EXPORT_DESC = "AI转化报表导出";

		String V1_LOAN_REPORT = "/report/loan";
		String V1_LOAN_REPORT_DESC = "放款邀约外拨营销结果小结报表";

		String V1_LOAN_REPORT_EXPORT = "/report/loan/export";
		String V1_LOAN_REPORT_EXPORT_DESC = "放款邀约外拨营销结果小结报表导出";

		String V1_CJHY_REPORT = "/report/cjhy";
		String V1_CJHY_REPORT_DESC = "超级会员外拨营销结果小结报表";

		String V1_CJHY_REPORT_EXPORT = "/report/cjhy/export";
		String V1_CJHY_REPORT_EXPORT_DESC = "超级会员外拨营销结果小结报表导出";

		String V1_WALLET_REPORT = "/report/wallet";
		String V1_WALLET_REPORT_DESC = "钱夹谷谷外拨营销结果小结报表";

		String V1_WALLET_REPORT_EXPORT = "/report/wallet/export";
		String V1_WALLET_REPORT_EXPORT_DESC = "钱夹谷谷外拨营销结果小结报表导出";

		String V1_UUID_REPORT = "/report/uuid";
		String V1_UUID_REPORT_DESC = "UUID外拨营销结果小结报表";

		String V1_UUID_REPORT_EXPORT = "/report/uuid/export";
		String V1_UUID_REPORT_EXPORT_DESC = "UUID外拨营销结果小结报表导出";

		String V1_ASSIGN_DETAIL_REPORT = "/report/assign/detail";
		String V1_ASSIGN_DETAIL_REPORT_DESC = "电销名单分配明细报表";

		String V1_ASSIGN_DETAIL_REPORT_EXPORT = "/report/assign/detail/export";
		String V1_ASSIGN_DETAIL_REPORT_EXPORT_DESC = "电销名单分配明细报表导出";

		String V1_UUID_PACKAGE_QUERY = "/uuid/package/query";
		String V1_UUID_PACKAGE_QUERY_DESC = "查询最近号码包";
	}

	interface TmkRule {
        String ROOT = V1 + "/tmkRule";
        String V1_TMK_RULE_QUERY = "/query";
        String V1_TMK_RULE_QUERY_DESC = "电销数据查询";
        String V1_TMK_RULE_TYPETOTAL = "/typeTotal";
        String V1_TMK_RULE_TYPETOTAL_DESC = "电销业务类型个数统计";
        String V1_TMK_RULE_ADJUST = "/adjust";
        String V1_TMK_RULE_ADJUST_DESC = "手动调剂确认分配";
    }

	interface TmkManager {
        String ROOT = V1 + "/tmkManager";
        String V1_TMK_MANAGER_QUERY = "/query";
        String V1_TMK_MANAGER_QUERY_DESC = "数据再分配详单查询";
        String V1_TMK_MANAGER_TOTAL = "/total";
        String V1_TMK_MANAGER_TOTAL_DESC = "数据再分配数量查询";
        String V1_TMK_MANAGER_ADJUST = "/adjust";
        String V1_TMK_MANAGER_ADJUST_DESC = "数据再分配";
        String V1_TMK_MANAGER_ASSIGN_QUERY = "/assign/query";
        String V1_TMK_MANAGER_ASSIGN_QUERY_DESC = "数据分配历史查询";
    }

	interface PhoneSoundRecording{
		String ROOT = V1 + "/file";

		String V1_QUERY_FILE = "/query";
		String V1_QUERY_FILE_DESC = "获取录音文件地址";

		String V1_QUERY_FILE_BY_ID = "/query-by-id";
		String V1_QUERY_FILE_BY_ID_DESC = "获取录音文件地址根据id";

		String V1_QUERY_DETAIL = "/detail";
		String V1_QUERY_DETAIL_DESC = "通话明细";
		
		
		String V1_CONNECTED_COUNT = "/count";
		String V1_CONNECTED_COUNT_DESC = "接通数量查询";
	}

	interface WebSocket{
		String ROOT = V1 + "/ws";

		String V1_SEND_MSG = "/sendMsg";
		String V1_SEND_MSG_DESC = "websocket发送消息";
	}

	interface Ivr {
		String ROOT = V1 + "/ivr";

		String V1_KEY_DETAIL_REPORT = "/report/keyDetail";
		String V1_KEY_DETAIL_REPORT_DESC = "ivr按键详情报表";

		String V1_KEY_DETAIL_REPORT_EXPORT = "/report/keyDetail/export";
		String V1_KEY_DETAIL_REPORT_EXPORT_DESC = "ivr按键详情报表导出";

		String V1_KEY_STATISTICS_REPORT = "/report/statistics";
		String V1_KEY_STATISTICS_REPORT_DESC = "ivr按键统计报表";

		String V1_KEY_STATISTICS_REPORT_EXPORT = "/report/statistics/export";
		String V1_KEY_STATISTICS_REPORT_EXPORT_DESC = "ivr按键统计报表导出";

	}

	interface Duplication {
		String ROOT = V1 + "/stats";

		String V1_TOTAL_QUERY = "/duplication/query";
		String V1_TOTAL_QUERY_DESC = "客户重复来电总量查询";
		String V1_TOTAL_EXPORT = "/duplication/export";
		String V1_TOTAL_EXPORT_DESC = "客户重复来电总量导出";
		String V1_DETAIL_QUERY = "/duplication/detail/query";
		String V1_DETAIL_QUERY_DESC = "客户重复来电明细查询";
		String V1_DETAIL_EXPORT = "/duplication/detail/export";
		String V1_DETAIL_EXPORT_DESC = "客户重复来电明细导出";
	}

	interface TmkIntercept {
        String ROOT = V1 + "/intercept";
        String V1_INTERCEPT_QUERY = "/query";
        String V1_INTERCEPT_QUERY_DESC = "拦截规则查询";
        String V1_INTERCEPT_UPDATE = "/update";
        String V1_INTERCEPT_UPDATE_DESC = "拦截规则更新";
        String V1_INTERCEPT_ADD = "/add";
        String V1_INTERCEPT_ADD_DESC = "拦截规则新增";
        String V1_INTERCEPT_DELETE = "/delete";
        String V1_INTERCEPT_DELETE_DESC = "拦截规则删除";

        String V1_INTERCEPT_HISTORY = "/history";
        String V1_INTERCEPT_HISTORY_DESC = "拦截历史查询";
    }

	interface webchat {
		String ROOT = V1 + "/webchat";
		String V1_AUTH_PARAMS = "/auth/params";
		String V1_AUTH_PARAMS_DESC = "获取在线客服系统鉴权参数";
	}

	interface withhold {
		String ROOT = V1 + "/withhold";
		String V1_WITHHOLD_ADD = "/add";
		String V1_WITHHOLD_ADD_DESC = "添加代扣";


		String V1_WITHHOLD_QUERY = "/query";
		String V1_WITHHOLD_QUERY_DESC = "查询代扣记录";

		String V1_WITHHOLD_GROUPS_QUERY = "/query/groups";
		String V1_WITHHOLD_GROUPS_QUERY_DESC = "获取有记录的所有组";

		String V1_WITHHOLD_QUERY_EXPORT = "/query/export";
		String V1_WITHHOLD_QUERY_EXPORT_DESC = "导出代扣记录";

		String V1_WITHHOLD_AMOUNT = "/amount";
		String V1_WITHHOLD_AMOUNT_DESC = "根据代扣模式查询金额";

		String V1_WITHHOLD_REPORT = "/report";
		String V1_WITHHOLD_REPORT_DESC = "代扣报表";

		String V1_WITHHOLD_REPORT_EXPORT = "/report/export";
		String V1_WITHHOLD_REPORT_EXPORT_DESC = "代扣报表导出";
		
		
		String V1_GET_REPAY_MODE = "/repay-mode/query";
		String V1_GET_REPAY_MODE_DESC = "代扣方式查询";
		
		String V1_REPAY_CHANNEL_QUERY = "/repay-channel/query";
		String V1_REPAY_CHANNEL_QUERY_DESC = "还款通道查询";
	}

	interface Repay {
		String ROOT = V1 + "/repay";
		String V1_IMPORT_TEMPLATE = "/import/template";
		String V1_IMPORT_TEMPLATE_DESC = "导入客户uuid模板";
		String V1_QUERY = "/query";
		String V1_QUERY_DESC = "还款明细查询列表";
		String V1_IMPORT = "/import/file";
		String V1_IMPORT_DESC = "导入要查询的客户uuid列表";
		
		
		String V1_FILE_URL = "/file-url";
		String V1_FILE_URL_DESC = "获取下载链接";
	}

	interface CallbackSummary {
		String ROOT = V1 + "/callbackSummary";
		String V1_DICT_QUERY = "/dict/query";
		String V1_DICT_QUERY_DESC = "查询回电小结维护界面数据";

		String V1_DICT_UPDATE = "/dict/update";
		String V1_DICT_UPDATE_DESC = "更新回电小结维护界面数据";

		String V1_SAVE = "/save";
		String V1_SAVE_DESC = "保存小结";


		String V1_REPORT_QUERY = "/report/query";
		String V1_REPORT_QUERY_DESC = "报表查询";

		String V1_REPORT_EXPORT = "/report/export";
		String V1_REPORT_EXPORT_DESC = "报表导出";
	}


	interface Video {
		String ROOT = V1 + "/video";
		String V1_TASK = "/task";
		String V1_TASK_DESC = "查询录制任务";

		String V1_URL = "/url/{id}";
		String V1_URL_DESC = "获取视频链接";

		String V1_COLLECT = "/collect";
		String V1_COLLECT_DESC = "收藏";

		String V1_EXPORT = "/export";
		String V1_EXPORT_DESC = "导出";
	}

	interface H5RepayLink {
		String ROOT = V1 + "/h5-repay-link";
		String V1_AMOUNT = "/amount";
		String V1_AMOUNT_DESC = "根据代扣模式试算还款金额";

		String V1_GENERATE_URL = "/generate-url";
		String V1_GENERATE_URL_DESC = "生成短链接";

		String V1_IS_ALLOW_DIY = "/is-allow-diy";
		String V1_IS_ALLOW_DIY_DESC = "是否支持自定义还款";

		String V1_RECORD  = "/record";
		String V1_RECORD_DESC  = "h5还款链接发送记录";

		String V1_RECORD_GROUPS  = "/record/group";
		String V1_RECORD_GROUPS_DESC  = "发送记录包含的所有组";

		String V1_SEND  = "/send";
		String V1_SEND_DESC  = "发送h5还款链接";

		String V1_COUNT  = "/count";
		String V1_COUNT_DESC  = "h5还款链接发送数量";


		String V1_RECORD_EXPORT  = "/export";
		String V1_RECORD_EXPORT_DESC  = "导出";
		
		
		String V1_REPAY_REPORT = "/repay-report";
		String V1_REPAY_REPORT_DESC = "h5还款统计报表";

		String V1_REPAY_REPORT_EXPORT = "/repay-report/export";
		String V1_REPAY_REPORT_EXPORT_DESC = "h5还款统计报表导出";

		String V1_FINANCE_REPAYMENT_BALANCE = "/finance/balance";
		String V1_FINANCE_REPAYMENT_BALANCE_DESC = "查询结清金额";

		String V1_FINANCE_REPAYMENT_CHANNEL = "/finance/channel/code";
		String V1_FINANCE_REPAYMENT_CHANNEL_DESC = "查询通道";
	}

	interface Screen{
		String ROOT = V1 + "/screen";
		String V1_AGENT_OVERVIEW = "/agentOverview";
		String V1_AGENT_OVERVIEW_DESC = "坐席工作概览";

		String V1_TOP_10_QUESTION = "/top10Question";
		String V1_TOP_10_QUESTION_DESC = "top10提问问题";

		String V1_ZK_OVERVIEW = "/zkOverview";
		String V1_ZK_OVERVIEW_DESC = "中控数据概览";

		String V1_HOURS_CALL_INFO = "/hoursCallInfo";
		String V1_HOURS_CALL_INFO_DESC = "分时统计来电信息";

		String V1_SERVICE_LEVEL = "/serviceLevel";
		String V1_SERVICE_LEVEL_DESC = "整体服务指标";


		String V1_CALL_MAP = "/callMap";
		String V1_CALL_MAP_DESC= "来电分布图";

		String V1_AGENT_EFFICIENT= "/agentEfficient";
		String V1_AGENT_EFFICIENT_DESC= "坐席工作效率";

		String V1_HOURS_REPEAT_DATA = "/repeat-data";
		String V1_HOURS_REPEAT_DATA_DESC = "分时查询总转人工服务数、2小时内重复进线量、重复进线率";

		String V1_PERSONAL_PANEL_QUERY = "/personal-panel";
		String V1_PERSONAL_PANEL_QUERY_DESC = "个人看板查询";

	}

	interface EarlySettled {
		String ROOT = V1 + "/early-settled";

		String USER_SUMMARY = "/user-summary";
		String USER_SUMMARY_DESC = "结清用户统计";

		String REASON_TENOR_SUMMARY = "/reason-tenor-summary";
		String REASON_TENOR_SUMMARY_DESC = "原因期数统计";


		String ORIGIN_PARTNER_SUMMARY = "/origin-partner-summary";
		String ORIGIN_PARTNER_SUMMARY_DESC = "渠道资金方统计";
	}


	interface BlackList{
		String ROOT = V1 + "/black-list";

		String QUERY = "/query";
		String QUERY_DESC = "查询黑名单列表";

		String QUERY_APPROVAL = "/query/approval";
		String QUERY_APPROVAL_DESC = "查询待审批黑名单列表";

		String ADD = "/add";
		String ADD_DESC = "添加黑名单";


		String DETAIL = "/detail";
		String DETAIL_DESC = "查询黑名单明细";

		String UPDATE = "/update";
		String UPDATE_DESC = "更新黑名单";

		String DELETE = "/delete";
		String DELETE_DESC = "删除黑名单";

		String IMPORT = "/import";
		String IMPORT_DESC = "导入黑名单";

		String EXPORT = "/export";
		String EXPORT_DESC = "导出黑名单";

		String TEMPLATE = "/template";
		String TEMPLATE_DESC = "获取黑名单导入模板";


		String CONTACT = "/contact";
		String CONTACT_DESC = "获取未加密的联系人信息";

		String OPERATE_RECORD = "/operate-record";
		String OPERATE_RECORD_DESC = "操作记录";

		String SYNC_DICT = "/sync-dict";
		String SYNC_DICT_DESC = "同步停催原因字典到催收";

		String DETAIL_REPORT = "/detail-report";
		String DETAIL_REPORT_DESC = "黑名单明细报表";

		String DETAIL_REPORT_EXPORT = "/detail-report/export";
		String DETAIL_REPORT_EXPORT_DESC = "黑名单明细报表导出";

		String SUMMARY_REPORT = "/summary-report";
		String SUMMARY_REPORT_DESC = "黑名单统计报表";
	}
	
	interface CalloutBlackList {
		String ROOT = V1 + "/callout-blacklist";
		
		String ADD = "/add";
		String ADD_DESC = "添加外呼黑名单";
		
		String QUERY = "/query";
		String QUERY_DESC = "查询外呼黑名单";
		
		
		String APPROVAL = "/approval";
		String APPROVAL_DESC = "审批外呼黑名单";
		
		String EXPORT_ALL = "/export-all";
		String EXPORT_ALL_DESC = "导出全部";
		
		
		String CANCEL = "/cancel";
		String CANCEL_DESC = "取消拉黑";
		
		String INTERCEPTION_RULE_ADD = "/interception-rules/add";
		String INTERCEPTION_RULE_ADD_DESC = "添加拦截规则";
		
		String INTERCEPTION_RULE_UPDATE = "/interception-rules/update";
		String INTERCEPTION_RULE_UPDATE_DESC = "更新拦截规则";
		
		String INTERCEPTION_RULE_QUERY = "/interception-rules/query";
		String INTERCEPTION_RULE_QUERY_DESC = "查询拦截规则";
		
		
		String INTERCEPTION_RULE_DELETE = "/interception-rules/delete";
		String INTERCEPTION_RULE_DELETE_DESC = "删除拦截规则";
		
		
		String CHECK_BLACK_LIST = "/check";
		String CHECK_BLACK_LIST_DESC = "判断是否外呼拦截";
	}
	
	interface Reduce {
		String ROOT = V1 + "/reduce";
		
		String SUBMIT_DATA_QUERY = "/submit-data/query";
		String SUBMIT_DATA_QUERY_DESC = "提交界面查询数据";
		
		
		String REDUCIBLE_AMOUNT_QUERY = "/reducible-amount/query";
		String REDUCIBLE_AMOUNT_QUERY_DESC = "可减免金额计算";
		
		
		String IRR_CALCULATE = "/irr-calculate";
		String IRR_CALCULATE_DESC = "irr计算";
		
		
		String REDUCE_REFUND_SUBMIT = "/submit";
		String REDUCE_REFUND_SUBMIT_DESC = "减免、退款申请提交";
		
		String RECORD_APPROVAL = "/approval";
		String RECORD_APPROVAL_DESC = "减免、退款申请审批";
		String RECORD_QUERY = "/query";
		String RECORD_QUERY_DESC = "减免、退款申请查询";

		String RECORD_DETAIL_QUERY = "/detail";
		String RECORD_DETAIL_QUERY_DESC = "减免退款详情查询";
		
		String DETAIL_REPORT_QUERY = "/report/detail/query";
		String DETAIL_REPORT_QUERY_DESC = "减免退款明细报表查询";

		String DETAIL_REPORT_EXPORT = "/report/detail/export";
		String DETAIL_REPORT_EXPORT_DESC = "减免退款明细报表导出";


		String STATISTIC_REPORT_QUERY = "/report/statistic/query";
		String STATISTIC_REPORT_QUERY_DESC = "减免退款统计报表查询";


		String STATISTIC_REPORT_EXPORT = "/report/statistic/export";
		String STATISTIC_REPORT_EXPORT_DESC = "减免退款统计报表导出";


		String HISTORY_REDUCED_RECORD = "/history/record";
		String HISTORY_REDUCED_RECORD_DESC = "历史减免记录查询";

		String REDUCED_APPLY_NAME = "/apply/name";
		String REDUCED_APPLY_NAME_DESC = "申请人统计";
		
		String UPLOAD_ATTACHMENT = "/attachment/upload";
		String UPLOAD_ATTACHMENT_DESC = "减免上传附件";

		String GET_ATTACHMENT = "/attachment/get";
		String GET_ATTACHMENT_DESC = "获取附件下载地址";

		String DEL_ATTACHMENT = "/attachment/del";
		String DEL_ATTACHMENT_DESC = "删除附件";
		
		String CHECK = "/check";
		String CHECK_DESC = "校验贷款号是否支持减免";
	}
	
	interface BlackProduction {
		String ROOT = "/black-production";
		
		String ADD = "/add";
		String ADD_DESC = "添加黑产信息";

		String QUERY = "/query";
		String QUERY_DESC = "查询黑产信息";
		
		String EXPORT = "/export";
		String EXPORT_DESC = "导出黑产信息";
		
		String TEMPLATE_EXPORT = "/template/export";
		String TEMPLATE_EXPORT_DESC = "导出模板";
		
		String IMPORT = "/import";
		String IMPORT_DESC = "导入黑产数据";
		
		
		String APPROVE = "/approve";
		String APPROVE_DESC = "审批";
		
	}

	interface Tools{
		String ROOT = V1 + "/tools";

		String TIPS = "/tips";
		String TIPS_DESC = "菜单提示";
	}
	
	
	interface PartnerInfo{
		String ROOT = V1 + "/partner-info";
		
		String IMPORT = "/import";
		String IMPORT_DESC = "导入";

		String ADD = "/add";
		String ADD_DESC = "新增";

		String QUERY = "/query";
		String QUERY_DESC = "查询";
		
		
		String UPDATE = "/update";
		String UPDATE_DESC = "更新";
		
		String EXPORT = "/export";
		String EXPORT_DESC = "导出";
		
		String QUERY_MSG = "query-msg";
		String QUERY_MSG_DESC = "在途贷款根据资金方名称跟资金方编码查询资金方信息";
	}
	
	interface StaffMonitor {
		String ROOT = V1 + "/staff-monitor";
		String REPORT_QUERY = "/report/query";
		String REPORT_QUERY_DESC = "员工监控报表查询";

		String REPORT_EXPORT = "/report/export";
		String REPORT_EXPORT_DESC = "员工监控报表导出";
	}
	
	interface Manual {
		String ROOT = V1 + "/manual";
		
		String IVR_JOB = "/ivr-job";
		String IVR_JOB_DESC = "IVR任务";
	}

	interface ReduceScheme {
		String ROOT = V1 + "/reduce/scheme";

		String QUERY = "/query";
		String QUERY_DESC = "查询减免方案";
	}
	
	interface FaceIdentify {
		String ROOT = V1 + "/face-identify";
		
		String UPLOAD_BASE_PHOTO = "/upload-base-photo";
		String UPLOAD_BASE_PHOTO_DESC = "上传底照";


		String QUERY_BASE_PHOTO = "/query-base-photo";
		String QUERY_BASE_PHOTO_DESC = "查询底照";
	}

	interface Login {
		String ROOT = V1 + "/login";

		String SUBMIT_LOGIN_RESULT = "/submit-login-result";
		String SUBMIT_LOGIN_RESULT_DESC = "提交登录结果";
		
		String QUERY_DETAIL_REPORT = "/detail-report/query";
		String QUERY_DETAIL_REPORT_DESC = "查询登录明细报表";
		
		String EXPORT_DETAIL_REPORT = "/detail-report/export";
		String EXPORT_DETAIL_REPORT_DESC = "导出登录明细报表";
		
	}
	
	interface SensitiveWorkorderConfig{
		String ROOT = V1 + "/sensitive-workorder-config";

		String QUERY = "/query";
		String QUERY_DESC = "查询敏感工单配置";

		String ADD = "/add";
		String ADD_DESC = "新增敏感工单配置";

		String UPDATE = "/update";
		String UPDATE_DESC = "更新敏感工单配置";

		String DELETE = "/delete";
		String DELETE_DESC = "删除敏感工单配置";
		
	}
	
	interface ContractSend {
		String ROOT = V1 + "/contract-send";
		
		String QUERY = "/query";
		String QUERY_DESC = "查询合同发送记录";
		
		String SEND = "/send";
		String SEND_DESC = "发送合同";
		
		String EXPORT = "/export";
		String EXPORT_DESC = "导出合同发送记录";
		
		String AUDIT = "/audit";
		String AUDIT_DESC = "审核合同发送记录";
		
		
		String QUERY_SEND_TYPE = "/query-send-type";
		String QUERY_SEND_TYPE_DESC = "查询合同发送类型";
	}

	 interface EXTERNAL_COMPLAINT {
		String ROOT = V1 + "/external-complaint";

		String V1_EXTERNAL_COMPLAINT_LIST = "/list";
		String V1_EXTERNAL_COMPLAINT_LIST_DESC = "投诉工单列表查询";

		String V1_EXTERNAL_COMPLAINT_ADD = "/add";
		String V1_EXTERNAL_COMPLAINT_ADD_DESC = "新增外部投诉";
		
		
		String V1_EXTERNAL_COMPLAINT_EXPORT = "/export";
		String V1_EXTERNAL_COMPLAINT_EXPORT_DESC = "导出外部投诉";
		
		String V1_EXTERNAL_COMPLAINT_DETAIL = "/detail/{id}";
		String V1_EXTERNAL_COMPLAINT_DETAIL_DESC = "外部投诉详情查询";
		
		String V1_EXTERNAL_COMPLAINT_CALL_LIST = "/call-list/{id}";
		String V1_EXTERNAL_COMPLAINT_CALL_LIST_DESC = "通话记录";
		
		String V1_EXTERNAL_COMPLAINT_LOG = "/log/{id}";
		String V1_EXTERNAL_COMPLAINT_LOG_DESC = "工单日志";
		
		String V1_GET_NAME_BY_ORDER_NO = "/name";
		String V1_GET_NAME_BY_ORDER_NO_DESC = "根据单号获取姓名";
		
		String V1_QUICK_ORDER_QUERY = "/quick-order/query";
		String V1_QUICK_ORDER_QUERY_DESC = "快速工单查询";
		
		
		String V1_EXTERNAL_LOGIN = "/external-login";
		String V1_EXTERNAL_LOGIN_DESC = "外部投诉登录";
		
		String V1_REMINDER_ORDER = "/reminder-order";
		String V1_REMINDER_ORDER_DESC = "催办工单";
		
		
		String V1_ORDER_UPDATE = "/order/update";
		String V1_ORDER_UPDATE_DESC = "工单更新";
		
		String V1_DICTINFO_QUERY = "/dictinfo/query";
		String V1_DICTINFO_QUERY_DESC = "查询字典信息";
		
		String V1_CAPTCHA = "/captcha";
		String V1_CAPTCHA_DESC = "获取验证码";

		 String V1_UPLOAD = "/upload";
		 String V1_UPLOAD_DESC = "上传文件";
		 
		 String V1_GET_FILE_URL = "/fileUrl";
		 String V1_GET_FILE_URL_DESC = "获取文件下载链接";
	 }

	/**
	 * 喵达投诉管理相关接口
	 */
	interface MiaodaComplaint {
		String V1_MIAODA = V1 + "/miaoda";

		// 投诉单管理
		String V1_COMPLAINT_LIST = "/complaint/list";
		String V1_COMPLAINT_LIST_DESC = "查询投诉单列表";
		String V1_COMPLAINT_REPLY = "/complaint/reply";
		String V1_COMPLAINT_REPLY_DESC = "回复投诉单";
		String V1_COMPLAINT_APPEAL = "/complaint/appeal";
		String V1_COMPLAINT_APPEAL_DESC = "申诉投诉单";
		String V1_COMPLAINT_APPEAL_STATUS = "/complaint/appeal-status";
		String V1_COMPLAINT_APPEAL_STATUS_DESC = "查询申诉状态";
		String V1_COMPLAINT_COMPLETE = "/complaint/complete";
		String V1_COMPLAINT_COMPLETE_DESC = "申请结案";
		String V1_COMPLAINT_CLAIM = "/complaint/claim";
		String V1_COMPLAINT_CLAIM_DESC = "投诉认领";
		String V1_COMPLAINT_SYNC = "/complaint/sync";
		String V1_COMPLAINT_SYNC_DESC = "手动同步投诉单";
		String V1_COMPLAINT_UPDATE_STATUS = "/complaint/update-status";
		String V1_COMPLAINT_UPDATE_STATUS_DESC = "更新投诉单状态";
		String V1_COMPLAINT_UPDATE_BY_SNS = "/complaint/update-by-sns";
		String V1_COMPLAINT_UPDATE_BY_SNS_DESC = "根据投诉单号更新投诉单";

		String V1_COMPLAINT_QUERY_BY_WORK_ORDER_NO = "/complaint/query-by-work-order-no";
		String V1_COMPLAINT_QUERY_BY_WORK_ORDER_NO_DESC = "根据工单编号查询投诉单";

		// 快速解决和套餐信息
		String V1_RAPID_SOLVE = "/rapid-solve";
		String V1_RAPID_SOLVE_DESC = "查询快速解决商家信息";
		String V1_PLAN_INFO = "/plan-info";
		String V1_PLAN_INFO_DESC = "查询套餐余量信息";

		// 投诉类型映射管理
		String V1_MAPPING_LIST = "/mapping/list";
		String V1_MAPPING_LIST_DESC = "分页查询投诉类型映射";
		String V1_MAPPING_GET = "/mapping/{id}";
		String V1_MAPPING_GET_DESC = "根据ID查询投诉类型映射";
		String V1_MAPPING_SAVE = "/mapping";
		String V1_MAPPING_SAVE_DESC = "保存投诉类型映射";
		String V1_MAPPING_UPDATE = "/mapping";
		String V1_MAPPING_UPDATE_DESC = "更新投诉类型映射";
		String V1_MAPPING_DELETE = "/mapping/{id}";
		String V1_MAPPING_DELETE_DESC = "删除投诉类型映射";
		String V1_MAPPING_BATCH_DELETE = "/mapping/batch";
		String V1_MAPPING_BATCH_DELETE_DESC = "批量删除投诉类型映射";
		String V1_MAPPING_TOGGLE = "/mapping/{id}/toggle";
		String V1_MAPPING_TOGGLE_DESC = "启用/禁用投诉类型映射";
		String V1_MAPPING_INIT = "/mapping/init";
		String V1_MAPPING_INIT_DESC = "初始化默认映射配置";
	}


}
