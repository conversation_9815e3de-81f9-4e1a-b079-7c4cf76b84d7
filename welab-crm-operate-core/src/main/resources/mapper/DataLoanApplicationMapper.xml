<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.DataLoanApplicationMapper">
    <select id="selectByAppNoList" resultType="com.welab.crm.operate.domain.DataLoanApplication">
        SELECT
            *
        FROM data_loan_application dla
        WHERE dla.wo_task_id = #{woTaskId}
        AND dla.application_id in
        <foreach item="item" index="index" collection="appNoList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
