<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.ComplaintTypeMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.ComplaintTypeMapping">
        <id column="id" property="id" />
        <result column="miaoda_issue" property="miaodaIssue" />
        <result column="op_dict_info_conf_id" property="opDictInfoConfId" />
        <result column="is_active" property="isActive" />
        <result column="priority" property="priority" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, miaoda_issue, op_dict_info_conf_id, is_active, priority, remark, create_by, update_by, gmt_create, gmt_modify
    </sql>

    <!-- 分页查询投诉类型映射列表 -->
    <select id="selectMappingPage" resultType="com.welab.crm.operate.vo.miaoda.ComplaintTypeMappingVO">
        SELECT
            ctm.id,
            ctm.miaoda_issue,
            ctm.op_dict_info_conf_id,
            ctm.is_active,
            CASE WHEN ctm.is_active = 1 THEN '启用' ELSE '禁用' END as is_active_desc,
            ctm.priority,
            ctm.remark,
            ctm.create_by,
            ctm.update_by,
            ctm.gmt_create,
            ctm.gmt_modify,
            odic.wo_type_detail as wo_type_name,
            odic.wo_type_fir_detail as wo_type_fir_name,
            odic.wo_type_sec_detail as wo_type_sec_name,
            odic.wo_type_thir_detail as wo_type_thir_name,
            odic.wo_type_child_detail as wo_type_child_name
        FROM complaint_type_mapping ctm
        LEFT JOIN op_dict_info_conf odic ON ctm.op_dict_info_conf_id = odic.id AND odic.is_status = 1
        <where>
            <if test="query.miaodaIssue != null and query.miaodaIssue != ''">
                AND ctm.miaoda_issue LIKE CONCAT('%', #{query.miaodaIssue}, '%')
            </if>
            <if test="query.opDictInfoConfId != null">
                AND ctm.op_dict_info_conf_id = #{query.opDictInfoConfId}
            </if>
            <if test="query.isActive != null">
                AND ctm.is_active = #{query.isActive}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                AND ctm.create_by = #{query.createBy}
            </if>
        </where>
        ORDER BY ctm.priority ASC, ctm.gmt_create DESC
    </select>

    <!-- 根据喵达投诉问题类型查询映射 -->
    <select id="selectByMiaodaIssue" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM complaint_type_mapping
        WHERE miaoda_issue = #{miaodaIssue}
        AND is_active = 1
        ORDER BY priority ASC
        LIMIT 1
    </select>

    <!-- 查询所有启用的映射配置 -->
    <select id="selectActiveMapping" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM complaint_type_mapping
        WHERE is_active = 1
        ORDER BY priority ASC, gmt_create ASC
    </select>

    <!-- 根据工单组合配置ID查询映射 -->
    <select id="selectByOpDictInfoConfId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM complaint_type_mapping
        WHERE is_active = 1
        AND op_dict_info_conf_id = #{opDictInfoConfId}
        ORDER BY priority ASC
    </select>

    <!-- 模糊匹配投诉问题类型 -->
    <select id="selectBestMatchMapping" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM complaint_type_mapping
        WHERE is_active = 1
        AND (
            miaoda_issue LIKE CONCAT('%', #{issue}, '%')
            OR #{issue} LIKE CONCAT('%', miaoda_issue, '%')
        )
        ORDER BY 
            CASE 
                WHEN miaoda_issue = #{issue} THEN 1
                WHEN miaoda_issue LIKE CONCAT(#{issue}, '%') THEN 2
                WHEN miaoda_issue LIKE CONCAT('%', #{issue}) THEN 3
                WHEN miaoda_issue LIKE CONCAT('%', #{issue}, '%') THEN 4
                WHEN #{issue} LIKE CONCAT('%', miaoda_issue, '%') THEN 5
                ELSE 6
            END,
            priority ASC,
            LENGTH(miaoda_issue) ASC
        LIMIT 1
    </select>

    <!-- 检查映射是否存在 -->
    <select id="existsByMiaodaIssue" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM complaint_type_mapping
        WHERE miaoda_issue = #{miaodaIssue}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>
    <select id="selectBestMatchMappingByList" resultType="com.welab.crm.operate.domain.ComplaintTypeMapping">
        SELECT <include refid="Base_Column_List" />
        FROM complaint_type_mapping
        WHERE miaoda_issue in
        <foreach collection="issueList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        AND is_active = 1
        ORDER BY priority ASC
        LIMIT 1
    </select>

</mapper>
