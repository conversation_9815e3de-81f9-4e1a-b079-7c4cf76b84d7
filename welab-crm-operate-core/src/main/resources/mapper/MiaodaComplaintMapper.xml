<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.operate.mapper.MiaodaComplaintMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.operate.domain.MiaodaComplaint">
        <id column="id" property="id" />
        <result column="sn" property="sn" />
        <result column="work_order_no" property="workOrderNo" />
        <result column="title" property="title" />
        <result column="nickname" property="nickname" />
        <result column="phone" property="phone" />
        <result column="comp_phone" property="compPhone" />
        <result column="privacy" property="privacy" />
        <result column="content" property="content" />
        <result column="issue" property="issue" />
        <result column="appeal" property="appeal" />
        <result column="cost" property="cost" />
        <result column="status" property="status" />
        <result column="status_no" property="statusNo" />
        <result column="created_at" property="createdAt" />
        <result column="assigned_at" property="assignedAt" />
        <result column="completed_at" property="completedAt" />
        <result column="uri" property="uri" />
        <result column="exposed" property="exposed" />
        <result column="appeal_chance" property="appealChance" />
        <result column="co_complete_chance" property="coCompleteChance" />
        <result column="co_complete_status" property="coCompleteStatus" />
        <result column="co_complete_at" property="coCompleteAt" />
        <result column="auto_complete_at" property="autoCompleteAt" />
        <result column="user_complete_at" property="userCompleteAt" />
        <result column="service" property="service" />
        <result column="attitude" property="attitude" />
        <result column="process" property="process" />
        <result column="satisfaction" property="satisfaction" />
        <result column="eval_content" property="evalContent" />
        <result column="eval_at" property="evalAt" />
        <result column="sync_status" property="syncStatus" />
        <result column="sync_fail_reason" property="syncFailReason" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="user_reply_count" property="userReplyCount" />
        <result column="reply_info" property="replyDetails" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="complete_info" property="coCompleteInfo"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="attachment_info" property="attaches"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="auto_reply_status" property="autoReplyStatus" />
        <result column="auto_reply_time" property="autoReplyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sn, work_order_no, title, nickname, phone, comp_phone, privacy, content, issue, appeal, cost, 
        status, status_no, created_at, assigned_at, completed_at, uri, exposed, appeal_chance, co_complete_chance, 
        co_complete_status, co_complete_at, auto_complete_at, user_complete_at, service, attitude, process, 
        satisfaction, eval_content, eval_at, sync_status, sync_fail_reason, gmt_create, gmt_modify, user_reply_count,
          reply_info,complete_info,attachment_info,auto_reply_status,auto_reply_time
    </sql>

    <!-- 分页查询喵达投诉单列表 -->
    <select id="selectComplaintPage" resultType="com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO">
        SELECT 
            <include refid="Base_Column_List" />
        FROM miaoda_complaint
        <where>
            <if test="query.sn != null and query.sn != ''">
                AND sn = #{query.sn}
            </if>
            <if test="query.workOrderNo != null and query.workOrderNo != ''">
                AND work_order_no = #{query.workOrderNo}
            </if>
            <if test="query.nickname != null and query.nickname != ''">
                AND nickname LIKE CONCAT('%', #{query.nickname}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND (phone LIKE CONCAT('%', #{query.phone}, '%') OR comp_phone LIKE CONCAT('%', #{query.phone}, '%'))
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.syncStatus != null">
                AND sync_status = #{query.syncStatus}
            </if>
            <if test="query.st != null">
                AND assigned_at >= FROM_UNIXTIME(#{query.st} / 1000)
            </if>
            <if test="query.et != null">
                AND assigned_at &lt;= FROM_UNIXTIME(#{query.et} / 1000)
            </if>
        </where>
        ORDER BY gmt_create DESC
    </select>

    <!-- 根据投诉单号查询投诉单 -->
    <select id="selectBySn" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        WHERE sn = #{sn}
        LIMIT 1
    </select>

    <!-- 根据工单编号查询投诉单 -->
    <select id="selectByWorkOrderNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        WHERE work_order_no = #{workOrderNo}
    </select>

    <!-- 查询需要同步的投诉单 -->
    <select id="selectNeedSyncComplaints" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        WHERE status not in ('投诉已完成','投诉已关闭','投诉已移除')
        ORDER BY gmt_create ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新投诉单状态 -->
    <update id="batchUpdateStatus">
        <foreach collection="complaints" item="complaint" separator=";">
            UPDATE miaoda_complaint 
            SET status = #{complaint.status},
                status_no = #{complaint.statusNo},
                completed_at = #{complaint.completedAt},
                co_complete_status = #{complaint.coCompleteStatus},
                co_complete_at = #{complaint.coCompleteAt},
                attitude = #{complaint.attitude},
                process = #{complaint.process},
                satisfaction = #{complaint.satisfaction},
                eval_content = #{complaint.evalContent},
                eval_at = #{complaint.evalAt},
                sync_status = #{complaint.syncStatus},
                gmt_modify = NOW()
            WHERE sn = #{complaint.sn}
        </foreach>
    </update>
    <update id="updateWorkOrderNoBySn">
        update miaoda_complaint
        set work_order_no = #{workOrderNo}
        where sn = #{sn}
    </update>

    <!-- 根据手机号查询相关投诉单 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        WHERE phone = #{phone} OR comp_phone = #{phone}
        ORDER BY gmt_create DESC
    </select>

    <!-- 查询指定时间范围内的投诉单 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        <where>
            <if test="startTime != null and startTime != ''">
                AND assigned_at >= STR_TO_DATE(#{startTime}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="endTime != null and endTime != ''">
                AND assigned_at &lt;= STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        ORDER BY assigned_at ASC
    </select>

    <!-- 统计投诉单数量 -->
    <select id="countComplaints" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM miaoda_complaint
        <where>
            <if test="query.sn != null and query.sn != ''">
                AND sn = #{query.sn}
            </if>
            <if test="query.workOrderNo != null and query.workOrderNo != ''">
                AND work_order_no = #{query.workOrderNo}
            </if>
            <if test="query.nickname != null and query.nickname != ''">
                AND nickname LIKE CONCAT('%', #{query.nickname}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND (phone LIKE CONCAT('%', #{query.phone}, '%') OR comp_phone LIKE CONCAT('%', #{query.phone}, '%'))
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.syncStatus != null">
                AND sync_status = #{query.syncStatus}
            </if>
            <if test="query.st != null and query.st != ''">
                AND assigned_at >= STR_TO_DATE(#{query.st}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.et != null and query.et != ''">
                AND assigned_at &lt;= STR_TO_DATE(#{query.et}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>

    <!-- 批量根据投诉单号查询工单号 -->
    <select id="selectWorkOrderNosBySns" resultType="java.util.Map">
        SELECT sn, work_order_no as workOrderNo
        FROM miaoda_complaint
        WHERE sn IN
        <foreach collection="sns" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
        AND work_order_no IS NOT NULL
        AND work_order_no != ''
    </select>
    <select id="selectBySns" resultType="com.welab.crm.operate.domain.MiaodaComplaint">
        SELECT <include refid="Base_Column_List" />
        FROM miaoda_complaint
        WHERE sn IN
        <foreach collection="sns" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>

</mapper>
