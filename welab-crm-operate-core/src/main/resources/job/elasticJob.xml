<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:reg="http://www.dangdang.com/schema/ddframe/reg"
  xmlns:job="http://www.dangdang.com/schema/ddframe/job"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd
                        http://www.dangdang.com/schema/ddframe/reg
                        http://www.dangdang.com/schema/ddframe/reg/reg.xsd
                        http://www.dangdang.com/schema/ddframe/job
                        http://www.dangdang.com/schema/ddframe/job/job.xsd">

  <reg:zookeeper id="regCenter" server-lists="${job.registry.address}" namespace="welab-crm-operate-job"
    base-sleep-time-milliseconds="10000" max-sleep-time-milliseconds="30000" max-retries="3"/>

  <job:simple id="ivrRateCalJob" class="com.welab.crm.operate.job.IvrRateCalJob"
    reconcile-interval-minutes="10"
    registry-center-ref="regCenter"
    sharding-total-count="1"
    cron="${job.ivr.shunt.cron}"
    sharding-item-parameters="0=0"
    monitor-execution="false"
    monitor-port="9890"
    failover="true"
    description="IVR分流率定时计算任务"
    overwrite="true"
    disabled="false"/>


  <job:simple id="orderUrgeMonitorJob" class="com.welab.crm.operate.job.OrderUrgeMonitorJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.urge.order.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9891"
              failover="true"
              description="催单定时提醒任务"
              overwrite="true"
              disabled="false"/>

  <job:simple id="orderCallbackMonitorJob" class="com.welab.crm.operate.job.OrderCallbackMonitorJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.callback.order.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9892"
              failover="true"
              description="工单回访通知任务"
              overwrite="true"
              disabled="false"/>

  <!-- 喵达投诉单同步定时任务 -->
  <job:simple id="miaodaComplaintSyncJob" class="com.welab.crm.operate.job.MiaodaComplaintSyncJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.miaoda.sync.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9893"
              failover="true"
              description="喵达投诉单同步定时任务，每日10点和16点自动从喵达平台拉取新投诉单"
              overwrite="true"
              disabled="false"/>

  <!-- 喵达投诉单状态更新定时任务 -->
  <job:simple id="miaodaComplaintUpdateJob" class="com.welab.crm.operate.job.MiaodaComplaintUpdateJob"
              reconcile-interval-minutes="10"
              registry-center-ref="regCenter"
              sharding-total-count="1"
              cron="${job.miaoda.update.cron}"
              sharding-item-parameters="0=0"
              monitor-execution="false"
              monitor-port="9894"
              failover="true"
              description="喵达投诉单状态更新定时任务，定时检查投诉单状态更新和催单信息"
              overwrite="true"
              disabled="false"/>

</beans>
