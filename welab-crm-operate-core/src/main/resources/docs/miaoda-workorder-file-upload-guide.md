# 喵达工单文件上传接口使用指南

## 概述
本文档说明喵达工单文件上传接口的使用方法，该接口专门用于上传与喵达投诉单和工单相关的文件，支持多种文件格式，文件链接有效期为1年。

## 接口信息

### 1. 文件上传接口
```
POST /v1/miaoda/complaint/workorder/upload
```

### 2. 获取文件下载链接接口
```
GET /v1/miaoda/complaint/workorder/file-url
```

## 文件上传接口详情

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| file | MultipartFile | 是 | 上传的文件 | - |
| complaintSn | String | 否 | 关联的投诉单号 | *********** |
| workOrderNo | String | 否 | 关联的工单编号 | WO2025012400001 |
| description | String | 否 | 文件描述（最大200字符） | 投诉相关证明材料 |
| category | String | 否 | 文件分类（最大50字符） | complaint_evidence |
| remark | String | 否 | 上传人备注（最大500字符） | 客户提供的相关证明文件 |
| isPublic | Boolean | 否 | 是否公开文件 | false |
| expireDays | Integer | 否 | 文件有效期（天数，默认365天） | 365 |

### 文件限制
- **文件大小**：最大50MB
- **有效期**：1-3650天（默认365天，即1年）
- **支持的文件类型**：
  - 图片：jpg, jpeg, png, gif, bmp, webp, svg
  - 文档：pdf, doc, docx, xls, xlsx, ppt, pptx, txt, rtf
  - 压缩文件：zip, rar, 7z, tar, gz
  - 音视频：mp3, wav, mp4, avi, mov, wmv, flv
  - 其他：xml, json, csv

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": "file123456789",
    "originalFileName": "证明材料.pdf",
    "storedFileName": "20250124_证明材料.pdf",
    "downloadUrl": "https://storage.example.com/download/file123456789?expires=1737724800",
    "fileSize": 1048576,
    "fileType": "application/pdf",
    "fileExtension": "pdf",
    "description": "投诉相关证明材料",
    "category": "complaint_evidence",
    "complaintSn": "***********",
    "workOrderNo": "WO2025012400001",
    "businessType": "miaoda_complaint",
    "isPublic": false,
    "remark": "客户提供的相关证明文件",
    "status": "SUCCESS",
    "uploadTime": "2025-01-24T10:30:00",
    "expireTime": "2026-01-24T10:30:00",
    "createTime": "2025-01-24T10:30:00"
  }
}
```

## 获取文件下载链接接口详情

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| fileId | String | 是 | 文件ID | file123456789 |
| expireSeconds | Long | 否 | 链接有效期（秒，默认1年） | 31536000 |

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": "https://storage.example.com/download/file123456789?expires=1737724800"
}
```

## 使用示例

### 1. 使用curl上传文件
```bash
# 基本文件上传
curl -X POST "http://localhost:8080/v1/miaoda/complaint/workorder/upload" \
  -F "file=@/path/to/document.pdf" \
  -F "complaintSn=***********" \
  -F "description=投诉相关证明材料"

# 完整参数上传
curl -X POST "http://localhost:8080/v1/miaoda/complaint/workorder/upload" \
  -F "file=@/path/to/evidence.jpg" \
  -F "complaintSn=***********" \
  -F "workOrderNo=WO2025012400001" \
  -F "description=投诉证据图片" \
  -F "category=complaint_evidence" \
  -F "remark=客户提供的现场照片" \
  -F "isPublic=false" \
  -F "expireDays=365"
```

### 2. 使用JavaScript上传文件
```javascript
// 创建FormData对象
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('complaintSn', '***********');
formData.append('workOrderNo', 'WO2025012400001');
formData.append('description', '投诉相关证明材料');
formData.append('category', 'complaint_evidence');
formData.append('remark', '客户提供的相关证明文件');
formData.append('isPublic', 'false');
formData.append('expireDays', '365');

// 发送请求
fetch('/v1/miaoda/complaint/workorder/upload', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('文件上传成功:', data.data);
    console.log('下载链接:', data.data.downloadUrl);
  } else {
    console.error('文件上传失败:', data.message);
  }
})
.catch(error => {
  console.error('请求失败:', error);
});
```

### 3. 获取文件下载链接
```bash
# 获取文件下载链接
curl -X GET "http://localhost:8080/v1/miaoda/complaint/workorder/file-url?fileId=file123456789"

# 指定链接有效期（7天）
curl -X GET "http://localhost:8080/v1/miaoda/complaint/workorder/file-url?fileId=file123456789&expireSeconds=604800"
```

### 4. 前端文件上传组件示例
```html
<form id="uploadForm" enctype="multipart/form-data">
  <div>
    <label>选择文件:</label>
    <input type="file" name="file" id="fileInput" required>
  </div>
  <div>
    <label>投诉单号:</label>
    <input type="text" name="complaintSn" placeholder="***********">
  </div>
  <div>
    <label>工单编号:</label>
    <input type="text" name="workOrderNo" placeholder="WO2025012400001">
  </div>
  <div>
    <label>文件描述:</label>
    <input type="text" name="description" placeholder="投诉相关证明材料">
  </div>
  <div>
    <label>文件分类:</label>
    <select name="category">
      <option value="complaint_evidence">投诉证据</option>
      <option value="solution_proof">解决方案证明</option>
      <option value="communication_record">沟通记录</option>
    </select>
  </div>
  <div>
    <label>备注:</label>
    <textarea name="remark" placeholder="上传人备注"></textarea>
  </div>
  <div>
    <label>
      <input type="checkbox" name="isPublic" value="true">
      公开文件
    </label>
  </div>
  <div>
    <label>有效期（天）:</label>
    <input type="number" name="expireDays" value="365" min="1" max="3650">
  </div>
  <button type="submit">上传文件</button>
</form>

<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  
  fetch('/v1/miaoda/complaint/workorder/upload', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      alert('文件上传成功！');
      console.log('文件信息:', data.data);
    } else {
      alert('文件上传失败: ' + data.message);
    }
  })
  .catch(error => {
    alert('上传过程中发生错误: ' + error.message);
  });
});
</script>
```

## 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 400 | 上传文件不能为空 | 确保选择了文件 |
| 400 | 文件大小不能超过50MB | 压缩文件或选择较小的文件 |
| 400 | 不支持的文件类型 | 使用支持的文件格式 |
| 400 | 参数错误：文件描述不能超过200个字符 | 缩短文件描述 |
| 400 | 参数错误：文件有效期必须在1-3650天之间 | 设置合理的有效期 |
| 500 | 文件上传失败 | 检查网络连接和服务器状态 |

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误：文件大小不能超过50MB",
  "data": null
}
```

## 最佳实践

### 1. 文件命名规范
- 使用有意义的文件名
- 避免特殊字符和中文字符
- 包含日期或版本信息

### 2. 文件分类建议
- `complaint_evidence` - 投诉证据
- `solution_proof` - 解决方案证明
- `communication_record` - 沟通记录
- `legal_document` - 法律文件
- `technical_spec` - 技术规格

### 3. 安全考虑
- 验证文件类型和大小
- 不上传敏感信息
- 合理设置文件有效期
- 谨慎使用公开文件选项

### 4. 性能优化
- 压缩大文件后上传
- 使用适当的文件格式
- 避免重复上传相同文件
- 定期清理过期文件

## 注意事项

1. **文件有效期**：默认1年，过期后文件将无法访问
2. **存储空间**：合理控制文件大小，避免占用过多存储空间
3. **权限控制**：公开文件可被任何人访问，请谨慎设置
4. **关联关系**：建议至少提供投诉单号或工单编号之一
5. **备份策略**：重要文件建议本地备份
6. **监控告警**：关注上传失败率和存储使用情况
