package com.welab.crm.operate.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.operate.config.MiaodaApiConfig;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import com.welab.crm.operate.dto.miaoda.MiaodaLoginDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;

/**
 * 喵达Token管理器
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Component
public class MiaodaTokenManager {

    @Resource
    private MiaodaApiConfig miaodaApiConfig;

    @Resource
    private JedisCommands jedisCommands;

    @Resource
    private MiaodaApiClient miaodaApiClient;

    /**
     * 获取有效的Token
     *
     * @return Token字符串
     */
    public String getValidToken() {
        try {
            // 先从缓存获取
            String cachedToken = jedisCommands.get(MiaodaApiConstant.REDIS_TOKEN_KEY);
            if (StringUtils.isNotBlank(cachedToken)) {
                log.debug("从缓存获取到喵达Token");
                return cachedToken;
            }

            // 缓存中没有，重新获取
            return refreshToken();
        } catch (Exception e) {
            log.error("获取喵达Token失败", e);
            throw new RuntimeException("获取喵达Token失败: " + e.getMessage());
        }
    }

    /**
     * 刷新Token
     *
     * @return 新的Token
     */
    public String refreshToken() {
        try {
            log.info("开始刷新喵达Token");

            // 构建登录请求
            MiaodaLoginDTO loginDTO = new MiaodaLoginDTO();
            loginDTO.setUid(miaodaApiConfig.getUid());
            loginDTO.setKey(miaodaApiConfig.getKey());

            // 调用登录接口
            String response = miaodaApiClient.doGet(MiaodaApiConstant.LOGIN_PATH, JSON.toJSONString(loginDTO), null);
            
            if (StringUtils.isBlank(response)) {
                throw new RuntimeException("登录接口返回空响应");
            }

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                throw new RuntimeException("登录接口响应格式错误");
            }

            JSONObject status = result.getJSONObject("status");
            if (status == null || !Integer.valueOf(200).equals(status.getInteger("code"))) {
                String msg = status != null ? status.getString("msg") : "未知错误";
                throw new RuntimeException("登录失败: " + msg);
            }

            JSONObject data = result.getJSONObject("data");
            if (data == null) {
                throw new RuntimeException("登录接口未返回数据");
            }

            String token = data.getString("token");
            String expire = data.getString("expire");

            if (StringUtils.isBlank(token)) {
                throw new RuntimeException("登录接口未返回Token");
            }

            // 缓存Token（提前30分钟过期）
            int cacheTime = miaodaApiConfig.getTokenCacheTime() - 1800; // 减去30分钟
            jedisCommands.set(MiaodaApiConstant.REDIS_TOKEN_KEY, token);
            jedisCommands.expire(MiaodaApiConstant.REDIS_TOKEN_KEY, cacheTime);

            log.info("喵达Token刷新成功，过期时间: {}", expire);
            return token;

        } catch (Exception e) {
            log.error("刷新喵达Token失败", e);
            throw new RuntimeException("刷新喵达Token失败: " + e.getMessage());
        }
    }

    /**
     * 清除Token缓存
     */
    public void clearToken() {
        try {
            jedisCommands.del(MiaodaApiConstant.REDIS_TOKEN_KEY);
            log.info("已清除喵达Token缓存");
        } catch (Exception e) {
            log.error("清除喵达Token缓存失败", e);
        }
    }

    /**
     * 检查Token是否即将过期
     *
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon() {
        try {
            Long expire = jedisCommands.ttl(MiaodaApiConstant.REDIS_TOKEN_KEY);
            if (expire == null || expire <= 0) {
                return true;
            }
            // 如果剩余时间少于30分钟，认为即将过期
            return expire < 1800;
        } catch (Exception e) {
            log.error("检查Token过期时间失败", e);
            return true;
        }
    }

    /**
     * 获取Token剩余时间（秒）
     *
     * @return 剩余时间
     */
    public Long getTokenRemainingTime() {
        try {
            return jedisCommands.ttl(MiaodaApiConstant.REDIS_TOKEN_KEY);
        } catch (Exception e) {
            log.error("获取Token剩余时间失败", e);
            return 0L;
        }
    }
}
