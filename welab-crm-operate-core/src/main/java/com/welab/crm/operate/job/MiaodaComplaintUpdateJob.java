package com.welab.crm.operate.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.operate.config.MiaodaApiConfig;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import com.welab.crm.operate.service.MiaodaApiService;
import com.welab.crm.operate.service.MiaodaAutoReplyConfigService;
import com.welab.crm.operate.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;

/**
 * 喵达投诉单状态更新定时任务
 * 定时检查投诉单状态更新和催单信息
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Component
public class MiaodaComplaintUpdateJob implements SimpleJob {

    @Resource
    private MiaodaApiService miaodaApiService;

    @Resource
    private MiaodaApiConfig miaodaApiConfig;

    @Resource
    private MiaodaAutoReplyConfigService autoReplyConfigService;

    @Resource
    private JedisCommands jedisCommands;

    @Override
    public void execute(ShardingContext shardingContext) {
        if (!miaodaApiConfig.getEnabled() || !miaodaApiConfig.getAutoSyncEnabled()) {
            log.info("喵达API功能或自动同步功能已禁用，跳过更新任务");
            return;
        }

        String lockKey = MiaodaApiConstant.REDIS_SYNC_LOCK_KEY + ":update";
        try {
            // 获取分布式锁，防止重复执行
            boolean lockAcquired = RedisUtil.tryGetDistributedLock(jedisCommands, lockKey, "1",
                MiaodaApiConstant.LOCK_EXPIRE_MINUTES * 60); // 转换为秒

            if (!lockAcquired) {
                log.info("获取更新锁失败，可能有其他实例正在执行更新任务");
                return;
            }

            log.info("开始执行喵达投诉单状态更新任务");
            
            // 执行更新逻辑
            updateComplaintStatus();


            // 执行自动回复逻辑
            processAutoReply();
            
            log.info("喵达投诉单状态更新任务执行完成");

        } catch (Exception e) {
            log.error("执行喵达投诉单状态更新任务异常", e);
        } finally {
            // 释放锁
            try {
                RedisUtil.releaseDistributedLock(jedisCommands, lockKey);
            } catch (Exception e) {
                log.error("释放更新锁失败", e);
            }
        }
    }

    /**
     * 更新投诉单状态
     */
    private void updateComplaintStatus() {
        try {
            log.info("开始检查并更新投诉单状态");

            // 检查并更新投诉单状态
            int updateCount = miaodaApiService.checkAndUpdateComplaintStatus();
            log.info("本次更新投诉单状态数量：{}", updateCount);

        } catch (Exception e) {
            log.error("更新投诉单状态异常", e);
            throw e;
        }
    }



    /**
     * 处理自动回复逻辑
     */
    private void processAutoReply() {
        try {
            // 检查自动回复功能是否启用
            if (!autoReplyConfigService.isAutoReplyEnabled()) {
                log.debug("自动回复功能已禁用，跳过自动回复处理");
                return;
            }

            log.info("开始执行自动回复处理");

            // 执行自动回复
            int replyCount = miaodaApiService.processAutoReply();

            log.info("自动回复处理完成，处理数量：{}", replyCount);

        } catch (Exception e) {
            log.error("执行自动回复处理异常", e);
            // 不抛出异常，确保不影响同步任务的执行
        }
    }


    /**
     * 获取任务名称
     */
    public String getJobName() {
        return "MiaodaComplaintUpdateJob";
    }

    /**
     * 获取任务描述
     */
    public String getJobDescription() {
        return "喵达投诉单状态更新定时任务，定时检查投诉单状态更新和催单信息";
    }
}
