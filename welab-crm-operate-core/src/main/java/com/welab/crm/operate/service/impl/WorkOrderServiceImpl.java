package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.collection.interview.dto.complain.ComplainAttachmentDTO;
import com.welab.collection.interview.dto.complain.ComplainDTO;
import com.welab.collection.interview.enums.ComplainEnum;
import com.welab.collection.interview.vo.complain.ComplainDictVO;
import com.welab.common.response.Response;
import com.welab.common.utils.DateUtil;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.crm.base.dto.WoAssignLogDTO;
import com.welab.crm.base.enums.AssignTypeEnum;
import com.welab.crm.base.service.StaffService;
import com.welab.crm.base.service.WoAssignLogService;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.base.workflow.busi.constant.WFWorkorderStateEnum;
import com.welab.crm.base.workflow.busi.service.WFHisBusiService;
import com.welab.crm.base.workflow.busi.service.WFRunBusiService;
import com.welab.crm.base.workflow.common.constant.WFConstant;
import com.welab.crm.base.workflow.common.dto.args.WFExecuteDTO;
import com.welab.crm.base.workflow.common.dto.args.WFExecuteStartDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigNodeDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigTransitionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunLogDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunTaskDTO;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.dto.workorder.WorkOrderNoticeDTO;
import com.welab.crm.interview.service.ComplainOrderService;
import com.welab.crm.interview.service.IUploadService;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.util.BairongRequestVO;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.interview.vo.loan.LoanApplicationVO;
import com.welab.crm.interview.vo.loan.LoanDetailsVO;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.constant.WfVariableConstant;
import com.welab.crm.operate.domain.*;
import com.welab.crm.operate.dto.InitCodeConfigDTO;
import com.welab.crm.operate.dto.SensitiveWorkOrderDTO;
import com.welab.crm.operate.dto.notice.WoNoticeDTO;
import com.welab.crm.operate.dto.workorder.*;
import com.welab.crm.operate.enums.BlackProductionUserTypeEnum;
import com.welab.crm.operate.enums.OrderStatusEnum;
import com.welab.crm.operate.enums.StaffExtraStatusEnum;
import com.welab.crm.operate.exception.CrmOperateException;
import com.welab.crm.operate.mapper.*;
import com.welab.crm.operate.model.WorkOrderModel;
import com.welab.crm.operate.service.*;
import com.welab.crm.operate.util.*;
import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;
import com.welab.crm.operate.vo.online.OnlineFileRetVO;
import com.welab.crm.operate.vo.online.OnlineRes;
import com.welab.crm.operate.vo.staff.ColOrgInfoResVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.workorder.*;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.welab.crm.operate.util.DateUtils.YYYYMMDD_HHMM;

/**
 * 工单service服务
 * <AUTHOR>
 * @date 2021-10-26
 */
@Slf4j
@Service
public class WorkOrderServiceImpl implements WorkOrderService {

    @Resource
    private WoTaskMapper woTaskMapper;
    
    @Resource
    private DataCustomerMapper dataCustomerMapper;
    
    @Resource
    private DataLoanApplicationMapper dataLoanApplicationMapper;
    
    @Resource
    private WFRunBusiService runBusiService;

	@Resource
	private WFHisBusiService hisBusiService;

    @Resource
	private ICrmOrgService iCrmOrgService;
    
    @Resource
    private WoTaskSignMapper woTaskSignMapper;

    @Resource
	private StaffService staffService;

    @Resource
	private OpDictInfoConfMapper opDictInfoConfMapper;
    
    @Resource
	private NoticeMsgService noticeMsgService;

    @Resource
	private IUploadService uploadService;

    @Resource
	private WoAttachmentMapper woAttachmentMapper;
	
	@Resource
	private CsVideoCheckMapper csVideoCheckMapper;
	
	
	@Resource
	private BlackProductionAttachmentMapper blackProductionAttachmentMapper;

    @Resource
	private WoAppointCallbackMapper woAppointCallbackMapper;

    @Resource
	private WoReturnLogMapper woReturnLogMapper;

    @Resource
	private WoAssignLogService woAssignLogService;

	@Resource
	private ComplainOrderService complainOrderService;

    @Resource
	private EliteWorkorderHistoryMapper eliteWorkorderHistoryMapper;

    @Resource
	private EliteWorkorderLoansMapper eliteWorkorderLoansMapper;

    @Resource
	private EliteWorkorderLogsMapper eliteWorkorderLogsMapper;

    @Resource
	private EliteWorkorderAttachmentMapper eliteWorkorderAttachmentMapper;

    @Resource
	private OpDictInfoMapper opDictInfoMapper;

	@Resource
	private OpCustomerComplainMapper complainMapper;
	
	@Resource
	private OpStaffExtraStatusLogMapper opStaffExtraStatusLogMapper;
	
	@Resource
	private InAuthCrmStaffMapper staffMapper;
	
	@Resource
	private BlackProductionUserInfoMapper blackProductionUserInfoMapper;
	
	@Resource
	private OpTempFileMapper opTempFileMapper;
	
	@Resource
	private OnlineSystemService onlineSystemService;

	@Autowired
	private SensitiveWorkOrderService sensitiveWorkOrderService;
	
	@Resource
	private com.welab.crm.interview.service.WorkOrderService workOrderServiceInterview;
	
	@Resource
	private LoanApplicationService loanApplicationService;

	@Resource
	private ICrmOrgStaffService crmOrgStaffService;

	@Resource
	private UserInfoService userInfoService;

	@Resource
	private CustomerService customerService;

    @Value("${elite.attachment.download.url}")
	private String eliteUrl;

	/**
	 * 结案状态
	 */
	private static final List<String> ORDER_CLOSE_STATUS = new ArrayList<>(
			Arrays.asList("resolved_close", "unresolved_close", "not_connected_close"));


	@Value("${bairong.public.key}")
	private String bairongPublicKey;

	@Value("${bairong.private.key}")
	private String bairongPrivateKey;

	@Value("${bairong.aes.key}")
	private String bairiongAesKey;
	
	@Value("${bairong.request.domain}")
	private String bairongReuqestDomain;
	

	@Value("${online.system.http.url.pre}")
	private String onlineSystemUrlPre;
	
	
	private static final String BAIRONG_EXTERNAL_SOURCE = "bairong";

	/**
	 * 提单时，限制这些组的选择项
	 */
	private static final List<String> ORDER_SUBMIT_LIMIT_GROUPS = Collections.unmodifiableList(Arrays.asList("hrz","zxz"));


	/**
	 * 当为系统自动生成的工单时，提单人的loginName取这个
	 */
	private static final String DEFAULT_SUBMIT_ORDER_STAFF = "system";


	/**
	 * 文件过期时间 7 天
	 */
	private static final Long FILE_URL_EXPIRE_SECONDS = 60 * 60 * 24 * 7L;


	@Autowired
	private WoRuleInfoMapper woRuleInfoMapper;
	@Autowired
	private CallbackSummaryService callbackSummaryService;

	@Override
    public Page<WorkOrderInfoVO> queryWorkOrderList(WorkOrderSearchReqDTO reqDTO) {
        log.info("queryWorkOrderList,reqDTO:{}", JSON.toJSONString(reqDTO));
        
        WorkOrderModel workOrderModel = new WorkOrderModel();
		BeanUtils.copyProperties(reqDTO, workOrderModel);

		if (StringUtils.isNotBlank(workOrderModel.getGroupCode())){
			List<String> groupList = new ArrayList<>();
			groupList.add(workOrderModel.getGroupCode());
			groupList.add("gdz");
			workOrderModel.setGroupCodeList(groupList);
		}

		//分单时间
		if(StringUtils.isNotBlank(reqDTO.getDistributeStartDate())) {
			workOrderModel.setDistributeStartDate(DateUtils.parseDate(reqDTO.getDistributeStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getDistributeEndDate())) {
			workOrderModel.setDistributeEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getDistributeEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		//提单时间
		if(StringUtils.isNotBlank(reqDTO.getSubmitStartDate())) {
			workOrderModel.setSubmitStartDate(DateUtils.parseDate(reqDTO.getSubmitStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getSubmitEndDate())) {
			workOrderModel.setSubmitEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getSubmitEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		//申请时间
		if(StringUtils.isNotBlank(reqDTO.getApplyStartDate())) {
			workOrderModel.setApplyStartDate(DateUtils.parseDate(reqDTO.getApplyStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getApplyEndDate())) {
			workOrderModel.setApplyEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getApplyEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		//审批时间
		if(StringUtils.isNotBlank(reqDTO.getApprovalStartDate())) {
			workOrderModel.setApprovalStartDate(DateUtils.parseDate(reqDTO.getApprovalStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getApprovalEndDate())) {
			workOrderModel.setApprovalEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getApprovalEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		//确认时间
		if(StringUtils.isNotBlank(reqDTO.getConfirmStartDate())) {
			workOrderModel.setConfirmStartDate(DateUtils.parseDate(reqDTO.getConfirmStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getConfirmEndDate())) {
			workOrderModel.setConfirmEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getConfirmEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		//放款时间
		if(StringUtils.isNotBlank(reqDTO.getLoanStartDate())) {
			workOrderModel.setLoanStartDate(DateUtils.parseDate(reqDTO.getLoanStartDate(), DateUtils.YYYY_MM_DD));
		}
		if(StringUtils.isNotBlank(reqDTO.getLoanEndDate())) {
			workOrderModel.setLoanEndDate(DateUtils.addDays(DateUtils.parseDate(reqDTO.getLoanEndDate(), DateUtils.YYYY_MM_DD), 1));
		}
		if (StringUtils.isNotBlank(reqDTO.getNoContactTime())){
			String[] split = reqDTO.getNoContactTime().split("-");
			if (split.length == 2){
				workOrderModel.setNoContactTimeStart(Integer.valueOf(split[0]));
				workOrderModel.setNoContactTimeEnd(Integer.valueOf(split[1]));
			} else {
				workOrderModel.setNoContactTimeStart(Integer.valueOf(split[0]));
			}
		}
		Page<WorkOrderInfoVO> page = woTaskMapper
				.queryWorkOrderByPage(new Page<WorkOrderInfoVO>(reqDTO.getCurPage(), reqDTO.getPageSize()),
						workOrderModel);
		if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())){
			page.getRecords().parallelStream().forEach(item -> item.setMobile(SecurityUtil.maskMobile(item.getMobile())));
		}

		return page;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String submitWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO) {
		WFConfigAssignedDTO assignedDTO = reqDTO.getAssignedDTO();
		try {
			if (StringUtils.isBlank(reqDTO.getOrderNo())) {
				reqDTO.setOrderNo(SerialNoUtil.serialNo("WF"));
			}
			reqDTO.setStatus(OrderStatusEnum.SUBMIT.getValue());
			//保存工单信息
			saveOrUpdateWorkOrder(reqDTO, staffVO, 1);
			//工单流程
			InitCodeConfigDTO initDTO = new InitCodeConfigDTO();
			initDTO.setProcessCode(reqDTO.getProcessCode());
			// 进入流程发起页
			WFExecuteStartDTO executeStartArgs = new WFExecuteStartDTO();
			executeStartArgs.setCurGroupCode(staffVO.getGroupCode());
			executeStartArgs.setCurStaffId(staffVO.getId().toString());
			executeStartArgs.setBusiKey(reqDTO.getOrderNo());
			executeStartArgs.setProcessCode(reqDTO.getProcessCode());
			executeStartArgs.setAssignedStaff(assignedDTO);
			executeStartArgs.setTransition(reqDTO.getTransitionDTO());
			Map<String, Object> variables = new HashMap<String, Object>();
			variables.put(WfVariableConstant.BUSI_KEY, executeStartArgs.getBusiKey());
			variables.put(WfVariableConstant.DEPARTMENT, executeStartArgs.getCurGroupCode());
			variables.put(WfVariableConstant.IS_MANA_GROUP_MANAGER, isManaGroupManager(staffVO).toString());
			log.info(">>>>>>>>>业务代码对流程变量进行设值 variables={}", JSON.toJSONString(variables));
			executeStartArgs.setVariables(variables);
			JSONObject json = new JSONObject();
			json.put("opinion", reqDTO.getOpinion());
			json.put("callbackNote", reqDTO.getCallbackNote());
			executeStartArgs.setComment(json);
			// 发起流程并获得下步任务信息
			runBusiService.startExcute(executeStartArgs);
		} catch (Exception e) {
			log.error("submitWorkOrder error", e);
			throw new CrmOperateException("工单提交失败");
		}
		try {
			// 指定人员时需记录分单历史
			if (StringUtils.isNotBlank(assignedDTO.getStaffId())) {
				WoAssignLogDTO woAssignLogDTO = new WoAssignLogDTO();
				woAssignLogDTO.setCreateStaffId(staffVO.getId().toString());
				woAssignLogDTO.setStaffId(assignedDTO.getStaffId());
				woAssignLogDTO.setGroupCode(assignedDTO.getAssignedValue());
				woAssignLogDTO.setAssignType(AssignTypeEnum.ADJUST.getValue());
				woAssignLogDTO.setOrderNo(reqDTO.getOrderNo());
				woAssignLogService.assignLog(Arrays.asList(woAssignLogDTO));
			}
		}catch (Exception e){
			log.error("submitWorkOrder assign log error", e);
		}
		
		return reqDTO.getOrderNo();
	}

	@Override
	public List<WorkOrderComplainVO> getWorkOrderComplains(String workOrderNo) {
		List<OpCustomerComplain> complains = listOpCustomerComplains(workOrderNo);
		List<WorkOrderComplainVO> complainList = new ArrayList<>();
		// 去催收系统查询关于核实结果和处理结果的字典配置数据
		ComplainDictVO dictVo = getComplainDictVo();
		for (OpCustomerComplain complain : complains) {
			WorkOrderComplainVO vo = new WorkOrderComplainVO();
			BeanUtils.copyProperties(complain, vo);
			vo.setComplainLevel(ComplainEnum.getText(vo.getComplainLevel()));
			// 设置核实结果和处理结果
			setComplainResult(dictVo, vo);
			complainList.add(vo);
		}
		return complainList;
	}

	@Override
	public com.welab.xdao.context.page.Page<WorkOrderComplainVO> listComplains(WorkOrderComplainDTO reqDTO) {
		Page<OpCustomerComplain> page = new Page<>();
		page.setCurrent(reqDTO.getCurrentPage());
		page.setSize(reqDTO.getRowsPerPage());
		Page<WorkOrderComplainVO> complainsList = complainMapper.selectComplainsList(page, reqDTO);
		// 去催收系统查询关于核实结果和处理结果的字典配置数据
		ComplainDictVO dictVo = getComplainDictVo();
		for (WorkOrderComplainVO record : complainsList.getRecords()) {
			record.setComplainLevel(ComplainEnum.getText(record.getComplainLevel()));
			// 设置核实结果和处理结果
			setComplainResult(dictVo, record);
		}

		com.welab.xdao.context.page.Page<WorkOrderComplainVO> resultPage = new com.welab.xdao.context.page.Page<>();
		resultPage.setList(complainsList.getRecords());
		resultPage.setCurrentPage((int) complainsList.getCurrent());
		resultPage.setTotalPage((int) complainsList.getPages());
		resultPage.setRowsPerPage((int) complainsList.getSize());
		resultPage.setTotalRows((int) complainsList.getTotal());
		return resultPage;
	}

	@Override
	public List<WorkOrderComplainVO> exportComplains(WorkOrderComplainDTO reqDTO) {
		Page<OpCustomerComplain> page = new Page<>();
		page.setCurrent(1);
		page.setSize(500000);
		Page<WorkOrderComplainVO> complainsList = complainMapper.selectComplainsList(page, reqDTO);
		if (CollectionUtils.isEmpty(complainsList.getRecords())) {
			return Collections.emptyList();
		}

		// 去催收系统查询关于核实结果和处理结果的字典配置数据
		ComplainDictVO dictVo = getComplainDictVo();
		for (WorkOrderComplainVO record : complainsList.getRecords()) {
			record.setComplainLevel(ComplainEnum.getText(record.getComplainLevel()));
			record.setApproveStatusExport(record.getApproveStatus() == 0 ? "处理中" : "已完成");
			if (record.getReviewTime() != null) {
				record.setReviewTimeExport(DateUtils.formatDate(record.getReviewTime(), YYYYMMDD_HHMM));
			}
			if (record.getApproveTime() != null) {
				record.setApproveTimeExport(DateUtils.formatDate(record.getApproveTime(), YYYYMMDD_HHMM));
			}
			if (record.getGmtCreate() != null) {
				record.setGmtCreateExport(DateUtils.formatDate(record.getGmtCreate(), YYYYMMDD_HHMM));
			}
			// 设置核实结果和处理结果
			setComplainResult(dictVo, record);
		}
		return complainsList.getRecords();
	}

	@Override
	public void updateComplainUrgeStatus(List<String> orderNoList) {
		try {
			// 先推送到催收系统进行催促
			complainOrderService.updateComplainUrgeStatus(orderNoList);
			// 保存催促状态到客服系统本地数据库
			LambdaUpdateWrapper<OpCustomerComplain> wrapper = Wrappers.<OpCustomerComplain>lambdaUpdate()
					.in(OpCustomerComplain::getWorkOrderNo, orderNoList)
					.eq(OpCustomerComplain::getApproveStatus, 0);
			OpCustomerComplain complain = new OpCustomerComplain();
			complain.setUrgeStatus(Boolean.TRUE);
			complainMapper.update(complain, wrapper);
		} catch (Exception e) {
			log.warn("updateComplainUrgeStatus exception: {}", e.getMessage(), e);
			throw new CrmOperateException("催促状态推送到催收系统异常");
		}
	}

	/**
	 * 处理客户投诉相关的代码，即如果符合推送到催收系统的条件，则做推送且记录投诉的操作
	 */
	@Override
	@Async
	@Transactional(rollbackFor = Exception.class)
	public void saveWorkOrderComplain(WorkOrderSubmitReqDTO reqDTO, Long staffId, String staffName) {
		ComplainDTO complainDTO = null;
		try {
			// 组件需要的参数对象: 包括手机号、userId、合同号列表以及投诉相关内容
			complainDTO = setComplainLevelAndResult(reqDTO);
			// 投诉级别是正常，不需要核实和处理结果则不需要推送到催收系统
			if (ComplainEnum.NORMAL.getValue().equals(complainDTO.getComplainLevel()) && !complainDTO.getMustApprove()
					&& !complainDTO.getMustReview()) {
				return;
			}
			if (!StringUtils.isEmpty(reqDTO.getCustId())) {
				DataCustomer customer = dataCustomerMapper.selectById(reqDTO.getCustId());
				complainDTO.setUserId(String.valueOf(customer.getUserId()));
				complainDTO.setMobile(customer.getMobile());
			}
			List<WorkOrderLoanReqDTO> loanList = reqDTO.getLoanList();
			if (CollectionUtils.isNotEmpty(loanList)) {
				List<String> contractNoList = loanList.stream().map(WorkOrderLoanReqDTO::getApplicationId)
						.collect(Collectors.toList());
				complainDTO.setContractNoList(contractNoList);
			}

			List<ComplainAttachmentDTO> attachments = getComplainFilesDTO(reqDTO.getFileList(), staffId, staffName);
			complainDTO.setWorkOrderNo(reqDTO.getOrderNo());
			complainDTO.setContent(reqDTO.getDescription());
			complainDTO.setAttachments(attachments);
			complainDTO.setOrderTwoClass(opDictInfoMapper.selectById(reqDTO.getOrderTwoClass()).getContent());
			complainOrderService.pushComplainOrder(complainDTO);
		} catch (Exception e) {
			log.warn("推送或者保存客户投诉异常: {}, 参数:{}", e.getMessage(), complainDTO, e);
		}
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
	public void executeWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO) {
		WFConfigAssignedDTO assignedDTO = reqDTO.getAssignedDTO();
		WFRunExecutionDTO runExecution = reqDTO.getExecutionDTO();
        checkBackStaffIsExist(assignedDTO, reqDTO.getTransitionDTO());
		WFRunTaskDTO nextRunTaskDTO = null;
		try {
			//保存工单信息
			saveOrUpdateWorkOrder(reqDTO, staffVO, 2);
			WFConfigTransitionDTO transition = reqDTO.getTransitionDTO();
			WFExecuteDTO executeArgs = new WFExecuteDTO();
			executeArgs.setProcessCode(runExecution.getProcessCode());
			executeArgs.setExecutionId(runExecution.getExecutionId());
			executeArgs.setTaskId(runExecution.getCurrTask().getTaskId());
			executeArgs.setTransition(transition);
			runExecution.getVariables().put(WfVariableConstant.DEPARTMENT, assignedDTO.getAssignedValue());
			executeArgs.setVariables(runExecution.getVariables());
			executeArgs.setAssignedStaff(assignedDTO);
			JSONObject json = new JSONObject();
			json.put("opinion", reqDTO.getOpinion());
			json.put("callbackNote", reqDTO.getCallbackNote());
			executeArgs.setComment(json);
			// 记录退单历史
			if(WFWorkorderStateEnum.RETURN.getCode().equals(executeArgs.getTransition().getTargetState())){
				WoReturnLog woReturnLog = new WoReturnLog();
				woReturnLog.setOrderNo(reqDTO.getOrderNo());
				woReturnLog.setStaffId(runExecution.getPreStaffId());
				woReturnLog.setCreateStaffId(staffVO.getId().toString());
				woReturnLog.setRemark(reqDTO.getOpinion());
				woReturnLogMapper.insert(woReturnLog);
			}
			//工单流程
			nextRunTaskDTO = runBusiService.excuteTask(executeArgs);
		} catch (Exception e) {
			log.error("executeWorkOrder error", e);
			throw new CrmOperateException("工单流转失败");
		}

		// 工单扭转时不需要记录分单历史
//		try {
//			// 指定人员时需记录分单历史
//			if (StringUtils.isNotBlank(assignedDTO.getStaffId()) && !WFWorkorderStateEnum.RETURN.getCode()
//					.equals(reqDTO.getTransitionDTO().getTargetState())) {
//				WoAssignLogDTO woAssignLogDTO = new WoAssignLogDTO();
//				woAssignLogDTO.setCreateStaffId(staffVO.getId().toString());
//				woAssignLogDTO.setStaffId(assignedDTO.getStaffId());
//				woAssignLogDTO.setGroupCode(assignedDTO.getAssignedValue());
//				woAssignLogDTO.setAssignType(AssignTypeEnum.ADJUST.getValue());
//				woAssignLogDTO.setOrderNo(reqDTO.getOrderNo());
//				woAssignLogService.assignLog(Arrays.asList(woAssignLogDTO));
//			}
//		}catch (Exception e){
//			log.error("executeWorkOrder assign log error", e);
//		}
		//发送通知
		log.info("executeWorkOrder,assigne:{}",JSON.toJSONString(assignedDTO));
		log.info("executeWorkOrder nextRunTaskDTO:{}",JSON.toJSONString(nextRunTaskDTO));

		try {
			if (StringUtils.isNotBlank(assignedDTO.getStaffId())) {
				WoNoticeDTO woNoticeDTO = new WoNoticeDTO();
				woNoticeDTO.setTitle("工单流转");
				woNoticeDTO.setContent("工单流转");
				StaffVO staffVo = staffService.getStaffByStaffId(assignedDTO.getStaffId());
				woNoticeDTO.setReceiver(staffVo.getLoginName());
				woNoticeDTO.setNoticeType(nextRunTaskDTO.getConfigNode().getNoticeType());
				woNoticeDTO.setMobile(staffVo.getStaffMobile());
				noticeMsgService.workOrderNoticeByType(woNoticeDTO);
			}
		} catch (Exception e) {
			log.error("executeWorkOrder 发送工单消息异常, executionId={}", runExecution.getExecutionId(), e);
		}
		
		externalOrderCallback(reqDTO.getOrderNo(),reqDTO.getOpinion());
	}

	/**
	 * 外部工单回调
	 * @param orderNo
	 * @param opinion
	 */
	private void externalOrderCallback(String orderNo, String opinion) {
		CompletableFuture.runAsync(() -> {
			WoTask woTask = woTaskMapper.selectOne(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, orderNo));
			if (Objects.isNull(woTask) || StringUtils.isBlank(woTask.getExternalSource())) {
				return;
			}
			if (BAIRONG_EXTERNAL_SOURCE.equals(woTask.getExternalSource())) {
				BairongWorkOrderCallbackDTO callbackDTO = new BairongWorkOrderCallbackDTO();
				callbackDTO.setUniqueOrderNo(orderNo);
				callbackDTO.setInformation(opinion);
				callbackDTO.setCallbackTime(DateUtil.dateToString(new Date()));
				BairongRequestVO<BairongWorkOrderCallbackDTO> requestVO = new BairongRequestVO<>(callbackDTO, bairongPrivateKey, bairiongAesKey);
				log.info("externalOrderCallback start, req:{}", JSON.toJSONString(requestVO));
				String res = HttpClientUtil.postJson(bairongReuqestDomain + "/customerManager/api/wolaishuke/callback", JSON.toJSONString(requestVO));
				log.info("externalOrderCallback end, res:{}", res);
			}
		});


	}

	private void checkBackStaffIsExist(WFConfigAssignedDTO assignedDTO, WFConfigTransitionDTO transitionDTO) {
		// 如果是退回操作
        if (Objects.nonNull(transitionDTO) && "1".equals(transitionDTO.getBackFlag())) {
	        String staffId = assignedDTO.getStaffId();
	        InAuthCrmStaff staff = staffMapper.selectById(Long.parseLong(staffId));
			if (Objects.isNull(staff) || 0 == (staff.getIsStatus())){
				throw new FastRuntimeException("提单人已离职");
			}
        }
    }

	@Override
	public WFConfigNodeDTO getInitNodeConfig(InitCodeConfigDTO reqDTO, StaffVO staffVO) {
		Map<String, Object> variable = new HashMap<>();
		variable.put(WfVariableConstant.IS_MANA_GROUP_MANAGER, isManaGroupManager(staffVO).toString());
		WFConfigNodeDTO configNode = runBusiService.getInitNodeConfig(reqDTO.getProcessCode(), variable);
		return configNode;
	}
    
    @Override
    public WFRunExecutionDTO getTaskNodeInfo(WFRunTaskDTO reqDTO){
    	WFRunExecutionDTO runExecution = runBusiService.getTaskNodeInfo(reqDTO.getExecutionId(), reqDTO.getTaskId());
    	return runExecution;
    }
    
    @Override
    public WFConfigAssignedDTO getAssignedStaffId(AssignedStaffIdDTO reqDTO) {
    	WFConfigAssignedDTO assign = runBusiService.getAssignedStaffId(reqDTO.getExecutionId(), reqDTO.getProcessCode(),
                		reqDTO.getNodeCode(), reqDTO.getTarnsBackFlag(), reqDTO.getCurStaffId());
	    ColOrgInfoResVO org = iCrmOrgService.getOrgInfoByCode(CommonUtils.getCurrentloggedOrg());
		if (Objects.nonNull(org) && "nwoWorkOrder".equals(reqDTO.getNodeCode())) {
			if (ORDER_SUBMIT_LIMIT_GROUPS.contains(org.getPcode())){
				filterAssign(assign);
			}
		}
	    return assign;
    }

	private void filterAssign(WFConfigAssignedDTO assign) {
		if (Objects.isNull(assign)) {
			return;
		}
		WFConfigAssignDTO dto = assign.getAssigns().stream()
				.filter(item -> "gdz".equals(item.getAssignValue()))
				.findFirst().get();
		dto.setPendingStaffs(Collections.emptyMap());
		assign.setAssigns(Collections.singletonList(dto));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
    public void saveWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO) {
    	try {
			saveOrUpdateWorkOrder(reqDTO, staffVO, 3);
			WFRunLogDTO logDto = new WFRunLogDTO();
			WFRunExecutionDTO runExecution = reqDTO.getExecutionDTO();
			logDto.setExecutionId(runExecution.getExecutionId());
			logDto.setTaskId(runExecution.getCurrTask().getTaskId());
			logDto.setStaffId(runExecution.getCurrStaffId());
			logDto.setNodeCode(runExecution.getCurrNodeCode());
			JSONObject json = new JSONObject();
			json.put("opinion", reqDTO.getOpinion());
			json.put("callbackNote", reqDTO.getCallbackNote());
			logDto.setComment(json);
			logDto.setOperateType("保存");
			runBusiService.tempStore(logDto);
		}catch (Exception e){
    		log.error("saveWorkOrder error. {}", reqDTO.toString(), e);
			throw new CrmOperateException("工单保存失败");
		}

	    externalOrderCallback(reqDTO.getOrderNo(), reqDTO.getOpinion());

    }
    
    @Override
    public List<WorkOrderCountVO> queryWorkCountList(WorkOrderSearchReqDTO reqDTO) {
        log.info("queryWorkCountList,reqDTO:{}", JSON.toJSONString(reqDTO));
        return woTaskMapper.queryWorkCountList(reqDTO);
    }
    
    @Override
    public WorkOrderTotalVO totalWorkOrder(WorkOrderTotalReqDTO reqDTO) {
        log.info("totalWorkOrder,reqDTO:{}", JSON.toJSONString(reqDTO));
        WorkOrderTotalVO vo = new WorkOrderTotalVO();
        //总工单量
        reqDTO.setStatus(null);
        vo.setTotalCount(woTaskMapper.totalWorkOrder(reqDTO));
        //待处理
        reqDTO.setStatus(OrderStatusEnum.PENDING.getValue());
        vo.setPendingCount(woTaskMapper.totalWorkOrder(reqDTO));
        //处理中
        reqDTO.setStatus(OrderStatusEnum.PROCESS.getValue());
        vo.setProcessingCount(woTaskMapper.totalWorkOrder(reqDTO));
        //已流转
        reqDTO.setStatus(OrderStatusEnum.FLOW.getValue());
        vo.setMovingCount(woTaskMapper.totalWorkOrder(reqDTO));
        //已过期
        reqDTO.setStatus(OrderStatusEnum.EXPIRE.getValue());
        vo.setExpiredCount(woTaskMapper.totalWorkOrder(reqDTO));
		
        return vo;
    }
    
    @Override
	public WorkOrderDetailVO queryWorkOrderDetail(Long id, String executionId, String staffId) {
		WorkOrderDetailVO vo = woTaskMapper.queryWorkOrderDetail(id, staffId);
		if (Objects.isNull(vo)) {
			return null;
		}
		vo.setLoanList(setExternalOrder(woTaskMapper.queryWorkOrderLoanList(id)));
		vo.setExecutionId(executionId);
		// 根据executionId查询流程，分两种情况，是否已完成流程
		WFRunExecutionDTO runExecution = runBusiService.getRunExecution(executionId);
		if (Objects.isNull(runExecution)) {
			runExecution = hisBusiService.getHisExecution(executionId);
		}
		if (Objects.isNull(runExecution)) {
			return vo;
		}
		vo.setCompleteFlag(runExecution.getCompleteFlag());
		vo.setProcessCode(runExecution.getProcessCode());
		if (!WFConstant.EXECUTION_COMPLETE_FLAG_DONE.equals(runExecution.getCompleteFlag())) {
			vo.setTaskId(runExecution.getCurrTask().getTaskId());
		}
		// 获得工单流水
		List<WFRunLogDTO> runLogs = runExecution.getRunLog();
		List<String> staffIds = runLogs.stream().map(WFRunLogDTO::getStaffId).distinct().collect(Collectors.toList());
		List<StaffVO> staffs = staffService.getStaffById(staffIds);
		Map<String, StaffVO> staffMap = staffs.stream()
			.collect(Collectors.toMap(k -> String.valueOf(k.getId()), v -> v));
		List<WorkOrderLogVO> logList = new ArrayList<>();
	    List<WorkOrderLogVO> callbackSummaryLogList = callbackSummaryService.queryCallbackSummaryLogList(vo.getOrderNo());
	    for (WFRunLogDTO logDTO : runLogs) {
			WorkOrderLogVO logVO = new WorkOrderLogVO();
			logVO.setGmtCreate(DateUtil.dateToString(logDTO.getGmtCreate()));
			JSONObject jObjectComment = logDTO.getComment();
			if (Objects.nonNull(jObjectComment)) {
				logVO.setComment(jObjectComment.getString("opinion"));
			}
			StaffVO staffVO = staffMap.get(logDTO.getStaffId());
			if (Objects.nonNull(staffVO)) {
				logVO.setStaffId(staffVO.getStaffName());
				logVO.setGroupCode(staffVO.getGroupCode());
				logVO.setGroupName(staffVO.getGroupName());
			}
			logVO.setOperate(logDTO.getOperateType());
			logList.add(logVO);
		}
		logList.addAll(callbackSummaryLogList);
		logList.sort(Comparator.comparing(WorkOrderLogVO::getGmtCreate));
		vo.setLogList(logList);

		// 查询附件
        vo.setFileList(queryAttachment(id));
		try {
			if (StringUtils.isNotBlank(vo.getMobile())) {
				vo.setMobileMask(SecurityUtil.maskMobile2(vo.getMobile()));
				vo.setMobile(AesUtils.encrypt(vo.getMobile()));
			}
			fullFillEmailVo(vo);
			fullFillMobileBakList(vo);
		} catch (Exception e) {
			log.error("queryWorkOrderDetail encrypt mobile error", e);
		}
		return vo;
	}

	private List<WorkOrderLoanReqDTO> setExternalOrder(List<WorkOrderLoanReqDTO> workOrderLoanReqDTOS) {
		workOrderLoanReqDTOS.forEach(item -> {
			if (Objects.isNull(item)){
				return;
			}
			if (StringUtils.isBlank(item.getApplicationId()) || item.getApplicationId().startsWith("CA")){
				return;
			}
			LoanDetailsVO externalOrderNoByApplicationId = loanApplicationService.getExternalOrderNoByApplicationId(item.getApplicationId());
			if (Objects.nonNull(externalOrderNoByApplicationId)){
				item.setChannelOrderNo(externalOrderNoByApplicationId.getChannelOrderNo());
				item.setDebtOrderNo(externalOrderNoByApplicationId.getExternalOrderNo());
			}
			
			
		});
		return workOrderLoanReqDTOS;
	}

	private void fullFillEmailVo(WorkOrderDetailVO vo) {
        String email = vo.getEmail();
        if (StringUtils.isNotBlank(email)) {
            vo.setEmailVO(buildEmailVo(email));
        }
    }

    private WoEmailVO buildEmailVo(String email) {
        WoEmailVO emailVO = new WoEmailVO();
        emailVO.setEmail(email);
        List<BlackProductionUserInfo> blackList = blackProductionUserInfoMapper.selectList(Wrappers
            .lambdaQuery(BlackProductionUserInfo.class).eq(BlackProductionUserInfo::getBlackProductionEmail, email)
				        .eq(BlackProductionUserInfo::getApprovalStatus,CallOutBlackListServiceImpl.ApprovalStatusEnum.APPROVED.getCode())
            .orderByDesc(BlackProductionUserInfo::getId));
        if (CollectionUtils.isNotEmpty(blackList)) {
            emailVO
                .setBlackProductionUserType(BlackProductionUserTypeEnum.getDescByCode(blackList.get(0).getUserType()));
        }
        return emailVO;
    }

	private void fullFillMobileBakList(WorkOrderDetailVO vo) {
        List<WoMobileBakVO> mobileBakList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getMobileBak())) {
	        WoMobileBakVO mobileBakVO = buildWoMobileBak(vo.getMobileBak());
			mobileBakList.add(mobileBakVO);
        }
		
		if (StringUtils.isNotBlank(vo.getMobileBaks())){
			for (String mobileBak : vo.getMobileBaks().split(",")) {
				mobileBakList.add(buildWoMobileBak(mobileBak));
			}
		}
		vo.setMobileBakList(mobileBakList);
    }

	private WoMobileBakVO buildWoMobileBak(String mobileBak1) {
		WoMobileBakVO mobileBakVO = new WoMobileBakVO();
		mobileBakVO.setMobileMask(SecurityUtil.maskMobile2(mobileBak1));
		mobileBakVO.setMobileEncrypt(AesUtils.encrypt(mobileBak1));
		List<BlackProductionUserInfo> blackList =
				blackProductionUserInfoMapper.selectList(Wrappers.lambdaQuery(BlackProductionUserInfo.class)
						.eq(BlackProductionUserInfo::getBlackProductionMobile, mobileBak1)
								.eq(BlackProductionUserInfo::getApprovalStatus, CallOutBlackListServiceImpl.ApprovalStatusEnum.APPROVED.getCode())
						.orderByDesc(BlackProductionUserInfo::getId));
		if (CollectionUtils.isNotEmpty(blackList)) {
			mobileBakVO.setBlackProductionUserType(
					BlackProductionUserTypeEnum.getDescByCode(blackList.get(0).getUserType()));
		}
		return mobileBakVO;
	}

	private void setComplainResult(ComplainDictVO dictVo, WorkOrderComplainVO record) {
		if (dictVo != null) {
			String reviewResult = dictVo.getReviewDictMap().get(record.getReviewResult());
			if (StringUtils.isNotBlank(reviewResult)) {
				record.setReviewResult(reviewResult);
			}
			String approveResult = dictVo.getApproveDictMap().get(record.getApproveResult());
			if (StringUtils.isNotBlank(approveResult)) {
				record.setApproveResult(approveResult);
			}
		}
	}

	/**
	 * 通过工单组合中配置的投诉信息设置投诉相关的参数: 投诉级别、需要核实结果和处理结果
	 */
	private ComplainDTO setComplainLevelAndResult(WorkOrderSubmitReqDTO reqDTO) {
		LambdaQueryWrapper<OpDictInfoConf> wrapper = Wrappers.<OpDictInfoConf>lambdaQuery()
				.eq(OpDictInfoConf::getWoTypeId, reqDTO.getType())
				.eq(OpDictInfoConf::getWoTypeFirId, reqDTO.getOrderOneClass())
				.eq(OpDictInfoConf::getWoTypeSecId, reqDTO.getOrderTwoClass())
				.eq(OpDictInfoConf::getWoTypeThirId, reqDTO.getOrderThreeClass())
				.eq(OpDictInfoConf::getIsStatus, 1);
		List<OpDictInfoConf> confList = opDictInfoConfMapper.selectList(wrapper);
		ComplainDTO complainDTO = new ComplainDTO();
		if (CollectionUtils.isNotEmpty(confList)) {
			OpDictInfoConf conf = confList.get(0);
			complainDTO.setComplainLevel(conf.getComplainLevel());
			complainDTO.setMustReview(conf.getMustReview());
			complainDTO.setMustApprove(conf.getMustApprove());
		} else {
			throw new CrmOperateException("提交工单组合配置id组合异常: 参数" + reqDTO);
		}
		// 只要核实结果和处理结果有一个存在就需要处理，状态设置为处理中
		if (complainDTO.getMustReview() || complainDTO.getMustApprove()) {
			complainDTO.setApproveStatus(0);
		} else {
			complainDTO.setApproveStatus(1);
		}
		complainDTO.setUrgeStatus(Boolean.FALSE);
		return complainDTO;
	}

	/**
	 * 根据工单业务主键，查询改工单对应的附件
	 * @param taskId
	 * @return
	 */
    private List<WoAttachmentVO> queryAttachment(Long taskId) {
    	List<WoAttachmentVO> woAttachmentVOList = new ArrayList<>();
        List<WoAttachment> woAttachments = woAttachmentMapper
                .selectList(Wrappers.lambdaQuery(WoAttachment.class).eq(WoAttachment::getWoTaskId, taskId)
                        .eq(WoAttachment::getStatus, Boolean.TRUE));
        for (WoAttachment woAttachment : woAttachments) {
			WoAttachmentVO woAttachmentVO = new WoAttachmentVO();
			BeanUtils.copyProperties(woAttachment,woAttachmentVO);
			woAttachmentVO.setUploadTime(woAttachment.getGmtCreate());
			woAttachmentVO.setStaffId(woAttachment.getStaffId());
	        StaffVO staffVO = staffService.getStaffByStaffId(woAttachment.getStaffId().toString());
			if (Objects.nonNull(staffVO)) {
				woAttachmentVO.setStaffName(staffVO.getStaffName());
				woAttachmentVO.setGroupCode(staffVO.getGroupCode());
			}
			woAttachmentVOList.add(woAttachmentVO);
        }
        return woAttachmentVOList;
    }


	@Override
	public WorkOrderDetailVO queryEliteWorkOrderDetail(Long id) {
    	log.info("queryEliteWorkOrderDetail, id:{}", id);
		WorkOrderDetailVO workOrderDetailVO = new WorkOrderDetailVO();
		EliteWorkorderHistory eliteWorkorderHistory = eliteWorkorderHistoryMapper.selectById(id);
		if (Objects.nonNull(eliteWorkorderHistory)){
			convertEliteToDetailVO(workOrderDetailVO, eliteWorkorderHistory);
			// 贷款信息列表
			List<EliteWorkorderLoans> eliteWorkorderLoans = eliteWorkorderLoansMapper.selectList(
					Wrappers.lambdaQuery(EliteWorkorderLoans.class)
							.eq(EliteWorkorderLoans::getObjectiveGuid, eliteWorkorderHistory.getObjectiveGuid()));
			List<WorkOrderLoanReqDTO> loans = eliteWorkorderLoans.stream().map(this::convertToLoans)
					.collect(Collectors.toList());
			workOrderDetailVO.setLoanList(loans);

			// 工单流水
			List<EliteWorkorderLogs> eliteWorkorderLogs = eliteWorkorderLogsMapper.selectList(
					Wrappers.lambdaQuery(EliteWorkorderLogs.class)
							.eq(EliteWorkorderLogs::getObjectiveGuid, eliteWorkorderHistory.getObjectiveGuid()));
			List<WorkOrderLogVO> logs = eliteWorkorderLogs.stream().map(item -> convertToLogs(item))
					.collect(Collectors.toList());
			workOrderDetailVO.setLogList(logs);

			// 附件
			List<EliteWorkorderAttachment> eliteWorkorderAttachments = eliteWorkorderAttachmentMapper.selectList(
					Wrappers.lambdaQuery(EliteWorkorderAttachment.class)
							.eq(EliteWorkorderAttachment::getCustomerGuid, eliteWorkorderHistory.getCustomerGuid()));
			List<WoAttachmentVO> files = eliteWorkorderAttachments.stream().map(item -> convertToAttachment(item))
					.collect(Collectors.toList());
			workOrderDetailVO.setFileList(files);

		}

		return workOrderDetailVO;
	}

	private ComplainDictVO getComplainDictVo() {
		// 去催收系统查询关于核实结果和处理结果的字典配置数据
		ComplainDictVO dictVO = null;
		try {
			dictVO = complainOrderService.getComplainDictList();
		} catch (Exception e) {
			log.error("getComplainDictDTO getComplainDictList exception: {}", e.getMessage(), e);
		}
		return dictVO;
	}

	private List<OpCustomerComplain> listOpCustomerComplains(String workOrderNo) {
		LambdaQueryWrapper<OpCustomerComplain> wrapper = Wrappers.<OpCustomerComplain>lambdaQuery()
				.eq(OpCustomerComplain::getWorkOrderNo, workOrderNo);
		return complainMapper.selectList(wrapper);
	}

	private WoAttachmentVO convertToAttachment(EliteWorkorderAttachment item) {
		WoAttachmentVO woAttachmentVO = new WoAttachmentVO();
		if (Objects.nonNull(item)){
			woAttachmentVO.setFilename(item.getFileName());
			woAttachmentVO.setPath(eliteUrl + item.getPath());
			woAttachmentVO.setStaffName(item.getStaffName());
			woAttachmentVO.setUploadTime(item.getCreateDate());
		}
		return woAttachmentVO;
	}

	private WorkOrderLogVO convertToLogs(EliteWorkorderLogs item) {
		WorkOrderLogVO workOrderLogVO = new WorkOrderLogVO();
		if (Objects.nonNull(item)){
			workOrderLogVO.setGroupName(item.getDealGroup());
			workOrderLogVO.setStaffId(item.getDealStaff());
			workOrderLogVO.setGmtCreate(DateUtil.dateToString(item.getDealTime()));
			workOrderLogVO.setComment(item.getComments());
			workOrderLogVO.setOperate(item.getOperateType());
		}
		return workOrderLogVO;
	}

	private WorkOrderLoanReqDTO convertToLoans(EliteWorkorderLoans item) {
		WorkOrderLoanReqDTO workOrderLoanReqDTO = new WorkOrderLoanReqDTO();
		if (Objects.nonNull(item)){
			BeanUtils.copyProperties(item, workOrderLoanReqDTO);
			workOrderLoanReqDTO.setStatus(item.getOrderStatus());
		}
		return workOrderLoanReqDTO;
	}

	private void convertEliteToDetailVO(WorkOrderDetailVO workOrderDetailVO,
			EliteWorkorderHistory eliteWorkorderHistory) {
    	workOrderDetailVO.setId(eliteWorkorderHistory.getId());
    	workOrderDetailVO.setCompleteFlag("1");
    	workOrderDetailVO.setCustomerName(eliteWorkorderHistory.getCustomerName());
    	workOrderDetailVO.setGender(eliteWorkorderHistory.getGender());
    	workOrderDetailVO.setMobile(eliteWorkorderHistory.getMobile());
    	workOrderDetailVO.setOrderNo(eliteWorkorderHistory.getWorkorderId());
    	workOrderDetailVO.setRemark(eliteWorkorderHistory.getRemark());
    	workOrderDetailVO.setOrderOneClass(eliteWorkorderHistory.getWoType1());
    	workOrderDetailVO.setOrderTwoClass(eliteWorkorderHistory.getWoType2());
    	workOrderDetailVO.setOrderThreeClass(eliteWorkorderHistory.getWoType3());
    	workOrderDetailVO.setDescription(eliteWorkorderHistory.getProblemDesc());
    	workOrderDetailVO.setOpinion(eliteWorkorderHistory.getComments());
    	workOrderDetailVO.setStatus(eliteWorkorderHistory.getOrderStatus());

    	// 获取工单类型编码
		List<OpDictInfo> formTypeList = opDictInfoMapper
				.selectList(Wrappers.lambdaQuery(OpDictInfo.class).eq(OpDictInfo::getCategory, "formType"));
		List<OpDictInfo> filters = formTypeList.stream()
				.filter(opDictInfo -> opDictInfo.getContent().equals(eliteWorkorderHistory.getOrderType())).collect(
						Collectors.toList());
		if (CollectionUtils.isNotEmpty(filters)){
			workOrderDetailVO.setType(filters.get(0).getId().toString());
		} else {
			workOrderDetailVO.setType(eliteWorkorderHistory.getOrderType());
		}
	}

	@Override
	public List<WorkOrderHisVO> queryWorkOrderHistory(String uuid, String mobile, String orderNo, String processCode) {
		if (StringUtils.isNotBlank(mobile) && !Pattern.matches(Constant.MOBILE, mobile)){
			mobile = AesUtils.decrypt(mobile);
		}
		// 根据uuid查询全部的customerId
		List<Long> custIdList = new ArrayList<>();
		if (StringUtils.isNotBlank(uuid)) {
			List<DataCustomer> customers = dataCustomerMapper
					.selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getUuid, uuid)
							.select(DataCustomer::getId));
			custIdList = customers.stream().map(DataCustomer::getId).collect(Collectors.toList());
		} else if (StringUtils.isNotBlank(mobile)) { // 根据未注册用户的手机号码查询全部的customerId
			List<DataCustomer> customers = dataCustomerMapper
					.selectList(Wrappers.lambdaQuery(DataCustomer.class).eq(DataCustomer::getMobile, mobile)
							.select(DataCustomer::getId));
			custIdList = customers.stream().map(DataCustomer::getId).collect(Collectors.toList());
		}
		if (StringUtils.isBlank(orderNo) && custIdList.isEmpty()) {
			log.warn("queryWorkOrderHistory param: uuid-{}, orderNo-{}, mobile-{}", uuid, orderNo, mobile);
			return Collections.emptyList();
		}

		List<WorkOrderHisVO> result = woTaskMapper.queryWorkOrderHistory(custIdList, orderNo);
		List<WorkOrderHisVO> eliteWoHis = null;
		if (StringUtils.isNotBlank(mobile)) {
			eliteWoHis = getEliteWoHis(mobile);
		}
		if (CollectionUtils.isEmpty(result)) {
			return eliteWoHis;
		}
		List<String> busiKeys = result.stream().map(WorkOrderHisVO::getOrderNo).collect(Collectors.toList());
		Map<String, WFRunExecutionDTO> runExecution = runBusiService.getRunExecutionByBusiKeys(processCode, busiKeys);
		Map<String, WFRunExecutionDTO> hisExecution = hisBusiService.getHisExecutionByBusiKeys(processCode, busiKeys);
		runExecution.putAll(hisExecution);
		if (runExecution.isEmpty()) {
			return result;
		}
		WFRunExecutionDTO dto = null;
		for (WorkOrderHisVO vo : result) {
			dto = runExecution.get(vo.getOrderNo());
			if (Objects.nonNull(dto)) {
				vo.setExecutionId(dto.getExecutionId());
				vo.setCompleteTime(dto.getCompleteTime());
				if (Objects.nonNull(dto.getCurrTask())) {
					vo.setTaskId(dto.getCurrTask().getTaskId());
				}
				vo.setStaffId(dto.getCurrStaffId());
				vo.setGroupName(dto.getCurrGroupName());
				vo.setStaffName(dto.getCurrStaffName());
				vo.setStatus(WFWorkorderStateEnum.getDesc(dto.getStatus()));
				if (CommonUtil.checkIsEnd(dto.getStatus())){
					List<WorkOrderLogVO> woLogs = woTaskMapper.queryWorkOrderLogListByBusiKey(vo.getOrderNo());
					if (CollectionUtils.isNotEmpty(woLogs) && woLogs.size() >= 2){
						WorkOrderLogVO cur = woLogs.get(0);
						vo.setStaffName(cur.getStaffId());
						vo.setGroupName(cur.getGroupName());
					}
				}
			}
		}
		if (CollectionUtils.isNotEmpty(eliteWoHis)) {
			result.addAll(eliteWoHis);
		}
		return result;
	}

	/**
	 *  查询过河兵工单历史
	 * @param mobile 客户手机号
	 * @return
	 */
	private List<WorkOrderHisVO> getEliteWoHis(String mobile) {
		List<WorkOrderHisVO> workOrderHisVOS = new ArrayList<>();
		// 根据客户手机号查询过河兵历史工单
		List<EliteWorkorderHistory> eliteWorkorderHistories = eliteWorkorderHistoryMapper.selectList(
				Wrappers.lambdaQuery(EliteWorkorderHistory.class).eq(EliteWorkorderHistory::getMobile, mobile));
		workOrderHisVOS = eliteWorkorderHistories.stream()
				.map(this::convertToWorkOrderHisVO).sorted((o1, o2) -> {
					if (o1.getGmtCreate().after(o2.getGmtCreate())) {
						return -1;
					} else if (o1.getGmtCreate().before(o2.getGmtCreate())) {
						return 1;
					} else {
						return 0;
					}
				}).collect(Collectors.toList());

		return workOrderHisVOS;
	}

	private WorkOrderHisVO convertToWorkOrderHisVO(EliteWorkorderHistory eliteWorkorderHistory) {
		WorkOrderHisVO workOrderHisVO = new WorkOrderHisVO();
		if (Objects.nonNull(eliteWorkorderHistory)){
			workOrderHisVO.setId(eliteWorkorderHistory.getId());
			workOrderHisVO.setOrderNo(eliteWorkorderHistory.getWorkorderId());
			workOrderHisVO.setType(eliteWorkorderHistory.getOrderType());
			workOrderHisVO.setCustomerName(eliteWorkorderHistory.getCustomerName());
			workOrderHisVO.setStatus(eliteWorkorderHistory.getOrderStatus());
			workOrderHisVO.setGmtCreate(eliteWorkorderHistory.getCreateTime());
			workOrderHisVO.setCompleteTime(eliteWorkorderHistory.getEndTime());
			workOrderHisVO.setOpinion(eliteWorkorderHistory.getComments());
			workOrderHisVO.setGroupName(eliteWorkorderHistory.getCurrGroup());
			workOrderHisVO.setStaffName(eliteWorkorderHistory.getCurrStaff());
			workOrderHisVO.setThirdType(eliteWorkorderHistory.getWoType3());
			workOrderHisVO.setIsElite(Boolean.TRUE);
		}
		return workOrderHisVO;
	}

	@Override
    public void signWorkOrder(WorkOrderSignReqDTO reqDTO) {
    	WoTaskSign woTaskSign = woTaskSignMapper.queryWorkOrderSign(reqDTO);
    	if(woTaskSign != null) {
    		woTaskSign.setSign(reqDTO.getSign());
			woTaskSign.setGmtModify(new Date());
    		woTaskSignMapper.updateById(woTaskSign);
    	}else { 
    		woTaskSign = new WoTaskSign();
    		BeanUtils.copyProperties(reqDTO, woTaskSign);
    		woTaskSignMapper.insert(woTaskSign);
    	}
    }
	/**
	 * 是否大组长
	 * @return
	 */
    private Boolean isManaGroupManager(StaffVO staffVO){
		// 判断是否大组
		Boolean isManaGroup = iCrmOrgService.isManaGroup(staffVO.getGroupCode());
		Boolean isManager = false;
		if (isManaGroup) {
			// 是否大组长
			isManager = staffVO.getIsManager();
		}
		return isManager;
	}


	/**
	 * @param flag 1-提交工单；2-执行工单；3-保存工单
	 */
	public void saveOrUpdateWorkOrder(WorkOrderSubmitReqDTO reqDTO, StaffVO staffVO, Integer flag) {
    	WoTask woTask = null;
    	DataCustomer dataCustomer = dataCustomerMapper.selectById(reqDTO.getCustId());
    	if(reqDTO.getId() != null) {
    		woTask = woTaskMapper.selectById(reqDTO.getId());
    		if (woTask != null) {
    			BeanUtils.copyProperties(reqDTO, woTask);
    			woTask.setModifyStaffId(staffVO.getId().toString());
				woTask.setGmtModify(new Date());
				checkReminderFlag(woTask, staffVO.getId(), flag);
            	woTaskMapper.updateById(woTask);

            	//保存预约记录
            	if(StringUtils.isNotBlank(reqDTO.getAppointTime())) {
            		WoAppointCallback wac = new WoAppointCallback();
            		wac.setStaffId(reqDTO.getExecutionDTO().getCurrStaffId());
            		wac.setOrderNo(reqDTO.getOrderNo());
            		WoAppointCallback appointInfo = woAppointCallbackMapper.queryAppointCallback(wac);
            		if(appointInfo == null) {
            			wac.setAppointTime(DateUtils.parseDate(reqDTO.getAppointTime(), DateUtils.DATE_FORMAT));
            			wac.setAppointTitle(reqDTO.getAppointTitle());
            			wac.setGmtCreate(new Date());
            			woAppointCallbackMapper.insert(wac);
            		}
            	}
    		}
    	}else { 
    		woTask = new WoTask();
    		BeanUtils.copyProperties(reqDTO, woTask);
			saveMobileBak(reqDTO, woTask);
        	woTask.setStatus(reqDTO.getStatus());
        	if(dataCustomer != null) {
        		woTask.setCnid(dataCustomer.getCnid());
            	woTask.setAge(dataCustomer.getAge());
            	woTask.setGender(dataCustomer.getGender());
            	woTask.setMobile(dataCustomer.getMobile());
            	woTask.setCustomerName(dataCustomer.getCustomerName());
        	}
        	woTask.setSubmitTime(new Date());
        	woTask.setDistributeTime(new Date());
        	woTask.setCreateStaffId(staffVO.getId().toString());
        	woTaskMapper.insert(woTask);

			//保存贷款信息
			saveLoanList(woTask.getId(), reqDTO.getLoanList());
        	
//        	List<WorkOrderLoanReqDTO> loanList = reqDTO.getLoanList();
//        	if(loanList != null && loanList.size() > 0 ) {
//        		for(WorkOrderLoanReqDTO loan : loanList) {
//        			DataLoanApplication dataLoan = new DataLoanApplication();
//        			BeanUtils.copyProperties(loan, dataLoan);
//        			dataLoan.setWoTaskId(woTask.getId());
//        			dataLoan.setConfirmTime(loan.getConfirmedTime());
//        			dataLoanApplicationMapper.insert(dataLoan);
//        		}
//        	}

        	// 保存附件信息
			if (CollectionUtils.isNotEmpty(reqDTO.getFileList())){
				WoAttachmentReqDTO woAttachmentReqDTO = new WoAttachmentReqDTO();
				woAttachmentReqDTO.setFileList(reqDTO.getFileList());
				woAttachmentReqDTO.setId(woTask.getId());
				woAttachmentReqDTO.setStaffId(staffVO.getId());
				saveWoAttachment(woAttachmentReqDTO);
			}

        	//保存预约记录
			if(StringUtils.isNotBlank(reqDTO.getAppointTime())) {
				WoAppointCallback wac = new WoAppointCallback();
	    		wac.setStaffId(reqDTO.getExecutionDTO().getCurrStaffId());
	    		wac.setOrderNo(woTask.getOrderNo());
				wac.setAppointTime(DateUtils.parseDate(reqDTO.getAppointTime(), DateUtils.DATE_FORMAT));
				wac.setAppointTitle(reqDTO.getAppointTitle());
				wac.setGmtCreate(new Date());
				woAppointCallbackMapper.insert(wac);
			}
    	}
    }

	private void saveLoanList(Long id, List<WorkOrderLoanReqDTO> loanList) {
		if(loanList != null && !loanList.isEmpty()) {
			for(WorkOrderLoanReqDTO loan : loanList) {
				DataLoanApplication dataLoan = new DataLoanApplication();
				BeanUtils.copyProperties(loan, dataLoan);
				dataLoan.setWoTaskId(id);
				dataLoan.setConfirmTime(loan.getConfirmedTime());
				dataLoanApplicationMapper.insert(dataLoan);
			}
		}
	}

	private void checkReminderFlag(WoTask woTask, Long id, Integer flag) {
		if (flag != 3){
			return;
		}
		WFRunTaskDTO lastestTask = runBusiService.getLastestTaskByOrderNo(woTask.getOrderNo());
		if (StringUtils.isNotBlank(lastestTask.getStaffId()) && lastestTask.getStaffId().equals(String.valueOf(id))) {
			woTask.setReminderFlag("0");
		}

	}

	/**
	 * 保存备用号码字段字段
	 * @param reqDTO
	 * @param woTask
	 */
	private void saveMobileBak(WorkOrderSubmitReqDTO reqDTO, WoTask woTask) {
		if (CollectionUtils.isEmpty(reqDTO.getMobileBakList())){
			return;
		}
		StringBuilder mobileBakStr = new StringBuilder();
		for (String mobileBak : reqDTO.getMobileBakList()) {
			if (StringUtils.isBlank(mobileBak)) {
				continue;
			}
			boolean matches = Pattern.matches(Constant.MOBILE, mobileBak);
			if (!matches) {
                try {
                    mobileBakStr.append(AesUtils.decrypt(mobileBak)).append(",");
                } catch (Exception e) {
                    log.warn("aes 解密失败,保存原手机号 {}", mobileBak);
					mobileBakStr.append(mobileBak).append(",");
                }
            } else {
				mobileBakStr.append(mobileBak).append(",");
			}
		}
		if (StringUtils.isNotBlank(mobileBakStr)) {
			woTask.setMobileBaks(mobileBakStr.substring(0, mobileBakStr.length() - 1));
		}
	}


	

	@Override
	@Transactional(rollbackFor = Exception.class)
    public void reminderWorkOrder(WorkOrderSubmitReqDTO reqDTO, String staffId) {
    	try {
			WoTask woTask = woTaskMapper.selectById(reqDTO.getId());
			if(woTask != null && !"1".equals(woTask.getReminderFlag())) {
				woTask.setGmtModify(new Date());
				woTask.setReminderFlag("1");
				woTaskMapper.updateById(woTask);
			}
			WFRunExecutionDTO runExecution = reqDTO.getExecutionDTO();
			JSONObject json = new JSONObject();
			json.put("opinion", reqDTO.getOpinion());
			json.put("callbackNote", reqDTO.getCallbackNote());
		    Long currentStaffId = runBusiService.urgeTask(runExecution.getExecutionId(), staffId, json.toJSONString());

		    sendSensitiveWorkOrderNotice(woTask, currentStaffId, reqDTO.getOpinion());
		}catch (Exception e){
    		log.error("reminderWorkOrder error.", e);
			throw new CrmOperateException("催单失败");
		}
    }

	private void sendSensitiveWorkOrderNotice(WoTask woTask, Long currentStaffId, String opinion) {
		if (Objects.isNull(woTask)){
			return;
		}
		SensitiveWorkOrderDTO dto = new SensitiveWorkOrderDTO();
		dto.setWoTypeId(Long.valueOf(woTask.getType()));
//		dto.setWoTypeFirId(woTask.getOrderOneClass());
//		dto.setWoTypeSecId(woTask.getOrderTwoClass());
//		dto.setWoTypeThirId(woTask.getOrderThreeClass());
		dto.setCurPage(1);
		dto.setPageSize(1000);
		dto.setWarnType("robot");
		Page<SensitiveWorkOrderDTO> page = sensitiveWorkOrderService.query(dto);
		if (CollectionUtils.isNotEmpty(page.getRecords())){
			List<SensitiveWorkOrderDTO> records = page.getRecords();
			List<DataLoanApplication> dataLoanApplicationList = dataLoanApplicationMapper.selectList(Wrappers.lambdaQuery(DataLoanApplication.class).eq(DataLoanApplication::getWoTaskId, woTask.getId()));
			for (SensitiveWorkOrderDTO record : records) {
				if (isMatchConfig(woTask, record, dataLoanApplicationList)) {
					// 发送催单通知
					WorkOrderNoticeDTO noticeDTO = new WorkOrderNoticeDTO();
					noticeDTO.setUserName(woTask.getCustomerName());
					noticeDTO.setUuid(dataCustomerMapper.selectById(woTask.getCustId()).getUuid());
					noticeDTO.setCurrentStaff(staffMapper.selectById(currentStaffId).getStaffName());
					noticeDTO.setReminderStaff(CommonUtils.getCurrentloggedName());
					noticeDTO.setContent(opinion);
					workOrderServiceInterview.sensitiveOrderNotice(noticeDTO);
					return;
				}
			}
		}
	}

	private boolean isMatchConfig(WoTask woTask, SensitiveWorkOrderDTO config, List<DataLoanApplication> loanList) {
		boolean typeMatch = false;
		boolean firMatch = false;
		boolean secMatch = false;
		boolean thirMatch = false;
		boolean partnerMatch = false;
		if (woTask.getType().equals(config.getWoTypeId().toString())){
			typeMatch = true;
		}
		
		if (Objects.isNull(config.getWoTypeFirId()) || woTask.getOrderOneClass().equals(config.getWoTypeFirId())){
			firMatch = true;
		}
		
		if (Objects.isNull(config.getWoTypeSecId()) || woTask.getOrderTwoClass().equals(config.getWoTypeSecId())){
			secMatch = true;
		}
		if (Objects.isNull(config.getWoTypeThirId()) || woTask.getOrderThreeClass().equals(config.getWoTypeThirId())){
			thirMatch = true;
		}
		if (StringUtils.isBlank(config.getPartnerNames())){
			partnerMatch = true;
		} else {
			String[] partnerNames = config.getPartnerNames().split(",");
			for (String partnerName : partnerNames) {
				if (StringUtils.isNotBlank(partnerName)){
					for (DataLoanApplication loan : loanList) {
						if (partnerName.equals(loan.getPartnerCode())){
							partnerMatch = true;
							break;
						}
					}
				}
			}
		}
		
		return typeMatch && firMatch && secMatch && thirMatch && partnerMatch;
		
	}

	@Override
	public void batchReturn(List<String> executionIds, String staffId) {
		List<String> errorList = runBusiService.batchReturn(executionIds, staffId);
		if(CollectionUtils.isNotEmpty(errorList)){
			throw new CrmOperateException("有工单退回失败");
		}
	}

	@Override
	public void batchClose(List<String> executionIds, String staffId) {
		List<String> errorList = runBusiService.batchClose(executionIds, staffId);
		if(CollectionUtils.isNotEmpty(errorList)){
			throw new CrmOperateException("有工单结案失败");
		}
	}

	@Override
	public void batchBack(List<String> executionIds, String staffId) {
		List<String> errorList = runBusiService.batchBack(executionIds, staffId);
		if(CollectionUtils.isNotEmpty(errorList)){
			throw new CrmOperateException("有工单返回结果失败");
		}
	}

	@Override
	public void batchSave(WorkOrderBatchSaveDTO dto, String staffId) {
		String comment = dto.getComment();
		boolean hasException = false;
		for (WorkOrderBatchReplyDTO replyDTO : dto.getList()) {
			try {
				// 保存本步意见
				WoTask woTask = new WoTask();
				woTask.setId(replyDTO.getId());
				woTask.setOpinion(comment);
				woTask.setGmtModify(new Date());
				woTaskMapper.updateById(woTask);
				// 保存工单流水
				WFRunLogDTO logDTO = new WFRunLogDTO();
				logDTO.setExecutionId(replyDTO.getExecutionId());
				logDTO.setTaskId(replyDTO.getTaskId());
				logDTO.setStaffId(staffId);
				logDTO.setNodeCode(replyDTO.getNodeCode());
				JSONObject json = new JSONObject();
				json.put("opinion", comment);
				json.put("callbackNote", "");
				logDTO.setComment(json);
				logDTO.setOperateType("保存");
				runBusiService.tempStore(logDTO);
			} catch (Exception e) {
				log.error("batchSave error. comment={}, {}", comment, replyDTO.toString(), e);
				hasException = true;
			}
		}
		if (hasException) {
			throw new CrmOperateException("有工单保存失败");
		}
	}

	@Override
	public void saveWoAttachment(WoAttachmentReqDTO woAttachmentReqDTO) {
    	if (Objects.nonNull(woAttachmentReqDTO)){
			WoTask woTask = woTaskMapper.selectById(woAttachmentReqDTO.getId());
			if (Objects.isNull(woTask)){
				throw new CrmOperateException("该工单不存在:" + woAttachmentReqDTO.getId());
			}
			List<WoAttachmentVO> fileNameList = woAttachmentReqDTO.getFileList();
            // 根据文件名，获取 文件名:下载地址 map
            for (WoAttachmentVO vo : fileNameList) {
                WoAttachment woAttachment = new WoAttachment();
                woAttachment.setFilename(vo.getFilename());
                woAttachment.setPath(vo.getPath());
                woAttachment.setWoTaskId(woAttachmentReqDTO.getId());
                woAttachment.setStatus(Boolean.TRUE);
	            woAttachment.setStaffId(Objects.isNull(woAttachmentReqDTO.getStaffId()) ? CommonUtils.getCurrentloggedStaffId() : woAttachmentReqDTO.getStaffId());
                woAttachmentMapper.insert(woAttachment);
				vo.setId(woAttachment.getId());
            }
    	}
	}

	@Override
	public void deleteWoAttachment(Long id) {
		WoAttachment woAttachment = woAttachmentMapper.selectById(id);
		if (Objects.nonNull(woAttachment)) {
			woAttachment.setStatus(Boolean.FALSE);
			woAttachment.setGmtModify(new Date());
			woAttachmentMapper.updateById(woAttachment);
		} else {
			throw new CrmOperateException("该附件已不存在:" + id);
		}

	}

	@Override
	public List<WoAttachmentVO> getDownLoadUrl(List<String> fileNameList, Long id, ColStaffResVO staff) {

		Long staffId = Objects.isNull(staff) ? CommonUtils.getCurrentloggedStaffId() : staff.getId();
		String staffName = Objects.isNull(staff) ? CommonUtils.getCurrentloggedName() : staff.getStaffName();
    	if (CollectionUtils.isNotEmpty(fileNameList)) {
			Map<String, Object> uploadFile = uploadService.getUploadFile(fileNameList);
			WoAttachmentReqDTO woAttachmentReqDTO = new WoAttachmentReqDTO();
			List<WoAttachmentVO> woAttachmentVOS = new ArrayList<>();
			uploadFile.forEach((name, path) -> {
				WoAttachmentVO woAttachmentVO = new WoAttachmentVO();
				woAttachmentVO.setFilename(name);
				woAttachmentVO.setPath((String) path);
				woAttachmentVO.setUploadTime(new Date());
				woAttachmentVO.setStaffName(staffName);
				woAttachmentVO.setStaffId(staffId);
				woAttachmentVOS.add(woAttachmentVO);
			});
			woAttachmentReqDTO.setFileList(woAttachmentVOS);
			if (Objects.nonNull(id)) {
				woAttachmentReqDTO.setId(id);
				woAttachmentReqDTO.setStaffId(staffId);
				saveWoAttachment(woAttachmentReqDTO);

				// 如果是投诉相关的工单，则这些上传的新增文件也要推送到催收系统
				WoTask woTask = woTaskMapper.selectById(woAttachmentReqDTO.getId());
				List<OpCustomerComplain> complains = listOpCustomerComplains(woTask.getOrderNo());
				if (CollectionUtils.isNotEmpty(complains)) {
					ComplainDTO complainDTO = new ComplainDTO();
					List<ComplainAttachmentDTO> attachments = getComplainFilesDTO(woAttachmentReqDTO.getFileList(),
							staffId, staffName);
					complainDTO.setWorkOrderNo(woTask.getOrderNo());
					complainDTO.setAttachments(attachments);
					complainOrderService.updateComplainFileList(complainDTO);
				}
			}
			return woAttachmentReqDTO.getFileList();
		}else {
    		throw new CrmOperateException("文件不存在");
		}

	}

	private List<ComplainAttachmentDTO> getComplainFilesDTO(List<WoAttachmentVO> fileList, Long staffId, String staffName) {
		List<ComplainAttachmentDTO> attachments = new ArrayList<>();
		for (WoAttachmentVO attachmentVO : fileList) {
			ComplainAttachmentDTO dto = new ComplainAttachmentDTO();
			dto.setFilename(attachmentVO.getFilename());
			dto.setStaffId(staffId);
			dto.setStaffName(staffName);
			dto.setUploadTime(new Date());
			dto.setFilePath(attachmentVO.getPath());
			attachments.add(dto);
		}
		return attachments;
	}

	@Override
	public Response<byte[]> downloadWatermarkFile(String path,String fileName){
    	return uploadService.getWatermarkFile(path,fileName,CommonUtils.getCurrentloggedName());
	}

	@Override
	public String getUuIdByMobile(String mobile) {
		LambdaQueryWrapper<WoTask> wr = Wrappers.<WoTask>lambdaQuery()
				.eq(WoTask::getMobile, mobile).groupBy(WoTask::getMobile);
		WoTask woTask = woTaskMapper.selectOne(wr);
		if (woTask == null) {
			log.warn("用户工单数据异常,不存在此手机号对应的工单, param: {}", mobile);
			return null;
		}
		String dataCustomerId = woTask.getCustId();
		DataCustomer customer = dataCustomerMapper.selectById(dataCustomerId);
		if (!customer.getIsZx()) {
			return customer.getUuid();
		} else {
			return null;
		}
	}

	@Override
	public void takeOverOrder(String executionId) {
    	try {
			runBusiService.takeOverOrder(executionId, CommonUtils.getCurrentloggedStaffId().toString(),
					CommonUtils.getCurrentloggedOrg());
		} catch (Exception e){
			log.error("takeOverOrder 接管工单异常 executionId:" + executionId, e);
		}
	}

	@Override
	public void isCustSameOrder(WorkOrderSubmitReqDTO dto) {
		
		// 查询该客户的uuid
		DataCustomer customer = dataCustomerMapper.selectById(Long.parseLong(dto.getCustId()));
		WorkOrderModel model = new WorkOrderModel();
		BeanUtils.copyProperties(dto, model);
		model.setOrderType(dto.getType());
		if (Objects.nonNull(customer) && StringUtils.isNotBlank(customer.getUuid())){
			model.setUuid(customer.getUuid());
		} else {
			model.setCustId(Long.parseLong(dto.getCustId()));
		}
		model.setNotInStatusList(ORDER_CLOSE_STATUS);
		List<WoTask> woTasks = woTaskMapper.queryWorkOrderHistoryByCondition(model);
		if (CollectionUtils.isNotEmpty(woTasks)){
			throw new FastRuntimeException("该客户已有同类工单处理中，无法再次提交");
		}
		
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String externalSameOrderCheck(WorkOrderSubmitReqDTO dto, Long staffId) {
		// 查询该客户的uuid
		DataCustomer customer = dataCustomerMapper.selectById(Long.parseLong(dto.getCustId()));
		WorkOrderModel model = new WorkOrderModel();
		BeanUtils.copyProperties(dto, model);
		model.setOrderType(dto.getType());
		if (Objects.nonNull(customer) && StringUtils.isNotBlank(customer.getUuid())){
			model.setUuid(customer.getUuid());
		} else {
			model.setCustId(Long.parseLong(dto.getCustId()));
		}
		model.setNotInStatusList(ORDER_CLOSE_STATUS);
		List<WoTask> woTasks = woTaskMapper.queryWorkOrderHistoryByCondition(model);
		if (CollectionUtils.isNotEmpty(woTasks)){
			WoTask woTask = woTasks.get(0);
			if (StringUtils.isNotBlank(woTask.getMatchUp()) && StringUtils.isNotBlank(dto.getMatchUp())){
				mergeMatchUp(woTask, dto.getMatchUp());
				woTaskMapper.updateById(woTask);
			}


			String executionId = woRuleInfoMapper.queryExeIdByOrderNo(woTask.getOrderNo());

			WFRunTaskDTO lastestTask = runBusiService.getLastestTask(executionId);

			WFRunLogDTO logDTO = new WFRunLogDTO();
			logDTO.setExecutionId(executionId);
			logDTO.setTaskId(lastestTask.getTaskId());
			logDTO.setStaffId(staffId.toString());
			logDTO.setNodeCode(lastestTask.getNodeCode());
			JSONObject json = new JSONObject();
			json.put("opinion", dto.getDescription());
			json.put("callbackNote", "");
			logDTO.setComment(json);
			logDTO.setOperateType("提单");
			runBusiService.tempStore(logDTO);
			
			// 保存附件
			WoAttachmentReqDTO attachmentReqDTO = new WoAttachmentReqDTO();
			attachmentReqDTO.setFileList(dto.getFileList());
			attachmentReqDTO.setId(woTask.getId());
			attachmentReqDTO.setStaffId(staffId);
			saveWoAttachment(attachmentReqDTO);
			
			return woTask.getOrderNo();
		}
		return "";
	}

	/**
	 * 将matchUp合并到现有的工单中
	 * @param woTask
	 * @param matchUp
	 */
	public void mergeMatchUp(WoTask woTask, String matchUp) {
		if (StringUtils.isBlank(matchUp)){
			return;
		}
		if (StringUtils.isBlank(woTask.getMatchUp())) {
			woTask.setMatchUp(matchUp);
			return;
		}
		List<FundNameMatchUpDTO> matchUpList = JSONArray.parseArray(woTask.getMatchUp(), FundNameMatchUpDTO.class);
		List<FundNameMatchUpDTO> dtoMatchUpList = JSONArray.parseArray(matchUp, FundNameMatchUpDTO.class);
		List<FundNameMatchUpDTO> mergedFundNameLists = mergeFundNameLists(matchUpList, dtoMatchUpList);
		woTask.setMatchUp(JSON.toJSONString(mergedFundNameLists));
		woTask.setComplaintsChannel(mergedFundNameLists.stream()
				.map(FundNameMatchUpDTO::getComplaintsChannel).collect(Collectors.joining(",")));
		woTask.setFundName(String.join(",", mergedFundNameLists.stream()
				.flatMap(fundNameMatchUpDTO -> fundNameMatchUpDTO.getFundName().stream())
				.collect(Collectors.toSet())));
	}

	public List<FundNameMatchUpDTO> mergeFundNameLists(List<FundNameMatchUpDTO> list1, List<FundNameMatchUpDTO> list2) {
		// 1. 合并两个列表并分组（按投诉渠道）
		Map<String, FundNameMatchUpDTO> mergedMap = Stream.concat(list1.stream(), list2.stream())
				.collect(Collectors.toMap(
						FundNameMatchUpDTO::getComplaintsChannel, // Key: 投诉渠道
						dto -> { // 值转换：创建包含当前fundName的副本
							FundNameMatchUpDTO newDto = new FundNameMatchUpDTO();
							newDto.setComplaintsChannel(dto.getComplaintsChannel());
							newDto.setFundName(new ArrayList<>(dto.getFundName()));
							return newDto;
						},
						// 合并策略：相同渠道时合并fundName并去重
						(existingDto, newDto) -> {
							Set<String> mergedNames = new HashSet<>(existingDto.getFundName());
							mergedNames.addAll(newDto.getFundName());
							existingDto.setFundName(new ArrayList<>(mergedNames));
							return existingDto;
						}
				));

		// 2. 将Map值转换为List
		return new ArrayList<>(mergedMap.values());
	}


	@Override
	public void statisticsSubmitOrderTime(String status) {
		if (StringUtils.isBlank(status)) {
			throw new FastRuntimeException("状态不能为空");
		}

		Date now = new Date();

		if ("start".equals(status)) {
			OpStaffExtraStatusLog log = new OpStaffExtraStatusLog();
			log.setStaffId(CommonUtils.getCurrentlogged());
			log.setStartTime(now);
			log.setStatus(StaffExtraStatusEnum.SUBMIT_ORDER.name());
			opStaffExtraStatusLogMapper.insert(log);
		} else if ("end".equals(status)) {
			List<OpStaffExtraStatusLog> list = opStaffExtraStatusLogMapper
					.selectList(Wrappers.lambdaQuery(OpStaffExtraStatusLog.class)
							.eq(OpStaffExtraStatusLog::getStaffId, CommonUtils.getCurrentlogged())
							.ge(OpStaffExtraStatusLog::getGmtCreate, DateUtils.getStartOfToday())
							.isNull(OpStaffExtraStatusLog::getEndTime)
							.orderByDesc(OpStaffExtraStatusLog::getGmtCreate));
			if (CollectionUtils.isNotEmpty(list)) {

				OpStaffExtraStatusLog log = list.get(0);
				Date startTime = log.getStartTime();
				log.setEndTime(now);
				List<OpDictInfo> dict = CommonUtils.getDict("submit_order_max_duration", "time");
				// 提单最大时长 默认180秒
				long maxDuration = 180;
				if (CollectionUtils.isNotEmpty(dict)) {
					maxDuration = Long.parseLong(dict.get(0).getContent());
				}
				// 实际时长
				long duration = DateUtils.getSecondsBetween(startTime, now);
				log.setDuration(Math.min(maxDuration, duration));
				log.setGmtModify(now);
				opStaffExtraStatusLogMapper.updateById(log);
			}
		}
	}

	@Override
	public List<FundNameResDTO> queryFundName(FundAndRegulatoryComplaintDTO dto) {
		List<FundNamesDTO> page = woTaskMapper.queryFundName(dto);
		if (Objects.isNull(page)) {
			log.info("queryFundName is no data");
			return new ArrayList<FundNameResDTO>();
		}
		List<FundNameResDTO> result = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(page)) {
			List<FundNameDTO> list = new ArrayList<>();
			for (FundNamesDTO t : page) {
				if (StringUtils.isNotBlank(t.getMatchUp())) {
					List<FundNameMatchUpDTO> fundNames = JSONArray.parseArray(t.getMatchUp(), FundNameMatchUpDTO.class);
					for (FundNameMatchUpDTO tmp : fundNames) {
						for (String fundName : tmp.getFundName()) {
							FundNameDTO vo = new FundNameDTO();
							vo.setFundName(fundName);
							vo.setComplaintsChannel(tmp.getComplaintsChannel());
							vo.setJieJueLiang(t.getJieJueLiang());
							list.add(vo);
						}
					}
				}
			}
			Map<String, List<FundNameDTO>> fundNameMap = list.stream().collect(Collectors.groupingBy(FundNameDTO::getFundName));
			for (Map.Entry<String, List<FundNameDTO>> entry : fundNameMap.entrySet()) {
				FundNameResDTO vo = new FundNameResDTO();
				vo.setFundName(entry.getKey());
				for (FundNameDTO fn : entry.getValue()) {
					if ("资方内部".equals(fn.getComplaintsChannel())) {
						vo.setZiFangNeiBu(vo.getZiFangNeiBu() + 1);
					} else if ("银保监".equals(fn.getComplaintsChannel())) {
						vo.setYinBaoJian(vo.getYinBaoJian() + 1);
					} else if ("信访".equals(fn.getComplaintsChannel())) {
						vo.setXinFang(vo.getXinFang() + 1);
					} else if ("举报件".equals(fn.getComplaintsChannel())) {
						vo.setJuBaoJian(vo.getJuBaoJian() + 1);
					}
					vo.setJieJueLiang(entry.getValue().stream().mapToInt(FundNameDTO::getJieJueLiang).sum());
				}
				vo.setTotal(vo.getZiFangNeiBu() + vo.getYinBaoJian() + vo.getXinFang() + vo.getJuBaoJian());
				result.add(vo);
			}
			int a = result.stream().mapToInt(FundNameResDTO::getZiFangNeiBu).sum();
			int b = result.stream().mapToInt(FundNameResDTO::getYinBaoJian).sum();
			int c = result.stream().mapToInt(FundNameResDTO::getXinFang).sum();
			int d = result.stream().mapToInt(FundNameResDTO::getJuBaoJian).sum();
			int e = result.stream().mapToInt(FundNameResDTO::getJieJueLiang).sum();
			for (FundNameResDTO res : result) {
				res.setNeiBuPercent((double) Math.round(res.getZiFangNeiBu() / (a * 1.0) * 100) / 100);
				res.setJianGuanPercent((double) Math.round((res.getTotal() - res.getZiFangNeiBu()) / ((b + c + d) * 1.0) * 100) / 100);
				res.setJieJuePercent((double) Math.round(res.getJieJueLiang() / (res.getTotal() * 1.0) * 100) / 100);
				res.setTotalPercent((double) Math.round(res.getTotal() / ((a + b + c + d) * 1.0) * 100) / 100);
			}
			FundNameResDTO heJi = new FundNameResDTO();
			heJi.setFundName("合计");
			heJi.setZiFangNeiBu(a);//资方内部合计
			heJi.setNeiBuPercent(1);
			heJi.setYinBaoJian(b);
			heJi.setXinFang(c);
			heJi.setJuBaoJian(d);
			heJi.setJianGuanPercent(1);
			heJi.setTotal(a + b + c + d);
			heJi.setTotalPercent(1);
			heJi.setJieJueLiang(e);
			heJi.setJieJuePercent((double) Math.round(e / (heJi.getTotal() * 1.0) * 100) / 100);
			result.add(heJi);
		}
		return result;
	}

	@Override
	public List<RegulatoryComplaintResDTO> queryComplaint(FundAndRegulatoryComplaintDTO dto) {
		List<RegulatoryComplaintResDTO> result = woTaskMapper.queryComplaint(dto);
		if (Objects.isNull(result)) {
			log.info("queryComplaint is no data");
			return new ArrayList<RegulatoryComplaintResDTO>();
		}
		if (CollectionUtils.isNotEmpty(result)) {
			for (RegulatoryComplaintResDTO t : result) {
				t.setTotal(t.getBeiHai() + t.getChongFuJian() + t.getGuoManJian() + t.getNanShan() + t.getPuTongJian() + t.getReXian() + t.getShuiWuJu() + t.getPaiChuSuo() + t.getZhiLiang() + t.getXiaoZhen() + t.getShiZheng() + t.getSuSong());
				t.setJieJuePercent((double) Math.round(t.getJieJueLiang() / (t.getTotal() * 1.0) * 100) / 100);
			}
		}
		int a = result.stream().mapToInt(RegulatoryComplaintResDTO::getReXian).sum();//12345
		int b = result.stream().mapToInt(RegulatoryComplaintResDTO::getGuoManJian).sum();//国满件
		int c = result.stream().mapToInt(RegulatoryComplaintResDTO::getPuTongJian).sum();//普通件
		int d = result.stream().mapToInt(RegulatoryComplaintResDTO::getChongFuJian).sum();//重复件
		int e = result.stream().mapToInt(RegulatoryComplaintResDTO::getShuiWuJu).sum();//税务局
		int f = result.stream().mapToInt(RegulatoryComplaintResDTO::getPaiChuSuo).sum();//派出所
		int g = result.stream().mapToInt(RegulatoryComplaintResDTO::getNanShan).sum();//南山
		int h = result.stream().mapToInt(RegulatoryComplaintResDTO::getBeiHai).sum();//北海
		int i = result.stream().mapToInt(RegulatoryComplaintResDTO::getJieJueLiang).sum();//解决量
		int k = result.stream().mapToInt(RegulatoryComplaintResDTO::getTotal).sum();//合计
		int j = result.stream().mapToInt(RegulatoryComplaintResDTO::getZhiLiang).sum();//质量
		int l = result.stream().mapToInt(RegulatoryComplaintResDTO::getXiaoZhen).sum();//小镇
		int m = result.stream().mapToInt(RegulatoryComplaintResDTO::getShiZheng).sum();//市政
		int n = result.stream().mapToInt(RegulatoryComplaintResDTO::getSuSong).sum();//诉讼
		RegulatoryComplaintResDTO heJi = new RegulatoryComplaintResDTO();
		heJi.setTimes("合计");
		heJi.setReXian(a);
		heJi.setGuoManJian(b);
		heJi.setPuTongJian(c);
		heJi.setChongFuJian(d);
		heJi.setShuiWuJu(e);
		heJi.setPaiChuSuo(f);
		heJi.setNanShan(g);
		heJi.setBeiHai(h);
		heJi.setZhiLiang(j);
		heJi.setXiaoZhen(l);
		heJi.setShiZheng(m);
		heJi.setSuSong(n);
		heJi.setTotal(k);
		heJi.setJieJueLiang(i);
		heJi.setJieJuePercent((double) Math.round(heJi.getJieJueLiang() / (heJi.getTotal() * 1.0) * 100) / 100);
		result.add(heJi);
		return result;
	}

    @Override
    public WoMobileBakVO saveMobileBak(saveDTO dto) {
        if (Objects.isNull(dto.getId())) {
            return buildWoMobileBak(dto.getMobileBak());
        }
        WoTask woTask = woTaskMapper.selectById(dto.getId());
        checkOrderEnd(woTask.getOrderNo());

        String mobileBak = woTask.getMobileBaks();
        if (StringUtils.isNotBlank(mobileBak)) {
            List<String> existMobiles = Arrays.asList(mobileBak.split(","));
            if (existMobiles.contains(dto.getMobileBak())) {
                throw new FastRuntimeException("不能重复添加备用手机号");
            }
            woTask.setMobileBaks(mobileBak + "," + dto.getMobileBak());
        } else {
            woTask.setMobileBaks(dto.getMobileBak());
        }

        woTaskMapper.updateById(woTask);
        return buildWoMobileBak(dto.getMobileBak());
    }

	/**
	 * 判断工单是否结案
	 * @param orderNo
	 */
    private void checkOrderEnd(String orderNo) {
        WFRunExecutionDTO runExecutionDTO = woTaskMapper.selectRunExecutionByOrderNo(orderNo);
        if (Objects.isNull(runExecutionDTO) || CommonUtil.checkIsEnd(runExecutionDTO.getStatus())) {
            throw new FastRuntimeException("案件已结案，不能继续添加备用手机号");
			
        }
		
    }

    @Override
    public WoEmailVO saveEmail(saveDTO dto) {
        if (Objects.isNull(dto.getId())) {
            return buildEmailVo(dto.getEmail());
        }
        WoTask woTask = woTaskMapper.selectById(dto.getId());
        checkOrderEnd(woTask.getOrderNo());
        String email = woTask.getEmail();
        if (StringUtils.isNotBlank(email)) {
            throw new FastRuntimeException("只能添加一条邮箱信息");
        } else {
            woTask.setEmail(dto.getEmail());
            woTaskMapper.updateById(woTask);
        }
        return buildEmailVo(dto.getEmail());
    }

	@Override
	public String getFileNameById(Long id) {
		WoAttachment woAttachment = woAttachmentMapper.selectById(id);
		return woAttachment.getFilename();
	}


    @Override
    public boolean fileNameIsExist(String fileName) {
        List<WoAttachment> attachments = woAttachmentMapper
            .selectList(Wrappers.lambdaQuery(WoAttachment.class).eq(WoAttachment::getFilename, fileName));
        if (CollectionUtils.isNotEmpty(attachments)) {
            return true;
        }

	    List<OpTempFile> opTempFiles = opTempFileMapper.selectList(Wrappers.lambdaQuery(OpTempFile.class).eq(OpTempFile::getFilename, fileName));
		if (CollectionUtils.isNotEmpty(opTempFiles)){
			return true;
		}

	    List<CsVideoCheck> videoList = csVideoCheckMapper.selectList(Wrappers.lambdaQuery(CsVideoCheck.class)
            .eq(CsVideoCheck::getVideoName, fileName).or().eq(CsVideoCheck::getSourceVideoName, fileName));

        if (CollectionUtils.isNotEmpty(videoList)) {
            return true;
        }

        List<BlackProductionAttachment> blackProductionAttachments = blackProductionAttachmentMapper.selectList(
            Wrappers.lambdaQuery(BlackProductionAttachment.class).eq(BlackProductionAttachment::getFileName, fileName));
        if (CollectionUtils.isNotEmpty(blackProductionAttachments)) {
            return true;
        }

        return false;
    }

	@Override
	public List<OnlineFileRetVO> queryOnlineFile(Integer userId, Integer interval) {
		Map<String, Object> params = onlineSystemService.buildAuthenticationParams();
		params.put("userId", userId);
		params.put("interval", interval);

		String res = HttpClientUtil.get(onlineSystemUrlPre + "/api/user/attachment", params);
		if (StringUtils.isBlank(res)) {
			log.error("queryOnlineFile error, res is null");
			return Collections.emptyList();
		}
		OnlineRes<List<OnlineFileRetVO>> resObject = JSON.parseObject(res, new TypeReference<OnlineRes<List<OnlineFileRetVO>>>() {
		});

		if (OnlineRes.isSuccess(resObject)) {
			return resObject.getData();
		} else {
			log.warn("queryOnlineFile error, res:{}", res);
		}

		return Collections.emptyList();
	}

	@Override
	public List<WorkOrderLogVO> queryWorkOrderLogList(String executionId) {
		List<WorkOrderLogVO> logVOList = woTaskMapper.selectLogListByExecutionId(executionId);
		logVOList.forEach(log -> {
			if (StringUtils.isNotBlank(log.getComment())) {
				JSONObject jsonObject = JSON.parseObject(log.getComment());
				log.setComment(jsonObject.getString("opinion"));
			}
		});
		
		return logVOList;
	}

	@Override
	public List<WoAttachmentVO> queryAttachmentByOrderNo(String orderNo) {
		if (StringUtils.isBlank(orderNo)) {
			return Collections.emptyList();
		}
		WoTask woTask = woTaskMapper.selectOne(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, orderNo));
		if (Objects.isNull(woTask)) {
			log.warn("queryAttachmentByOrderNo error, woTask is null");
			return Collections.emptyList();
		}

		return queryAttachment(woTask.getId());
	}

	@Override
	public WorkOrderInfoVO queryOrderInfoByOrderNo(String orderNo) {
		WoTask woTask = woTaskMapper.selectOne(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, orderNo));
		if(Objects.isNull(woTask)){
			log.warn("queryOrderInfoByOrderNo error, woTask is null, orderNo:{}", orderNo);
			return null;
		}


		return buildWorkVo(woTask);
	}

	@Override
	public String uploadFile(MultipartFile multipartFile) {
		byte[] bytes = null;
		try {
			bytes = multipartFile.getBytes();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		log.info("size:{}", multipartFile.getSize());
		if (multipartFile.getSize() > Constant.MAX_FILE_SIZE) {
			throw new FastRuntimeException("上传文件不能大于20M");
		}
		if (!SecurityUtil.checkFileTypeByFileName(multipartFile.getOriginalFilename())) {
			return "";
		}
		Response<String> result = uploadService
				.uploadFile(bytes, CommonUtils.replaceBlank(multipartFile.getOriginalFilename()));
		return result.getResult();
	}

	@Override
	public void checkExternalOrderFile(String orderNo, String fileName, Long staffId) {
		WorkOrderInfoVO vo = queryOrderInfoByOrderNo(orderNo);
		List<WoAttachment> woAttachments = woAttachmentMapper
				.selectList(Wrappers.lambdaQuery(WoAttachment.class)
						.eq(WoAttachment::getWoTaskId, vo.getId())
						.eq(WoAttachment::getFilename, fileName));
		if (CollectionUtils.isEmpty(woAttachments)){
			log.warn("checkExternalOrderFile error, woAttachments is null, orderNo:{}, fileName:{}, staffId:{}", orderNo, fileName, staffId);
			throw new FastRuntimeException("你无权查看此文件");
		}
		

	}

    @Override
    public WorkOrderInfoVO queryOrderInfoByMiaoDaSn(String sn) {

		WoTask woTask = woTaskMapper.selectOne(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getMiaodaSn, sn));
		if(Objects.isNull(woTask)){
			log.warn("queryOrderInfoByOrderNo error, woTask is null, sn:{}", sn);
			return null;
		}


		return buildWorkVo(woTask);
    }

	@Override
	public ExecutionVO queryExecutionByOrderNo(String orderNo) {
		return woTaskMapper.selectExecutionByOrderNo(orderNo);
	}

	@Override
	public ExecutionVO queryExecutionByCondition(WorkOrderSearchReqDTO dto) {
		return woTaskMapper.selectExecutionByCondition(dto);
	}

	@Override
	public void setLoanCount(WorkOrderSubmitReqDTO submitReqDTO, String uuid) {
		if (StringUtils.isBlank(uuid)) {
			return;
		}

		List<LoanApplicationVO> loans = loanApplicationService.getLoanApplicationList(Long.valueOf(uuid));
		submitReqDTO.setSucLoanCount((int) loans.stream()
				.filter(loan -> Arrays.asList(LoanApplicationStateEnum.DISBURSED.getText(), LoanApplicationStateEnum.CLOSED.getText()).contains(loan.getState()))
				.count());
		submitReqDTO.setCurrentLoanCount((int) loans.stream()
				.filter(loan -> LoanApplicationStateEnum.DISBURSED.getText().equals(loan.getState()))
				.count());
	}

	@Override
	public WFConfigAssignedDTO getAssignedDTO() {
		WFConfigAssignedDTO assignedDTO = new WFConfigAssignedDTO();
		assignedDTO.setAssignedModel("transfer");
		assignedDTO.setAssignedType("role");
		assignedDTO.setAssignedValue("gdz");
		assignedDTO.setAssignedName("工单组");
		return assignedDTO;
	}

	@Override
	public WFRunExecutionDTO getExecutionDTO(Long staffId) {
		WFRunExecutionDTO executionDTO = new WFRunExecutionDTO();
		if (Objects.nonNull(staffId)) {
			executionDTO.setCurrStaffId(String.valueOf(staffId));
		} else {
			// 没有提交人，获取默认提交人
			StaffVO staff = getDefaultSubmitStaff();
			if (Objects.nonNull(staff)) {
				executionDTO.setCurrStaffId(String.valueOf(staff.getId()));
			}
		}

		return executionDTO;
	}

	@Override
	public WFConfigTransitionDTO getTransitionDTO() {
		WFConfigTransitionDTO dto = new WFConfigTransitionDTO();
		dto.setBackFlag("0");
		dto.setShowExpr("");
		dto.setShowName("提单工单组");
		dto.setTargetNodeCode("nwoWorkOrder");
		dto.setTargetState("process");
		dto.setTransCode("nwoStartTrWorkOrder");
		dto.setTransName("开始节点提单投诉组");
		return dto;
	}

	@Override
	public String getProcessCode() {
		return "NornalWO";
	}

	@Override
	public StaffVO getDefaultSubmitStaff() {
		ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(DEFAULT_SUBMIT_ORDER_STAFF);
		if (Objects.nonNull(staff)) {
			StaffVO vo = new StaffVO();
			BeanUtils.copyProperties(staff, vo);
			return vo;
		}
		return null;
	}

	@Override
	public void updateMiaodaInfo(MiaodaComplaintVO complaintVO, String orderNo) {
		woTaskMapper.updateMiaodaInfo(complaintVO, orderNo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void reminderOrder(String orderNo, String opinion, String loginName) {
		Long staffId;
		if (StringUtils.isBlank(loginName)){
			StaffVO staff = getDefaultSubmitStaff();
			staffId = staff.getId();
		} else {
			ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(loginName);
			staffId = staff.getId();
		}
		WorkOrderSubmitReqDTO reqDTO = new WorkOrderSubmitReqDTO();
		ExecutionVO executionVO = queryExecutionByOrderNo(orderNo);
		if (Objects.isNull(executionVO) || CommonUtil.checkIsEnd(executionVO.getOrderStatus())) {
			throw new FastRuntimeException("催单失败，工单不存在或无权限或者已结案");
		}

		reqDTO.setId(executionVO.getWoTaskId());
		WFRunExecutionDTO executionDTO = new WFRunExecutionDTO();
		executionDTO.setExecutionId(executionVO.getExecutionId());
		reqDTO.setExecutionDTO(executionDTO);
		reqDTO.setOpinion(opinion);
		reminderWorkOrder(reqDTO, String.valueOf(staffId));
	}

	@Override
	public void customerUpdate(String mobile, String orderNo) {
		log.info("更新工单注册手机号，mobile:{}, orderNo:{}", mobile, orderNo);
		UserDetailQueryDTO dto = new UserDetailQueryDTO();
		dto.setMobile(mobile);
		PersonalDetailsVoExpand result = userInfoService.queryUserInfo(dto);

		if (Objects.nonNull(result) && StringUtils.isNotBlank(result.getMobile())) {
			Long id = customerService.saveUserInfoCash(result);
			updateCustomerInfo(id, result.getPersonalDetailsVo().getProfile().getName()
					, result.getMobile(), result.getPersonalDetailsVo().getProfile().getCnid()
					, result.getPersonalDetailsVo().getProfile().getAge()
					, result.getGender(), orderNo);

		} else {
			throw new FastRuntimeException("该手机号非注册手机号");
		}
	}

	@Override
	public void addApplication(String orderNo, List<WorkOrderLoanReqDTO> loanList) {
		log.info("新增工单关联贷款，orderNo:{}, loanList:{}", orderNo, JSON.toJSONString(loanList));
		WoTask woTask = woTaskMapper.selectWoTaskByOrderNo(orderNo);
		if (Objects.isNull(woTask)) {
			throw new FastRuntimeException("工单不存在");
		}
		List<String> appNoList = loanList.stream().map(WorkOrderLoanReqDTO::getApplicationId).collect(Collectors.toList());
		List<DataLoanApplication> list = dataLoanApplicationMapper
				.selectByAppNoList(woTask.getId(), appNoList);
		if (CollectionUtils.isNotEmpty(list)){
			throw new FastRuntimeException("存在已添加贷款，贷款号:" +
					list.stream().map(DataLoanApplication::getApplicationId).collect(Collectors.joining(",")));
		}
		saveLoanList(woTask.getId(), loanList);
	}

	@Override
	public void updateOrderThreeType(WorkOrderSubmitReqDTO reqDTO) {
		log.info("更新工单三级分类，reqDTO:{}", JSON.toJSONString(reqDTO));
		woTaskMapper.updateOrderThreeType(reqDTO);
	}

	private void updateCustomerInfo(Long custId, String custName, String mobile, String cnid, int age, String gender, String orderNo) {
		WoTask woTask = woTaskMapper.selectWoTaskByOrderNo(orderNo);
		if (Objects.isNull(woTask)) {
			throw new FastRuntimeException("工单不存在");
		}
		log.info("updateCustomerInfo, newCustId:{},preCustId:{}, orderNo:{}", custId, woTask.getCustId(), orderNo);
		// 将原来的手机号添加到备用号码
		appendMobileBak(woTask, woTask.getMobile());
		woTask.setCustId(String.valueOf(custId));
		woTask.setCustomerName(custName);
		woTask.setMobile(mobile);
		woTask.setCnid(cnid);
		woTask.setAge(age);
		woTask.setGender(gender);
		woTaskMapper.updateById(woTask);
	}

	private void appendMobileBak(WoTask woTask, String appendMobile) {
		if (StringUtils.isBlank(woTask.getMobileBaks())){
			woTask.setMobileBaks(appendMobile);
		} else {
			woTask.setMobileBaks(woTask.getMobileBaks() + "," + appendMobile);
		}

	}


	private WorkOrderInfoVO buildWorkVo(WoTask woTask) {
		WorkOrderInfoVO vo = new WorkOrderInfoVO();
		BeanUtils.copyProperties(woTask, vo);
		return vo;
	}
}
