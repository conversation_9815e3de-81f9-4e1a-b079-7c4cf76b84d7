package com.welab.crm.operate.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.welab.crm.operate.config.MiaodaApiConfig;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;

/**
 * 喵达API客户端工具类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Component
public class MiaodaApiClient {

    @Resource
    private MiaodaApiConfig miaodaApiConfig;

    /**
     * 执行GET请求
     * @param path API路径
     * @param body 请求体（JSON字符串或对象）
     * @param token 认证Token
     * @return 响应结果
     */
    public String doGet(String path, Object body, String token) {
        HashMap<String, String> headers = new HashMap<>();
        if (!path.equals(MiaodaApiConstant.LOGIN_PATH)) {
            headers.put(MiaodaApiConstant.AUTHORIZATION_HEADER, MiaodaApiConstant.AUTHORIZATION_PREFIX + token);
        }
        return HttpClientUtil.GetOpenUrlJsonString(miaodaApiConfig.getFullUrl(path),
                body instanceof String ? (String) body : JSON.toJSONString(body), headers);

    }

    /**
     * 执行POST请求
     *
     * @param path API路径
     * @param body 请求体（JSON字符串或对象）
     * @param token 认证Token
     * @return 响应结果
     */
    public String doPost(String path, Object body, String token) {
        return doRequest("POST", path, body, token);
    }

    /**
     * 执行HTTP请求
     *
     * @param method 请求方法
     * @param path API路径
     * @param data 请求数据
     * @param token 认证Token
     * @return 响应结果
     */
    private String doRequest(String method, String path, Object data, String token) {
        if (!miaodaApiConfig.getEnabled()) {
            throw new RuntimeException("喵达API功能已禁用");
        }

        String url = miaodaApiConfig.getFullUrl(path);
        int retryTimes = 0;
        Exception lastException = null;

        while (retryTimes <= miaodaApiConfig.getMaxRetryTimes()) {
            try {
                log.info("调用喵达API: {} {}, 重试次数: {}", method, url, retryTimes);

                HttpRequest request = createRequest(method, url, data, token);
                HttpResponse response = request.execute();

                if (response.isOk()) {
                    String responseBody = response.body();
                    log.info("喵达API调用成功: {} {}", method, url);
                    log.debug("响应内容: {}", responseBody);
                    return responseBody;
                } else {
                    throw new RuntimeException("HTTP请求失败，状态码: " + response.getStatus());
                }

            } catch (Exception e) {
                lastException = e;
                retryTimes++;
                
                log.warn("喵达API调用失败: {} {}, 重试次数: {}, 错误: {}", 
                    method, url, retryTimes, e.getMessage());

                if (retryTimes <= miaodaApiConfig.getMaxRetryTimes()) {
                    try {
                        Thread.sleep(miaodaApiConfig.getRetryInterval());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("喵达API调用最终失败: {} {}", method, url, lastException);
        throw new RuntimeException("喵达API调用失败: " + lastException.getMessage(), lastException);
    }

    /**
     * 创建HTTP请求
     *
     * @param method 请求方法
     * @param url 请求URL
     * @param data 请求数据（JSON字符串或对象）
     * @param token 认证Token
     * @return HttpRequest对象
     */
    private HttpRequest createRequest(String method, String url, Object data, String token) {
        HttpRequest request;

        if ("GET".equalsIgnoreCase(method)) {
            request = HttpUtil.createGet(url);
            // GET请求也使用JSON请求体，与POST请求保持一致
            if (data instanceof String) {
                request.body((String) data);
            } else if (data != null) {
                request.body(JSON.toJSONString(data));
            }
        } else {
            request = HttpUtil.createPost(url);
            if (data instanceof String) {
                request.body((String) data);
            } else if (data != null) {
                request.body(JSON.toJSONString(data));
            }
        }

        // 设置请求头
        request.header("Content-Type", MiaodaApiConstant.CONTENT_TYPE_JSON);
        
        // 设置认证Token
        if (StringUtils.isNotBlank(token)) {
            request.header(MiaodaApiConstant.AUTHORIZATION_HEADER, 
                MiaodaApiConstant.AUTHORIZATION_PREFIX + token);
        }

        // 设置超时时间
        request.timeout(miaodaApiConfig.getConnectTimeout());

        return request;
    }

    /**
     * 检查API响应是否成功
     *
     * @param response 响应字符串
     * @return 是否成功
     */
    public boolean isResponseSuccess(String response) {
        try {
            if (StringUtils.isBlank(response)) {
                return false;
            }

            JSONObject responseJson = JSON.parseObject(response);
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                return false;
            }

            JSONObject status = result.getJSONObject("status");
            if (status == null) {
                return false;
            }

            Integer code = status.getInteger("code");
            return Integer.valueOf(200).equals(code);

        } catch (Exception e) {
            log.error("解析API响应失败", e);
            return false;
        }
    }

    /**
     * 从响应中提取错误信息
     *
     * @param response 响应字符串
     * @return 错误信息
     */
    public String extractErrorMessage(String response) {
        try {
            if (StringUtils.isBlank(response)) {
                return "响应为空";
            }

            JSONObject responseJson = JSON.parseObject(response);
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                return "响应格式错误";
            }

            JSONObject status = result.getJSONObject("status");
            if (status == null) {
                return "状态信息缺失";
            }

            String msg = status.getString("msg");
            Integer code = status.getInteger("code");
            
            return String.format("错误码: %s, 错误信息: %s", code, msg);

        } catch (Exception e) {
            log.error("提取错误信息失败", e);
            return "解析错误信息失败: " + e.getMessage();
        }
    }

    /**
     * 从响应中提取数据
     *
     * @param response 响应字符串
     * @return 数据对象
     */
    public JSONObject extractData(String response) {
        try {
            if (StringUtils.isBlank(response)) {
                return null;
            }

            JSONObject responseJson = JSON.parseObject(response);
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                return null;
            }

            return result.getJSONObject("data");

        } catch (Exception e) {
            log.error("提取响应数据失败", e);
            return null;
        }
    }


    /**
     * 从响应中提取数据
     *
     * @param response 响应字符串
     * @return 数据对象
     */
    public JSONArray extractDataArray(String response) {
        try {
            if (StringUtils.isBlank(response)) {
                return null;
            }

            JSONObject responseJson = JSON.parseObject(response);
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                return null;
            }

            return result.getJSONArray("data");

        } catch (Exception e) {
            log.error("提取响应数据失败", e);
            return null;
        }
    }
}
