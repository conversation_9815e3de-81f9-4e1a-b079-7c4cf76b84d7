package com.welab.crm.operate.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.welab.crm.operate.vo.miaoda.MiaodaAttachmentVO;
import com.welab.crm.operate.vo.miaoda.MiaodaCompleteInfoVO;
import com.welab.crm.operate.vo.miaoda.MiaodaReplyDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 喵达投诉单JSON字段处理工具类
 * 用于处理回复、结案和附件信息的JSON序列化和反序列化
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Slf4j
public class MiaodaComplaintJsonUtil {

    /**
     * 将回复详情列表转换为JSON字符串
     *
     * @param replyDetails 回复详情列表
     * @return JSON字符串，如果列表为空则返回null
     */
    public static String replyDetailsToJson(List<MiaodaReplyDetailVO> replyDetails) {
        if (CollectionUtils.isEmpty(replyDetails)) {
            return null;
        }
        try {
            return JSON.toJSONString(replyDetails);
        } catch (Exception e) {
            log.error("回复详情列表转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为回复详情列表
     *
     * @param json JSON字符串
     * @return 回复详情列表，如果JSON为空或解析失败则返回空列表
     */
    public static List<MiaodaReplyDetailVO> jsonToReplyDetails(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            return JSON.parseObject(json, new TypeReference<List<MiaodaReplyDetailVO>>() {});
        } catch (Exception e) {
            log.error("JSON转换为回复详情列表失败: {}", json, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将结案信息列表转换为JSON字符串
     *
     * @param completeInfo 结案信息列表
     * @return JSON字符串，如果列表为空则返回null
     */
    public static String completeInfoToJson(List<MiaodaCompleteInfoVO> completeInfo) {
        if (CollectionUtils.isEmpty(completeInfo)) {
            return null;
        }
        try {
            return JSON.toJSONString(completeInfo);
        } catch (Exception e) {
            log.error("结案信息列表转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为结案信息列表
     *
     * @param json JSON字符串
     * @return 结案信息列表，如果JSON为空或解析失败则返回空列表
     */
    public static List<MiaodaCompleteInfoVO> jsonToCompleteInfo(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            return JSON.parseObject(json, new TypeReference<List<MiaodaCompleteInfoVO>>() {});
        } catch (Exception e) {
            log.error("JSON转换为结案信息列表失败: {}", json, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将附件列表转换为JSON字符串
     *
     * @param attachments 附件列表
     * @return JSON字符串，如果列表为空则返回null
     */
    public static String attachmentsToJson(List<MiaodaAttachmentVO> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        try {
            return JSON.toJSONString(attachments);
        } catch (Exception e) {
            log.error("附件列表转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为附件列表
     *
     * @param json JSON字符串
     * @return 附件列表，如果JSON为空或解析失败则返回空列表
     */
    public static List<MiaodaAttachmentVO> jsonToAttachments(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            return JSON.parseObject(json, new TypeReference<List<MiaodaAttachmentVO>>() {});
        } catch (Exception e) {
            log.error("JSON转换为附件列表失败: {}", json, e);
            return new ArrayList<>();
        }
    }

    /**
     * 验证JSON字符串格式是否正确
     *
     * @param json JSON字符串
     * @return 是否为有效的JSON格式
     */
    public static boolean isValidJson(String json) {
        if (StringUtils.isBlank(json)) {
            return true; // 空字符串认为是有效的
        }
        try {
            JSON.parse(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取回复详情列表的统计信息
     *
     * @param replyDetails 回复详情列表
     * @return 统计信息字符串
     */
    public static String getReplyStatistics(List<MiaodaReplyDetailVO> replyDetails) {
        if (CollectionUtils.isEmpty(replyDetails)) {
            return "无回复记录";
        }

        int totalCount = replyDetails.size();
        long userReplyCount = replyDetails.stream()
                .filter(reply -> reply.getSender() != null && reply.getSender() == 1)
                .count();
        long merchantReplyCount = replyDetails.stream()
                .filter(reply -> reply.getSender() != null && reply.getSender() == 2)
                .count();

        return String.format("总回复数: %d, 用户回复: %d, 商家回复: %d", 
                totalCount, userReplyCount, merchantReplyCount);
    }

    /**
     * 获取附件列表的统计信息
     *
     * @param attachments 附件列表
     * @return 统计信息字符串
     */
    public static String getAttachmentStatistics(List<MiaodaAttachmentVO> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return "无附件";
        }

        int totalCount = attachments.size();
        long imageCount = attachments.stream()
                .filter(attachment -> "image".equals(attachment.getType()))
                .count();
        long videoCount = attachments.stream()
                .filter(attachment -> "video".equals(attachment.getType()))
                .count();

        return String.format("总附件数: %d, 图片: %d, 视频: %d", 
                totalCount, imageCount, videoCount);
    }
}
