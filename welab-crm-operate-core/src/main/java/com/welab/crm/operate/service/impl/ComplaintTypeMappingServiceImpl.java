package com.welab.crm.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import com.welab.crm.operate.domain.ComplaintTypeMapping;
import com.welab.crm.operate.dto.miaoda.ComplaintTypeMappingDTO;
import com.welab.crm.operate.dto.miaoda.ComplaintTypeMappingQueryDTO;
import com.welab.crm.operate.mapper.ComplaintTypeMappingMapper;
import com.welab.crm.operate.service.ComplaintTypeMappingService;
import com.welab.crm.operate.vo.miaoda.ComplaintTypeMappingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 投诉类型映射服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
public class ComplaintTypeMappingServiceImpl implements ComplaintTypeMappingService {

    @Resource
    private ComplaintTypeMappingMapper complaintTypeMappingMapper;

    @Override
    public Page<ComplaintTypeMappingVO> queryMappingPage(ComplaintTypeMappingQueryDTO queryDTO) {
        try {
            log.info("分页查询投诉类型映射，参数：{}", queryDTO);

            Page<ComplaintTypeMappingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
            return complaintTypeMappingMapper.selectMappingPage(page, queryDTO);

        } catch (Exception e) {
            log.error("分页查询投诉类型映射异常", e);
            throw new RuntimeException("查询投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    public ComplaintTypeMappingVO getMappingById(Long id) {
        try {
            if (id == null) {
                return null;
            }

            ComplaintTypeMapping mapping = complaintTypeMappingMapper.selectById(id);
            if (mapping == null) {
                return null;
            }

            ComplaintTypeMappingVO vo = new ComplaintTypeMappingVO();
            BeanUtils.copyProperties(mapping, vo);
            
            // 设置状态描述
            vo.setIsActiveDesc(mapping.getIsActive() == MiaodaApiConstant.MAPPING_ENABLED ? "启用" : "禁用");
            
            return vo;

        } catch (Exception e) {
            log.error("根据ID查询投诉类型映射异常", e);
            throw new RuntimeException("查询投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMapping(ComplaintTypeMappingDTO mappingDTO, String operator) {
        try {
            log.info("保存投诉类型映射，参数：{}", mappingDTO);

            // 检查是否已存在相同的映射
            if (existsByMiaodaIssue(mappingDTO.getMiaodaIssue(), null)) {
                throw new RuntimeException("该投诉问题类型已存在映射配置");
            }

            ComplaintTypeMapping mapping = new ComplaintTypeMapping();
            BeanUtils.copyProperties(mappingDTO, mapping);
            mapping.setCreateBy(operator);
            mapping.setUpdateBy(operator);
            mapping.setGmtCreate(new Date());
            mapping.setGmtModify(new Date());

            int result = complaintTypeMappingMapper.insert(mapping);
            
            log.info("保存投诉类型映射成功，ID：{}", mapping.getId());
            return result > 0;

        } catch (Exception e) {
            log.error("保存投诉类型映射异常", e);
            throw new RuntimeException("保存投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMapping(ComplaintTypeMappingDTO mappingDTO, String operator) {
        try {
            log.info("更新投诉类型映射，参数：{}", mappingDTO);

            if (mappingDTO.getId() == null) {
                throw new IllegalArgumentException("更新时ID不能为空");
            }

            // 检查是否已存在相同的映射（排除当前记录）
            if (existsByMiaodaIssue(mappingDTO.getMiaodaIssue(), mappingDTO.getId())) {
                throw new RuntimeException("该投诉问题类型已存在映射配置");
            }

            ComplaintTypeMapping existing = complaintTypeMappingMapper.selectById(mappingDTO.getId());
            if (existing == null) {
                throw new RuntimeException("映射配置不存在");
            }

            BeanUtils.copyProperties(mappingDTO, existing);
            existing.setUpdateBy(operator);
            existing.setGmtModify(new Date());

            int result = complaintTypeMappingMapper.updateById(existing);
            
            log.info("更新投诉类型映射成功，ID：{}", existing.getId());
            return result > 0;

        } catch (Exception e) {
            log.error("更新投诉类型映射异常", e);
            throw new RuntimeException("更新投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMapping(Long id) {
        try {
            log.info("删除投诉类型映射，ID：{}", id);

            if (id == null) {
                throw new IllegalArgumentException("ID不能为空");
            }

            int result = complaintTypeMappingMapper.deleteById(id);
            
            log.info("删除投诉类型映射成功，ID：{}", id);
            return result > 0;

        } catch (Exception e) {
            log.error("删除投诉类型映射异常", e);
            throw new RuntimeException("删除投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteMapping(List<Long> ids) {
        try {
            log.info("批量删除投诉类型映射，IDs：{}", ids);

            if (CollectionUtils.isEmpty(ids)) {
                throw new IllegalArgumentException("ID列表不能为空");
            }

            int result = complaintTypeMappingMapper.deleteBatchIds(ids);
            
            log.info("批量删除投诉类型映射成功，删除数量：{}", result);
            return result > 0;

        } catch (Exception e) {
            log.error("批量删除投诉类型映射异常", e);
            throw new RuntimeException("批量删除投诉类型映射失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleMappingStatus(Long id, Integer isActive, String operator) {
        try {
            log.info("切换映射状态，ID：{}，状态：{}", id, isActive);

            if (id == null) {
                throw new IllegalArgumentException("ID不能为空");
            }

            ComplaintTypeMapping mapping = complaintTypeMappingMapper.selectById(id);
            if (mapping == null) {
                throw new RuntimeException("映射配置不存在");
            }

            mapping.setIsActive(isActive);
            mapping.setUpdateBy(operator);
            mapping.setGmtModify(new Date());

            int result = complaintTypeMappingMapper.updateById(mapping);
            
            log.info("切换映射状态成功，ID：{}", id);
            return result > 0;

        } catch (Exception e) {
            log.error("切换映射状态异常", e);
            throw new RuntimeException("切换映射状态失败：" + e.getMessage());
        }
    }

    @Override
    public ComplaintTypeMapping getMappingByMiaodaIssue(String miaodaIssue) {
        try {
            if (StringUtils.isBlank(miaodaIssue)) {
                return null;
            }

            return complaintTypeMappingMapper.selectByMiaodaIssue(miaodaIssue);

        } catch (Exception e) {
            log.error("根据喵达投诉问题类型查询映射异常", e);
            return null;
        }
    }

    @Override
    public ComplaintTypeMapping getBestMatchMapping(List<String> issueList) {
        try {
            if (CollectionUtils.isEmpty(issueList)) {
                return null;
            }

            // 返回优先级最高的映射
            return complaintTypeMappingMapper.selectBestMatchMappingByList(issueList);
        } catch (Exception e) {
            log.error("智能匹配投诉类型映射异常", e);
            return null;
        }
    }

    @Override
    public List<ComplaintTypeMapping> getActiveMappings() {
        try {
            return complaintTypeMappingMapper.selectActiveMapping();
        } catch (Exception e) {
            log.error("查询启用的映射配置异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ComplaintTypeMapping> getMappingsByOpDictInfoConfId(Long opDictInfoConfId) {
        try {
            return complaintTypeMappingMapper.selectByOpDictInfoConfId(opDictInfoConfId);
        } catch (Exception e) {
            log.error("根据工单组合配置ID查询映射异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean existsByMiaodaIssue(String miaodaIssue, Long excludeId) {
        try {
            return complaintTypeMappingMapper.existsByMiaodaIssue(miaodaIssue, excludeId);
        } catch (Exception e) {
            log.error("检查映射是否存在异常", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int initDefaultMappings(String operator) {
        try {
            log.info("初始化默认映射配置");

            // 检查是否已有配置
            List<ComplaintTypeMapping> existingMappings = getActiveMappings();
            if (CollectionUtils.isNotEmpty(existingMappings)) {
                log.info("已存在映射配置，跳过初始化");
                return 0;
            }

            // 创建默认映射配置
            List<ComplaintTypeMappingDTO> defaultMappings = createDefaultMappings();

            int count = 0;
            for (ComplaintTypeMappingDTO mappingDTO : defaultMappings) {
                try {
                    if (saveMapping(mappingDTO, operator)) {
                        count++;
                    }
                } catch (Exception e) {
                    log.error("初始化默认映射失败：{}", mappingDTO.getMiaodaIssue(), e);
                }
            }

            log.info("初始化默认映射配置完成，共创建{}条", count);
            return count;

        } catch (Exception e) {
            log.error("初始化默认映射配置异常", e);
            throw new RuntimeException("初始化默认映射配置失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importMappings(List<ComplaintTypeMappingDTO> mappings, String operator) {
        try {
            log.info("导入映射配置，数量：{}", mappings.size());

            if (CollectionUtils.isEmpty(mappings)) {
                return 0;
            }

            int count = 0;
            for (ComplaintTypeMappingDTO mappingDTO : mappings) {
                try {
                    if (mappingDTO.getId() != null) {
                        // 更新
                        if (updateMapping(mappingDTO, operator)) {
                            count++;
                        }
                    } else {
                        // 新增
                        if (saveMapping(mappingDTO, operator)) {
                            count++;
                        }
                    }
                } catch (Exception e) {
                    log.error("导入映射失败：{}", mappingDTO.getMiaodaIssue(), e);
                }
            }

            log.info("导入映射配置完成，共导入{}条", count);
            return count;

        } catch (Exception e) {
            log.error("导入映射配置异常", e);
            throw new RuntimeException("导入映射配置失败：" + e.getMessage());
        }
    }

    @Override
    public List<ComplaintTypeMappingVO> exportMappings(ComplaintTypeMappingQueryDTO queryDTO) {
        try {
            log.info("导出映射配置");

            // 设置大的分页大小以获取所有数据
            queryDTO.setPage(1);
            queryDTO.setPageSize(10000);

            Page<ComplaintTypeMappingVO> page = queryMappingPage(queryDTO);
            return page.getRecords();

        } catch (Exception e) {
            log.error("导出映射配置异常", e);
            throw new RuntimeException("导出映射配置失败：" + e.getMessage());
        }
    }

    /**
     * 创建默认映射配置
     * 注意：以下ID需要根据实际的op_dict_info_conf表中的数据进行调整
     * 请先查询op_dict_info_conf表获取实际的工单组合配置ID：
     * SELECT id, wo_type_detail, wo_type_fir_detail, wo_type_sec_detail, wo_type_thir_detail, wo_type_child_detail
     * FROM op_dict_info_conf WHERE is_status = 1;
     */
    private List<ComplaintTypeMappingDTO> createDefaultMappings() {
        List<ComplaintTypeMappingDTO> defaultMappings = new ArrayList<>();

        // TODO: 请根据实际的op_dict_info_conf表数据调整以下ID值
        // 这里使用的是示例ID，需要替换为实际存在的工单组合配置ID

        ComplaintTypeMappingDTO mapping1 = new ComplaintTypeMappingDTO();
        mapping1.setMiaodaIssue("还款问题");
        // TODO: 替换为实际的工单组合配置ID（从op_dict_info_conf表查询）
        mapping1.setOpDictInfoConfId(1L);  // 工单组合配置ID - 需要替换
        mapping1.setPriority(1);
        mapping1.setRemark("还款相关问题默认映射");
        defaultMappings.add(mapping1);

        ComplaintTypeMappingDTO mapping2 = new ComplaintTypeMappingDTO();
        mapping2.setMiaodaIssue("逾期问题");
        // TODO: 替换为实际的工单组合配置ID（从op_dict_info_conf表查询）
        mapping2.setOpDictInfoConfId(2L);  // 工单组合配置ID - 需要替换
        mapping2.setPriority(2);
        mapping2.setRemark("逾期相关问题默认映射");
        defaultMappings.add(mapping2);

        ComplaintTypeMappingDTO mapping3 = new ComplaintTypeMappingDTO();
        mapping3.setMiaodaIssue("服务态度问题");
        // TODO: 替换为实际的工单组合配置ID（从op_dict_info_conf表查询）
        mapping3.setOpDictInfoConfId(3L);  // 工单组合配置ID - 需要替换
        mapping3.setPriority(3);
        mapping3.setRemark("服务态度问题默认映射");
        defaultMappings.add(mapping3);

        ComplaintTypeMappingDTO mapping4 = new ComplaintTypeMappingDTO();
        mapping4.setMiaodaIssue("其他问题");
        // TODO: 替换为实际的工单组合配置ID（从op_dict_info_conf表查询）
        mapping4.setOpDictInfoConfId(4L);  // 工单组合配置ID - 需要替换
        mapping4.setPriority(99);
        mapping4.setRemark("其他问题默认映射");
        defaultMappings.add(mapping4);

        return defaultMappings;
    }
}
