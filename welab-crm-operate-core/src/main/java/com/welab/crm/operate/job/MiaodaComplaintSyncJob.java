package com.welab.crm.operate.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.welab.crm.operate.config.MiaodaApiConfig;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import com.welab.crm.operate.service.MiaodaApiService;
import com.welab.crm.operate.service.MiaodaAutoReplyConfigService;
import com.welab.crm.operate.util.DateTimeUtil;
import com.welab.crm.operate.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCommands;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * 喵达投诉单同步定时任务
 * 每30分钟自动从喵达平台拉取新投诉单并执行自动回复
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Component
public class MiaodaComplaintSyncJob implements SimpleJob {

    @Resource
    private MiaodaApiService miaodaApiService;

    @Resource
    private MiaodaApiConfig miaodaApiConfig;

    @Resource
    private JedisCommands jedisCommands;

    @Override
    public void execute(ShardingContext shardingContext) {
        if (!miaodaApiConfig.getEnabled() || !miaodaApiConfig.getAutoSyncEnabled()) {
            log.info("喵达API功能或自动同步功能已禁用，跳过同步任务");
            return;
        }

        try {

            log.info("开始执行喵达投诉单同步任务");

            // 执行同步逻辑
            syncNewComplaints();

            log.info("喵达投诉单同步任务执行完成");

        } catch (Exception e) {
            log.error("执行喵达投诉单同步任务异常", e);
        }
    }

    /**
     * 同步新投诉单
     */
    private void syncNewComplaints() {
        try {
            // 计算同步时间范围（最近24小时）
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(24);
            
            // 将 LocalDateTime 转化为 String 格式 "yyyy-MM-dd HH:mm:ss"
            String startTimeStr = startTime.format(DateTimeFormatter.ofPattern(DateTimeUtil.STANDARD_DATETIME_FORMAT));
            String endTimeStr = endTime.format(DateTimeFormatter.ofPattern(DateTimeUtil.STANDARD_DATETIME_FORMAT));

            log.info("同步时间范围：{} - {}", startTime, endTime);

            // 同步投诉单到本地数据库
            int syncCount = miaodaApiService.syncComplaintsToLocal(startTimeStr, endTimeStr);
            log.info("本次同步投诉单数量：{}", syncCount);

        } catch (Exception e) {
            log.error("同步新投诉单异常", e);
            throw e;
        }
    }


    /**
     * 获取任务名称
     */
    public String getJobName() {
        return "MiaodaComplaintSyncJob";
    }

    /**
     * 获取任务描述
     */
    public String getJobDescription() {
        return "喵达投诉单同步定时任务，每30分钟自动从喵达平台拉取新投诉单、自动创建工单并执行自动回复";
    }

}
