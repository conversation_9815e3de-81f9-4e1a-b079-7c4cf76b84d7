package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.welab.crm.base.vo.StaffVO;
import com.welab.crm.base.workflow.busi.constant.WFWorkorderStateEnum;
import com.welab.crm.base.workflow.busi.service.WFHisBusiService;
import com.welab.crm.base.workflow.busi.service.WFRunBusiService;
import com.welab.crm.base.workflow.common.dto.config.WFConfigAssignedDTO;
import com.welab.crm.base.workflow.common.dto.config.WFConfigTransitionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunExecutionDTO;
import com.welab.crm.base.workflow.common.dto.run.WFRunTaskDTO;
import com.welab.crm.interview.dto.UserDetailQueryDTO;
import com.welab.crm.interview.service.LoanApplicationService;
import com.welab.crm.interview.service.UserInfoService;
import com.welab.crm.interview.vo.PersonalDetailsVoExpand;
import com.welab.crm.interview.vo.UserInfoForChat;
import com.welab.crm.interview.vo.loan.LoanApplicationVO;
import com.welab.crm.operate.domain.ExternalComplaintOrder;
import com.welab.crm.operate.domain.WoTask;
import com.welab.crm.operate.dto.dict.DictInfoConfReqDTO;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintDetailDTO;
import com.welab.crm.operate.dto.externalCompalaint.ExternalComplaintQueryDTO;
import com.welab.crm.operate.dto.externalCompalaint.ReminderDTO;
import com.welab.crm.operate.dto.staff.ColStaffReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderLoanReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderSubmitReqDTO;
import com.welab.crm.operate.mapper.ExternalComplaintOrderMapper;
import com.welab.crm.operate.mapper.WoTaskMapper;
import com.welab.crm.operate.service.*;
import com.welab.crm.operate.util.AesUtils;
import com.welab.crm.operate.util.CommonUtil;
import com.welab.crm.operate.util.SecurityUtil;
import com.welab.crm.operate.vo.dict.DictInfoConfResVO;
import com.welab.crm.operate.vo.staff.ColStaffResVO;
import com.welab.crm.operate.vo.workorder.*;
import com.welab.enums.LoanApplicationStateEnum;
import com.welab.exception.FastRuntimeException;
import com.welab.loanapplication.interfaces.facade.LoanApplicationServiceFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExternalComplaintServiceImpl implements ExternalComplaintService {

	@Resource
	private WorkOrderService workOrderService;

	@Resource
	private ICrmOrgStaffService crmOrgStaffService;

	@Resource
	private UserInfoService userInfoService;

	@Resource
	private ICrmDictInfoConfService crmDictInfoConfService;

	@Resource
	private LoanApplicationServiceFacade loanApplicationServiceFacade;

	@Resource
	private LoanApplicationService loanApplicationService;

	@Resource
	private ExternalComplaintOrderMapper externalComplaintOrderMapper;

	@Resource
	private CustomerService customerService;
	@Resource
	private CallbackSummaryService callbackSummaryService;
	
	@Resource
	private WoTaskMapper woTaskMapper;
	
	@Resource
	private WorkOrderServiceImpl workOrderServiceImpl;

	@Resource
	private WFRunBusiService runBusiService;
	
	@Resource
	private WFHisBusiService hisBusiService;

	private static final List<String> LOG_GROUP_LIST = Collections.unmodifiableList(Arrays.asList("tsz"));

	@Override
	public void addExternalComplaint(ExternalComplaintDetailDTO externalComplaintDetailDTO) {

		// 查询提交人信息
		ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(externalComplaintDetailDTO.getLoginName());
		// 查询客户信息
		PersonalDetailsVoExpand userInfo = queryUserInfo(externalComplaintDetailDTO);
		WorkOrderSubmitReqDTO submitReqDTO = buildOrderSubmitReqDTO(externalComplaintDetailDTO, staff, userInfo);

		StaffVO staffVO = new StaffVO();
		BeanUtils.copyProperties(staff, staffVO);
		String orderNo = "";
		orderNo = workOrderService.externalSameOrderCheck(submitReqDTO, staffVO.getId());
		if (StringUtils.isNotBlank(orderNo)) {
			// 如果工单已存在，则直接返回
			externalComplaintDetailDTO.setOrderNo(orderNo);
			externalComplaintOrderMapper.insert(buildComplaintOrder(externalComplaintDetailDTO));
			return;
		}
		orderNo = workOrderService.submitWorkOrder(submitReqDTO, staffVO);

		// 保存工单投诉记录
		externalComplaintDetailDTO.setOrderNo(orderNo);
		externalComplaintOrderMapper.insert(buildComplaintOrder(externalComplaintDetailDTO));

	}

	@Override
	public ExternalComplaintDetailDTO queryNameByOrderNo(String channelOrderNo, String debtOrderNo) {
		if (StringUtils.isBlank(channelOrderNo) && StringUtils.isBlank(debtOrderNo)) {
			throw new FastRuntimeException("渠道订单号和借据号不能同时为空");
		}
		UserDetailQueryDTO detailQueryDTO = new UserDetailQueryDTO();
		detailQueryDTO.setExternalOrderNo(debtOrderNo);
		detailQueryDTO.setChannelOrderNo(channelOrderNo);
		UserInfoForChat userInfoForChat = userInfoService.queryUserInfoSimple(detailQueryDTO);
		if (Objects.nonNull(userInfoForChat)) {
			ExternalComplaintDetailDTO dto = new ExternalComplaintDetailDTO();
			dto.setName(SecurityUtil.maskNameMidChar(userInfoForChat.getName()));
			dto.setAesName(AesUtils.encrypt(userInfoForChat.getName()));
			return dto;
		}

		return null;
	}


	@Override
	public Page<ExternalComplaintListVO> queryPage(ExternalComplaintQueryDTO dto) {
//		CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
		Page<ExternalComplaintListVO> page =
				externalComplaintOrderMapper.selectOrderHistoryPage(new Page<>(dto.getCurPage(), dto.getPageSize()), dto);

		if (CollectionUtils.isNotEmpty(page.getRecords())) {
			List<String> busiKeys = page.getRecords().stream().map(ExternalComplaintListVO::getOrderNo).collect(Collectors.toList());
			Map<String, WFRunExecutionDTO> runExecution =  queryExecutions(busiKeys);
			Map<Long, String> staffIdCnoMap = queryStaffIdCnoMap();

			page.getRecords().forEach(item -> {
				item.setOrderStatus(WFWorkorderStateEnum.getDesc(item.getOrderStatus()));
				item.setName(SecurityUtil.maskNameMidChar(item.getName()));
				buildCno(item, staffIdCnoMap, runExecution);
			});
		}
		return page;
	}

	private Map<Long, String> queryStaffIdCnoMap() {
		ColStaffReqDTO reqDTO = new ColStaffReqDTO();
		reqDTO.setIsStatus(1);
		return crmOrgStaffService.getColStaffList(reqDTO).stream()
				.filter(item -> StringUtils.isNotBlank(item.getCno()))
				.collect(Collectors.toMap(ColStaffResVO::getId, ColStaffResVO::getCno, (v1, v2) -> v1));
	}
	private Map<String, WFRunExecutionDTO> queryExecutions(List<String> busiKeys) {
		Map<String, WFRunExecutionDTO> runExecution = runBusiService.getRunExecutionByBusiKeys("NornalWO", busiKeys);
		Map<String, WFRunExecutionDTO> hisRunExecution = hisBusiService.getHisExecutionByBusiKeys("NornalWO", busiKeys);
		runExecution.putAll(hisRunExecution);
		return runExecution;
	}

	private void buildCno(ExternalComplaintListVO item, Map<Long, String> staffIdCnoMap, Map<String, WFRunExecutionDTO> runExecution) {
		WFRunExecutionDTO executionDTO = runExecution.get(item.getOrderNo());
		if (executionDTO != null) {
			List<WFRunTaskDTO> hisTask = executionDTO.getHisTask().stream()
					.sorted(Comparator.comparing(WFRunTaskDTO::getGmtCreate).reversed()).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(hisTask)) {
				WFRunTaskDTO task = hisTask.get(hisTask.size() - 1);
				if (StringUtils.isNotBlank(task.getStaffId())) {
					item.setCno(staffIdCnoMap.get(Long.valueOf(task.getStaffId())));
				}
			}
		}
		
	}

	@Override
	public ExternalComplaintDetailDTO queryDetail(Long id, String loginName) {

		ExternalComplaintOrder complaintOrder = externalComplaintOrderMapper.selectById(id);
		if (Objects.nonNull(complaintOrder) && !complaintOrder.getLoginName().equals(loginName)) {
			return null;
		}
		ExternalComplaintDetailDTO detailDTO = buildExternalComplaintDTO(complaintOrder);
		ExecutionVO executionVO = externalComplaintOrderMapper.selectExecutionById(id, loginName);
		detailDTO.setOrderStatus(WFWorkorderStateEnum.getDesc(executionVO.getOrderStatus()));
		return detailDTO;
	}

	@Override
	public List<ExternalComplaintListVO> queryList(ExternalComplaintQueryDTO dto) {
//		CommonUtils.checkTimeThan31(dto.getStartTime(), dto.getEndTime());
		List<ExternalComplaintListVO> list = externalComplaintOrderMapper.selectOrderHistoryList(dto);
		list.forEach(item -> {
			item.setName(SecurityUtil.maskNameMidChar(item.getName()));
			item.setOrderStatus(WFWorkorderStateEnum.getDesc(item.getOrderStatus()));
		});

		return list;
	}

	private Wrapper<ExternalComplaintOrder> buildQueryWrapper(ExternalComplaintQueryDTO dto) {
		return Wrappers.lambdaQuery(ExternalComplaintOrder.class)
				.ge(StringUtils.isNotBlank(dto.getStartTime()), ExternalComplaintOrder::getGmtCreate, dto.getStartTime())
				.le(StringUtils.isNotBlank(dto.getEndTime()), ExternalComplaintOrder::getGmtCreate, dto.getEndTime())
				.eq(ExternalComplaintOrder::getLoginName, dto.getLoginName())
				.eq(StringUtils.isNotBlank(dto.getIdNo()), ExternalComplaintOrder::getIdNo, dto.getIdNo())
				.eq(StringUtils.isNotBlank(dto.getMobile()), ExternalComplaintOrder::getMobile, dto.getMobile())
				.like(StringUtils.isNotBlank(dto.getChannelOrderNo()), ExternalComplaintOrder::getChannelOrderNo, dto.getChannelOrderNo())
				.like(StringUtils.isNotBlank(dto.getDebtOrderNo()), ExternalComplaintOrder::getDebtOrderNo, dto.getDebtOrderNo())
				.orderByDesc(ExternalComplaintOrder::getId);
	}

	private ExternalComplaintDetailDTO buildExternalComplaintDTO(ExternalComplaintOrder order) {
		ExternalComplaintDetailDTO dto = new ExternalComplaintDetailDTO();
		dto.setId(order.getId());
		dto.setLoginName(order.getLoginName());
		if (StringUtils.isNotBlank(order.getChannelOrderNo())) {
			dto.setChannelOrderNo(Arrays.asList(StringUtils.split(order.getChannelOrderNo(), ",")));
		}
		if (StringUtils.isNotBlank(order.getDebtOrderNo())) {
			dto.setDebtOrderNo(Arrays.asList(StringUtils.split(order.getDebtOrderNo(), ",")));
		}
		dto.setIdNo(SecurityUtil.maskCnid(order.getIdNo()));
		dto.setName(SecurityUtil.maskNameMidChar(order.getName()));
		dto.setMobile(SecurityUtil.maskMobile2(order.getMobile()));
		if (StringUtils.isNotBlank(order.getMobileBak())) {
			String[] mobileBaks = StringUtils.split(order.getMobileBak(), ",");
			List<String> mobileBakList = new ArrayList<>();
			for (String mobileBak : mobileBaks) {
				mobileBakList.add(SecurityUtil.maskMobile2(mobileBak));
			}
			dto.setMobileBakList(mobileBakList);
		}
		dto.setEmail(order.getEmail());
		dto.setOrderNo(order.getOrderNo());
		dto.setUrgentFlag(order.getUrgentFlag());
		dto.setCallbackFlag(order.getCallbackFlag());
		dto.setQuickOrderId(order.getQuickOrderId());
		dto.setQuickOrder(order.getQuickOrder());
		dto.setComplaintsChannel(order.getComplaintsChannel());
		dto.setFundName(order.getFundName());
		dto.setDescription(order.getDescription());
		dto.setType(order.getType());
		dto.setOrderOneClass(order.getOrderOneClass());
		dto.setOrderTwoClass(order.getOrderTwoClass());
		dto.setOrderThreeClass(order.getOrderThreeClass());
		dto.setOrderCase(order.getOrderCase());
		dto.setUserType(order.getUserType());
		dto.setOrderStatus(WFWorkorderStateEnum.getDesc(dto.getOrderStatus()));

		// 查询关联工单的全部附件，然后筛选出提交人上传的附件
		List<WoAttachmentVO> woAttachmentVOS = workOrderService.queryAttachmentByOrderNo(order.getOrderNo());
		ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(dto.getLoginName());
		dto.setFileList(woAttachmentVOS.stream().filter(attach -> isShowLog(attach.getGroupCode(), staff.getGroupCode())).collect(Collectors.toList()));
		return dto;

	}

	private ExternalComplaintOrder buildComplaintOrder(ExternalComplaintDetailDTO externalComplaintDetailDTO) {
		ExternalComplaintOrder order = new ExternalComplaintOrder();
		order.setLoginName(externalComplaintDetailDTO.getLoginName());
		if (CollectionUtils.isNotEmpty(externalComplaintDetailDTO.getChannelOrderNo())) {
			order.setChannelOrderNo(String.join(",", externalComplaintDetailDTO.getChannelOrderNo()));

		}
		if (CollectionUtils.isNotEmpty(externalComplaintDetailDTO.getDebtOrderNo())) {
			order.setDebtOrderNo(String.join(",", externalComplaintDetailDTO.getDebtOrderNo()));
		}
		order.setIdNo(externalComplaintDetailDTO.getIdNo());
		order.setName(externalComplaintDetailDTO.getName());
		order.setMobile(externalComplaintDetailDTO.getMobile());
		if (CollectionUtils.isNotEmpty(externalComplaintDetailDTO.getMobileBakList())) {
			order.setMobileBak(String.join(",", externalComplaintDetailDTO.getMobileBakList()));
		}
		order.setEmail(externalComplaintDetailDTO.getEmail());
		order.setOrderNo(externalComplaintDetailDTO.getOrderNo());
		order.setUrgentFlag(externalComplaintDetailDTO.getUrgentFlag());
		order.setCallbackFlag(externalComplaintDetailDTO.getCallbackFlag());
		order.setQuickOrderId(externalComplaintDetailDTO.getQuickOrderId());
		order.setQuickOrder(externalComplaintDetailDTO.getQuickOrder());
		order.setComplaintsChannel(externalComplaintDetailDTO.getComplaintsChannel());
		order.setFundName(externalComplaintDetailDTO.getFundName());
		order.setDescription(externalComplaintDetailDTO.getDescription());
		order.setType(externalComplaintDetailDTO.getType());
		order.setOrderOneClass(externalComplaintDetailDTO.getOrderOneClass());
		order.setOrderTwoClass(externalComplaintDetailDTO.getOrderTwoClass());
		order.setOrderThreeClass(externalComplaintDetailDTO.getOrderThreeClass());
		order.setOrderCase(externalComplaintDetailDTO.getOrderCase());
		order.setUserType(externalComplaintDetailDTO.getUserType());
		return order;
	}

	private WorkOrderSubmitReqDTO buildOrderSubmitReqDTO(ExternalComplaintDetailDTO externalComplaintDetailDTO, ColStaffResVO staff, PersonalDetailsVoExpand userInfo) {
		WorkOrderSubmitReqDTO submitReqDTO = new WorkOrderSubmitReqDTO();
		submitReqDTO.setAssignedDTO(getAssignedDTO());
		submitReqDTO.setCallbackFlag(externalComplaintDetailDTO.getCallbackFlag());
		submitReqDTO.setComplaintsChannel(externalComplaintDetailDTO.getComplaintsChannel());
		submitReqDTO.setCustId(String.valueOf(userInfo.getId()));
		submitReqDTO.setDescription(externalComplaintDetailDTO.getDescription());
		submitReqDTO.setExecutionDTO(getExecutionDTO(staff));
		submitReqDTO.setFundName(externalComplaintDetailDTO.getFundName());
		submitReqDTO.setMatchUp(getMatchup(submitReqDTO.getComplaintsChannel(), submitReqDTO.getFundName()));
		submitReqDTO.setType(externalComplaintDetailDTO.getType());
		submitReqDTO.setOrderOneClass(externalComplaintDetailDTO.getOrderOneClass());
		submitReqDTO.setOrderTwoClass(externalComplaintDetailDTO.getOrderTwoClass());
		submitReqDTO.setOrderThreeClass(externalComplaintDetailDTO.getOrderThreeClass());
		submitReqDTO.setOrderCase(externalComplaintDetailDTO.getOrderCase());
		submitReqDTO.setProcessCode(workOrderService.getProcessCode());
		submitReqDTO.setEmail(externalComplaintDetailDTO.getEmail());
		submitReqDTO.setMobileBakList(externalComplaintDetailDTO.getMobileBakList());
		submitReqDTO.setFileList(externalComplaintDetailDTO.getFileList());


		List<LoanApplicationVO> loans = loanApplicationService.getLoanApplicationList(Long.valueOf(userInfo.getUuid()));
		submitReqDTO.setSucLoanCount((int) loans.stream()
				.filter(loan -> Arrays.asList(LoanApplicationStateEnum.DISBURSED.getText(), LoanApplicationStateEnum.CLOSED.getText()).contains(loan.getState()))
				.count());
		submitReqDTO.setCurrentLoanCount((int) loans.stream()
				.filter(loan -> LoanApplicationStateEnum.DISBURSED.getText().equals(loan.getState()))
				.count());
		submitReqDTO.setLoanList(getLoanList(loans, externalComplaintDetailDTO));
		submitReqDTO.setUrgentFlag(externalComplaintDetailDTO.getUrgentFlag());
		submitReqDTO.setTransitionDTO(getTransitionDTO());

		return submitReqDTO;
	}

	private List<WorkOrderLoanReqDTO> getLoanList(List<LoanApplicationVO> loans, ExternalComplaintDetailDTO externalComplaintDetailDTO) {
		List<WorkOrderLoanReqDTO> loanReqDTOS = new ArrayList<>();
		if (externalComplaintDetailDTO.getUserType().equals("source")) {
			List<String> applicationIdList = new ArrayList<>();
			externalComplaintDetailDTO.getChannelOrderNo().forEach(orderNo -> {
				String applicationId = userInfoService.queryApplicationIdByExternalOrderNo(orderNo);
				applicationIdList.add(applicationId);
			});
			
			loans.stream().filter(loan -> applicationIdList.contains(loan.getApplicationId()))
					.forEach(loan -> {
						loanReqDTOS.add(buildLoanReqDTO(loan));
					});


		} else if (externalComplaintDetailDTO.getUserType().equals("partner")) {
			loans.stream().filter(loan -> externalComplaintDetailDTO.getDebtOrderNo().contains(loan.getExternalOrderNo()))
					.forEach(loan -> {
						loanReqDTOS.add(buildLoanReqDTO(loan));
					});
		}
		return loanReqDTOS;
	}

	private WorkOrderLoanReqDTO buildLoanReqDTO(LoanApplicationVO loan) {
		WorkOrderLoanReqDTO loanReqDTO = new WorkOrderLoanReqDTO();
		BeanUtils.copyProperties(loan, loanReqDTO);
		loanReqDTO.setApplicationId(loan.getApplicationId());
		loanReqDTO.setProductName(loan.getProductName());
		loanReqDTO.setApplyTime(loan.getAppliedAt());
		loanReqDTO.setApprovalTime(loan.getApprovedAt());
		loanReqDTO.setConfirmedTime(loan.getConfirmedTime());
		loanReqDTO.setLoanTime(loan.getDisbursedTime());
		loanReqDTO.setChannelCode(loan.getOrigin());
		loanReqDTO.setPartnerCode(loan.getPartnerName());
		loanReqDTO.setApplyAmount(loan.getAppliedAmount());
		loanReqDTO.setApprovalAmount(loan.getApprovedAmount());
		loanReqDTO.setApplyTenor(loan.getAppliedTenor());
		loanReqDTO.setApprovalTenor(loan.getApprovedTenor());
		loanReqDTO.setStatus(loan.getState());
		loanReqDTO.setTotalRate(String.valueOf(loan.getTotalRate()));
		loanReqDTO.setPartnerCodeNew(loan.getPartnerCodeNew());
		return loanReqDTO;
	}

	private WFConfigTransitionDTO getTransitionDTO() {
		return workOrderService.getTransitionDTO();
	}

	private String getMatchup(String complaintsChannel, String fundName) {
		if (StringUtils.isBlank(complaintsChannel)) {
			return "";
		}
		JSONArray jsonArray = new JSONArray();
		for (String channel : complaintsChannel.split(",")) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("complaintsChannel", channel);
			jsonObject.put("fundName", Collections.singletonList(StringUtils.isBlank(fundName) ? "" : fundName));
			jsonArray.add(jsonObject);
		}
		return jsonArray.toJSONString();
	}

	private WFRunExecutionDTO getExecutionDTO(ColStaffResVO staff) {
		return workOrderService.getExecutionDTO(staff.getId());
	}

	private PersonalDetailsVoExpand queryUserInfo(ExternalComplaintDetailDTO externalComplaintDetailDTO) {
		PersonalDetailsVoExpand result = null;
		if (StringUtils.isNotBlank(externalComplaintDetailDTO.getMobile())) {
			UserDetailQueryDTO queryDTO = new UserDetailQueryDTO();
			queryDTO.setMobile(externalComplaintDetailDTO.getMobile());
			result = userInfoService.queryUserInfo(queryDTO);
		} else if (CollectionUtils.isNotEmpty(externalComplaintDetailDTO.getChannelOrderNo())) {
			// 如果没有手机号，则通过渠道订单号查询
			UserDetailQueryDTO queryDTO = new UserDetailQueryDTO();
			queryDTO.setChannelOrderNo(externalComplaintDetailDTO.getChannelOrderNo().get(0));
			result = userInfoService.queryUserInfo(queryDTO);
		} else if (CollectionUtils.isNotEmpty(externalComplaintDetailDTO.getDebtOrderNo())) {
			UserDetailQueryDTO queryDTO = new UserDetailQueryDTO();
			queryDTO.setExternalOrderNo(externalComplaintDetailDTO.getDebtOrderNo().get(0));
			result = userInfoService.queryUserInfo(queryDTO);
		}

		if (Objects.nonNull(result) && StringUtils.isNotBlank(result.getMobile())) {
			Long id = customerService.saveUserInfoCash(result);
			result.setId(id);
			return result;
		}

		throw new FastRuntimeException("无法查询到客户信息，请提供有效的手机号或渠道订单号");

	}


	private WFConfigAssignedDTO getAssignedDTO() {
		return workOrderService.getAssignedDTO();
	}


	@Override
	public List<DictInfoConfResVO> getQuickOrderOption(String userType) {
		DictInfoConfReqDTO reqDTO = new DictInfoConfReqDTO();
		reqDTO.setIsStatus(1);
		reqDTO.setFastTargetUser(userType);
		reqDTO.setFastStatus("Y");
		reqDTO.setCurPage(1);
		reqDTO.setPageSize(1000);
		return crmDictInfoConfService.getDictInfosConf(reqDTO).getRecords();
	}

	@Override
	public List<OrderContactVO> queryOrderContactList(Long id, String loginName) {
		List<OrderContactVO> orderContactVOS = externalComplaintOrderMapper.selectOrderContactList(id, loginName);
		orderContactVOS.forEach(item -> {
			StringBuilder callSummary = new StringBuilder(item.getCallSummary());
			// 后续的小结内容从json串中解析出来
			JSONObject jsonObject = JSON.parseObject(item.getReasonType());
			Set<String> typeSet = jsonObject.keySet();
			callSummary.append("(").append(Joiner.on(",").join(typeSet)).append(")");
			item.setCallSummary(callSummary.toString());
			item.setMobile(SecurityUtil.maskMobilePro(item.getMobile()));
		});

		return orderContactVOS;
	}

	@Override
	public List<WorkOrderLogVO> queryOrderLogList(Long id, String loginName) {
		ExecutionVO executionVO = externalComplaintOrderMapper.selectExecutionById(id, loginName);
		if (Objects.isNull(executionVO)) {
			throw new FastRuntimeException("工单不存在或无权限");
		}
		List<WorkOrderLogVO> logList = workOrderService.queryWorkOrderLogList(executionVO.getExecutionId());
		logList.addAll(callbackSummaryService.queryCallbackSummaryLogList(executionVO.getOrderNo()));
		ColStaffResVO staff = crmOrgStaffService.getColStaffByLoginName(loginName);
		return logList.stream().filter(item -> isShowLog(item.getGroupCode(), staff.getGroupCode()))
				.map(this::maskLog)
				.sorted(Comparator.comparing(WorkOrderLogVO::getGmtCreate).reversed()).collect(Collectors.toList());
	}

	private WorkOrderLogVO maskLog(WorkOrderLogVO item) {
		if ("手动调剂".equals(item.getOperate())){
			// 手动调剂的日志是 "操作人:人名"，需要把人名处理掉
			String comment = item.getComment();
			item.setComment(comment.replaceAll("(?<=操作人:).+", "***"));
		}
		return item;
	}
	

	private boolean isShowLog(String groupCode, String currentGroupCode) {
		if (currentGroupCode.equals(groupCode)) {
			return true;
		}
		for (String logGroup : LOG_GROUP_LIST) {
			if (groupCode.equals(logGroup)) {
				return true;
			}
		}
		return false;
	}


	@Override
	public void reminderOrder(ReminderDTO reminderDTO, String loginName) {
		ColStaffResVO colStaff = crmOrgStaffService.getColStaffByLoginName(loginName);
		WorkOrderSubmitReqDTO reqDTO = new WorkOrderSubmitReqDTO();
		ExecutionVO executionVO = externalComplaintOrderMapper.selectExecutionById(reminderDTO.getId(), colStaff.getLoginName());
		if (Objects.isNull(executionVO) || CommonUtil.checkIsEnd(executionVO.getOrderStatus())) {
			throw new FastRuntimeException("催单失败，工单不存在或无权限或者已结案");
		}
		
		reqDTO.setId(executionVO.getWoTaskId());
		WFRunExecutionDTO executionDTO = new WFRunExecutionDTO();
		executionDTO.setExecutionId(executionVO.getExecutionId());
		reqDTO.setExecutionDTO(executionDTO);
		reqDTO.setOpinion(reminderDTO.getOpinion());
		workOrderService.reminderWorkOrder(reqDTO, String.valueOf(colStaff.getId()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateOrder(ExternalComplaintDetailDTO dto, String loginName) {
		ExternalComplaintOrder order = externalComplaintOrderMapper.selectById(dto.getId());
		if (Objects.isNull(order) || !order.getLoginName().equals(loginName)) {
			throw new FastRuntimeException("工单不存在或无权限修改");
		}

		ExecutionVO executionVO = externalComplaintOrderMapper.selectExecutionById(dto.getId(), loginName);
		if (Objects.isNull(executionVO) || CommonUtil.checkIsEnd(executionVO.getOrderStatus())) {
			throw new FastRuntimeException("工单已结案，无法修改");
		}


		order.setComplaintsChannel(dto.getComplaintsChannel());
		order.setFundName(dto.getFundName());
		if (CollectionUtils.isNotEmpty(dto.getMobileBakList())){
			List<String> trueMobileBakList = dto.getMobileBakList().stream().filter(item -> !item.contains("*")).collect(Collectors.toList());
			dto.setMobileBakList(trueMobileBakList);
			if (CollectionUtils.isNotEmpty(trueMobileBakList)){
				order.setMobileBak(String.join(",", addMobileBak(trueMobileBakList, order.getMobileBak())));
			}
		}
		externalComplaintOrderMapper.updateById(order);

		WoTask woTask = woTaskMapper.selectOne(Wrappers.lambdaQuery(WoTask.class).eq(WoTask::getOrderNo, order.getOrderNo()));
		if (Objects.isNull(woTask)) {
			throw new FastRuntimeException("工单任务不存在");
		}

		workOrderServiceImpl.mergeMatchUp(woTask, getMatchup(dto.getComplaintsChannel(), dto.getFundName()));

		if (CollectionUtils.isNotEmpty(dto.getMobileBakList())) {
			if (StringUtils.isNotBlank(woTask.getMobileBaks())) {
				StringBuilder sb = new StringBuilder(woTask.getMobileBaks());
				for (String mobile : dto.getMobileBakList()) {
					if (!woTask.getMobileBaks().contains(mobile)) {
						sb.append(",").append(mobile);
					}
				}
				woTask.setMobileBaks(sb.toString());
			} else {
				woTask.setMobileBaks(String.join(",", dto.getMobileBakList()));
			}
		}
		
		woTaskMapper.updateById(woTask);

	}

	private List<String> addMobileBak(List<String> trueMobileBakList, String existMobileBaks) {
		List<String> mobileBakList;
		if (StringUtils.isNotBlank(existMobileBaks)) {
			mobileBakList = new ArrayList<>(Arrays.asList(existMobileBaks.split(",")));
		} else {
			mobileBakList = new ArrayList<>();
		}
		trueMobileBakList.forEach(item -> {
			if (!mobileBakList.contains(item)) {
				mobileBakList.add(item);
			}
		});

		return mobileBakList;

	}

	@Override
	public String checkOrderNo(String loginName, Long id) {
		if (Objects.isNull(id)){
			return null;
		}
		ExternalComplaintOrder order = externalComplaintOrderMapper.selectById(id);
		if (Objects.isNull(order) || !order.getLoginName().equals(loginName)) {
			throw new FastRuntimeException("工单不存在或无权限修改");
		}
		
		return order.getOrderNo();
	}
}
