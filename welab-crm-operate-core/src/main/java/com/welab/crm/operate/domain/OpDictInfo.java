package com.welab.crm.operate.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("op_dict_info")
public class OpDictInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @TableField(fill = FieldFill.INSERT) 
    private Long id;
    /**
     * 属性分组
     */
    private String category;

    /**
     * 自定义类型
     */
    private String type;

    /**
     * 配置说明
     */
    private String content;

    /**
     * 描述
     */
    private String detail;

    /**
     * 状态 1-有效 0-无效
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     * 排序字段，数字越大，排得越前
     */
    private Integer sort;


}
