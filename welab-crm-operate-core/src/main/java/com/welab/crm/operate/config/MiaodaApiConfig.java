package com.welab.crm.operate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 喵达API配置类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@Component
@ConfigurationProperties(prefix = "miaoda.api")
public class MiaodaApiConfig {

    /**
     * 喵达API基础URL
     */
    private String baseUrl;

    /**
     * 商家UID
     */
    private Integer uid;

    /**
     * 商家密钥
     */
    private String key;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes = 3;

    /**
     * 重试间隔时间（毫秒）
     */
    private Integer retryInterval = 1000;

    /**
     * 是否启用喵达API功能
     */
    private Boolean enabled;

    /**
     * 是否启用自动同步
     */
    private Boolean autoSyncEnabled;

    /**
     * 同步批次大小
     */
    private Integer syncBatchSize = 20;

    /**
     * Token缓存时间（秒）
     */
    private Integer tokenCacheTime = 10800; // 3小时

    /**
     * 获取完整的API URL
     *
     * @param path API路径
     * @return 完整URL
     */
    public String getFullUrl(String path) {
        if (baseUrl.endsWith("/") && path.startsWith("/")) {
            return baseUrl + path.substring(1);
        } else if (!baseUrl.endsWith("/") && !path.startsWith("/")) {
            return baseUrl + "/" + path;
        } else {
            return baseUrl + path;
        }
    }
}
