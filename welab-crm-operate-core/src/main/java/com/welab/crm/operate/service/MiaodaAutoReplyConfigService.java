package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.welab.crm.operate.domain.OpDictInfo;
import com.welab.crm.operate.mapper.OpDictInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 喵达自动回复配置服务
 * 从 op_dict_info 表中读取自动回复相关配置
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Service
@Slf4j
public class MiaodaAutoReplyConfigService {
    
    @Resource
    private OpDictInfoMapper opDictInfoMapper;
    
    private static final String CONFIG_CATEGORY = "miaoda_auto_reply";
    
    /**
     * 获取自动回复开关状态
     */
    public boolean isAutoReplyEnabled() {
        return getBooleanConfig("enabled", false);
    }
    
    /**
     * 获取自动回复内容
     */
    public String getAutoReplyContent() {
        return getStringConfig("content", "您好，我们已收到您的投诉，正在处理中，请耐心等待。如有疑问，请联系客服。");
    }
    
    /**
     * 获取回复时限（小时）
     */
    public int getReplyTimeLimit() {
        return getIntConfig("time_limit", 3);
    }
    
    /**
     * 获取同步任务执行频率（分钟）
     */
    public int getSyncInterval() {
        return getIntConfig("sync_interval", 30);
    }
    
    /**
     * 获取是否隐藏回复内容
     */
    public int getHideContent() {
        return getIntConfig("hide_content", 0);
    }
    
    /**
     * 获取是否隐藏回复附件
     */
    public int getHideAttach() {
        return getIntConfig("hide_attach", 0);
    }
    
    /**
     * 获取字符串类型配置
     */
    private String getStringConfig(String type, String defaultValue) {
        try {
            QueryWrapper<OpDictInfo> wrapper = new QueryWrapper<>();
            wrapper.eq("category", CONFIG_CATEGORY)
                   .eq("type", type)
                   .eq("status", 1); // status=0表示有效
            
            OpDictInfo config = opDictInfoMapper.selectOne(wrapper);
            return config != null ? config.getContent() : defaultValue;
        } catch (Exception e) {
            log.warn("获取配置失败，使用默认值: type={}, default={}", type, defaultValue, e);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔类型配置
     */
    private boolean getBooleanConfig(String type, boolean defaultValue) {
        String value = getStringConfig(type, String.valueOf(defaultValue));
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }
    
    /**
     * 获取整数类型配置
     */
    private int getIntConfig(String type, int defaultValue) {
        String value = getStringConfig(type, String.valueOf(defaultValue));
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("配置值格式错误，使用默认值: type={}, value={}, default={}", type, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 刷新配置缓存（如果需要的话）
     */
    public void refreshConfig() {
        log.info("刷新喵达自动回复配置");
        // 这里可以添加缓存刷新逻辑
    }
    
    /**
     * 获取所有配置信息（用于调试）
     */
    public String getAllConfigs() {
        StringBuilder sb = new StringBuilder();
        sb.append("自动回复开关: ").append(isAutoReplyEnabled()).append("\n");
        sb.append("回复内容: ").append(getAutoReplyContent()).append("\n");
        sb.append("回复时限: ").append(getReplyTimeLimit()).append("小时\n");
        sb.append("同步频率: ").append(getSyncInterval()).append("分钟\n");
        sb.append("隐藏内容: ").append(getHideContent()).append("\n");
        sb.append("隐藏附件: ").append(getHideAttach()).append("\n");
        return sb.toString();
    }
}
