package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ComplaintTypeMapping;
import com.welab.crm.operate.dto.miaoda.ComplaintTypeMappingQueryDTO;
import com.welab.crm.operate.vo.miaoda.ComplaintTypeMappingVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 投诉类型映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface ComplaintTypeMappingMapper extends BaseMapper<ComplaintTypeMapping> {

    /**
     * 分页查询投诉类型映射列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 映射列表
     */
    Page<ComplaintTypeMappingVO> selectMappingPage(Page<ComplaintTypeMappingVO> page, @Param("query") ComplaintTypeMappingQueryDTO queryDTO);

    /**
     * 根据喵达投诉问题类型查询映射
     *
     * @param miaodaIssue 喵达投诉问题类型
     * @return 映射信息
     */
    ComplaintTypeMapping selectByMiaodaIssue(@Param("miaodaIssue") String miaodaIssue);

    /**
     * 查询所有启用的映射配置
     *
     * @return 映射列表
     */
    List<ComplaintTypeMapping> selectActiveMapping();

    /**
     * 根据工单组合配置ID查询映射
     *
     * @param opDictInfoConfId 工单组合配置ID
     * @return 映射列表
     */
    List<ComplaintTypeMapping> selectByOpDictInfoConfId(@Param("opDictInfoConfId") Long opDictInfoConfId);

    /**
     * 模糊匹配投诉问题类型
     *
     * @param issue 投诉问题
     * @return 最匹配的映射
     */
    ComplaintTypeMapping selectBestMatchMapping(@Param("issue") String issue);


    /**
     * 模糊匹配投诉问题类型
     *
     * @param issueList 投诉问题列表
     * @return 最匹配的映射
     */
    ComplaintTypeMapping selectBestMatchMappingByList(@Param("issueList") List<String> issueList);

    /**
     * 检查映射是否存在
     *
     * @param miaodaIssue 喵达投诉问题类型
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByMiaodaIssue(@Param("miaodaIssue") String miaodaIssue, @Param("excludeId") Long excludeId);
}
