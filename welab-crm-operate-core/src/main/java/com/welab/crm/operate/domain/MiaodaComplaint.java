package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.welab.crm.operate.vo.miaoda.MiaodaAttachmentVO;
import com.welab.crm.operate.vo.miaoda.MiaodaCompleteInfoVO;
import com.welab.crm.operate.vo.miaoda.MiaodaReplyDetailVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 喵达投诉单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("miaoda_complaint")
public class MiaodaComplaint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 喵达投诉单号
     */
    private String sn;

    /**
     * 关联工单编号
     */
    private String workOrderNo;

    /**
     * 投诉标题
     */
    private String title;

    /**
     * 投诉人昵称
     */
    private String nickname;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 投诉时预留手机号
     */
    private String compPhone;

    /**
     * 涉诉单号
     */
    private String privacy;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 投诉问题
     */
    private String issue;

    /**
     * 投诉要求
     */
    private String appeal;

    /**
     * 涉诉金额
     */
    private String cost;

    /**
     * 投诉状态
     */
    private String status;

    /**
     * 状态版本号
     */
    private Integer statusNo;

    /**
     * 发起时间
     */
    private Date createdAt;

    /**
     * 分配时间
     */
    private Date assignedAt;

    /**
     * 完成时间
     */
    private Date completedAt;

    /**
     * 投诉链接
     */
    private String uri;

    /**
     * 是否已公开 0-未公开 1-已公开
     */
    private Integer exposed;

    /**
     * 剩余申诉次数
     */
    private Integer appealChance;

    /**
     * 剩余结案次数
     */
    private Integer coCompleteChance;

    /**
     * 结案状态
     */
    private String coCompleteStatus;

    /**
     * 结案完成时间
     */
    private Date coCompleteAt;

    /**
     * 自动完成时间
     */
    private Date autoCompleteAt;

    /**
     * 主动完成时间
     */
    private Date userCompleteAt;

    /**
     * 服务名称
     */
    private String service;

    /**
     * 用户评价-服务态度
     */
    private Integer attitude;

    /**
     * 用户评价-处理速度
     */
    private Integer process;

    /**
     * 用户评价-满意度
     */
    private Integer satisfaction;

    /**
     * 用户评价-评价内容
     */
    private String evalContent;

    /**
     * 用户评价-评价时间
     */
    private Date evalAt;

    /**
     * 同步状态 0-待同步 1-已同步 2-同步失败
     */
    private Integer syncStatus;

    /**
     * 同步失败原因
     */
    private String syncFailReason;


    /**
     * 用户回复数量
     */
    private Integer userReplyCount;

    /**
     * 回复详情列表JSON字段
     * 存储投诉单的回复记录，包括回复时间、回复内容、回复人、附件等信息
     * 对应数据库字段：reply_info
     */
    @TableField(value = "reply_info", typeHandler = JacksonTypeHandler.class)
    private List<MiaodaReplyDetailVO> replyDetails;

    /**
     * 结案信息列表JSON字段
     * 存储投诉单结案相关信息，包括结案类型、结案时间、结案说明等
     * 对应数据库字段：complete_info
     */
    @TableField(value = "complete_info", typeHandler = JacksonTypeHandler.class)
    private List<MiaodaCompleteInfoVO> coCompleteInfo;

    /**
     * 附件列表JSON字段
     * 存储投诉单相关的所有附件信息，包括图片、视频、音频等
     * 对应数据库字段：attachment_info
     */
    @TableField(value = "attachment_info", typeHandler = JacksonTypeHandler.class)
    private List<MiaodaAttachmentVO> attaches;

    /**
     * 自动回复状态：NULL-未处理，1-成功，2-失败
     */
    private Integer autoReplyStatus;

    /**
     * 自动回复时间
     */
    private Date autoReplyTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;
}
