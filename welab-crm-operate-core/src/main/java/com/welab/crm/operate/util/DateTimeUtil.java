package com.welab.crm.operate.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * 日期时间转换工具类
 * 用于处理喵达API查询中的时间格式转换
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Slf4j
public class DateTimeUtil {

    /**
     * 标准日期时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String STANDARD_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(STANDARD_DATETIME_FORMAT);

    /**
     * 线程安全的日期格式化器
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = ThreadLocal.withInitial(
            () -> new SimpleDateFormat(STANDARD_DATETIME_FORMAT)
    );

    /**
     * 将字符串格式的时间转换为时间戳（秒级）
     * 
     * @param dateTimeStr 时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 时间戳（秒），如果转换失败返回null
     */
    public static Long convertToTimestamp(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }

        try {
            // 使用 LocalDateTime 进行解析（推荐方式）
            LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr.trim(), DATETIME_FORMATTER);
            return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
        } catch (DateTimeParseException e) {
            log.warn("时间格式解析失败，使用备用方法: {}", dateTimeStr, e);
            
            // 备用方法：使用 SimpleDateFormat
            try {
                Date date = DATE_FORMAT.get().parse(dateTimeStr.trim());
                return date.getTime() / 1000; // 转换为秒级时间戳
            } catch (ParseException pe) {
                log.error("时间格式转换失败: {}", dateTimeStr, pe);
                return null;
            }
        }
    }

    /**
     * 将时间戳（秒级）转换为字符串格式
     * 
     * @param timestamp 时间戳（秒）
     * @return 格式化的时间字符串，如果转换失败返回null
     */
    public static String convertFromTimestamp(Long timestamp) {
        if (timestamp == null || timestamp <= 0) {
            return null;
        }

        try {
            LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(timestamp, 0, ZoneId.systemDefault().getRules().getOffset(LocalDateTime.now()));
            return localDateTime.format(DATETIME_FORMATTER);
        } catch (Exception e) {
            log.error("时间戳转换失败: {}", timestamp, e);
            return null;
        }
    }

    /**
     * 验证时间字符串格式是否正确
     * 
     * @param dateTimeStr 时间字符串
     * @return 是否为有效格式
     */
    public static boolean isValidDateTimeFormat(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return false;
        }

        try {
            LocalDateTime.parse(dateTimeStr.trim(), DATETIME_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            // 尝试备用方法
            try {
                DATE_FORMAT.get().parse(dateTimeStr.trim());
                return true;
            } catch (ParseException pe) {
                return false;
            }
        }
    }

    /**
     * 获取当前时间的字符串格式
     * 
     * @return 当前时间字符串
     */
    public static String getCurrentDateTimeString() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }

    /**
     * 获取当前时间的时间戳（秒级）
     * 
     * @return 当前时间戳
     */
    public static Long getCurrentTimestamp() {
        return LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 为查询条件添加默认时间范围
     * 如果起始时间为空，设置为当天00:00:00
     * 如果结束时间为空，设置为当天23:59:59
     * 
     * @param startTime 起始时间
     * @param endTime 结束时间
     * @return 包含起始和结束时间戳的数组 [startTimestamp, endTimestamp]
     */
    public static Long[] getDefaultTimeRange(String startTime, String endTime) {
        Long startTimestamp = null;
        Long endTimestamp = null;

        if (StringUtils.isNotBlank(startTime)) {
            startTimestamp = convertToTimestamp(startTime);
        }

        if (StringUtils.isNotBlank(endTime)) {
            endTimestamp = convertToTimestamp(endTime);
        }

        return new Long[]{startTimestamp, endTimestamp};
    }

    /**
     * 格式化时间范围用于日志输出
     * 
     * @param startTime 起始时间字符串
     * @param endTime 结束时间字符串
     * @return 格式化的时间范围字符串
     */
    public static String formatTimeRangeForLog(String startTime, String endTime) {
        StringBuilder sb = new StringBuilder();
        
        if (StringUtils.isNotBlank(startTime)) {
            sb.append("起始时间: ").append(startTime);
        }
        
        if (StringUtils.isNotBlank(endTime)) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append("结束时间: ").append(endTime);
        }
        
        if (sb.length() == 0) {
            return "无时间限制";
        }
        
        return sb.toString();
    }

    /**
     * 验证时间范围的合理性
     * 
     * @param startTime 起始时间字符串
     * @param endTime 结束时间字符串
     * @return 验证结果信息，null表示验证通过
     */
    public static String validateTimeRange(String startTime, String endTime) {
        // 验证格式
        if (StringUtils.isNotBlank(startTime) && !isValidDateTimeFormat(startTime)) {
            return "起始时间格式不正确，应为：yyyy-MM-dd HH:mm:ss";
        }
        
        if (StringUtils.isNotBlank(endTime) && !isValidDateTimeFormat(endTime)) {
            return "结束时间格式不正确，应为：yyyy-MM-dd HH:mm:ss";
        }
        
        // 验证时间范围
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            Long startTimestamp = convertToTimestamp(startTime);
            Long endTimestamp = convertToTimestamp(endTime);
            
            if (startTimestamp != null && endTimestamp != null && startTimestamp > endTimestamp) {
                return "起始时间不能晚于结束时间";
            }
        }
        
        return null; // 验证通过
    }

    /**
     * 清理ThreadLocal资源
     */
    public static void clearThreadLocal() {
        DATE_FORMAT.remove();
    }
}
