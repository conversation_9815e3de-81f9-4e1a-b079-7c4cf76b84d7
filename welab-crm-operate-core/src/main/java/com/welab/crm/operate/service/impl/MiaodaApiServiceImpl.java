package com.welab.crm.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.enums.MiaodaComplaintStatusEnum;
import com.welab.crm.operate.util.*;
import com.welab.common.utils.DateUtil;
import com.welab.crm.base.workflow.common.dto.config.WFConfigTransitionDTO;
import com.welab.crm.operate.constant.Constant;
import com.welab.crm.operate.constant.MiaodaApiConstant;
import com.welab.crm.operate.domain.ComplaintTypeMapping;
import com.welab.crm.operate.domain.MiaodaComplaint;
import com.welab.crm.operate.domain.OpDictInfoConf;
import com.welab.crm.operate.dto.miaoda.*;
import com.welab.crm.operate.service.MiaodaAutoReplyConfigService;
import org.springframework.beans.BeanUtils;
import com.welab.crm.operate.dto.workorder.WorkOrderSearchReqDTO;
import com.welab.crm.operate.dto.workorder.WorkOrderSubmitReqDTO;
import com.welab.crm.operate.enums.MiaodaSyncStatusEnum;
import com.welab.crm.operate.mapper.ComplaintTypeMappingMapper;
import com.welab.crm.operate.mapper.MiaodaComplaintMapper;
import com.welab.crm.operate.mapper.OpDictInfoConfMapper;
import com.welab.crm.operate.service.ComplaintTypeMappingService;
import com.welab.crm.operate.service.CustomerService;
import com.welab.crm.operate.service.MiaodaApiService;
import com.welab.crm.operate.service.WorkOrderService;
import com.welab.crm.operate.vo.customer.CashCustInfoVO;
import com.welab.crm.operate.vo.miaoda.MiaodaAttachmentVO;
import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;
import com.welab.crm.operate.vo.miaoda.MiaodaCompleteInfoVO;
import com.welab.crm.operate.vo.miaoda.MiaodaReplyDetailVO;
import com.welab.crm.operate.vo.workorder.ExecutionVO;
import com.welab.crm.operate.vo.workorder.WorkOrderInfoVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import redis.clients.jedis.JedisCommands;

/**
 * 喵达API服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
public class MiaodaApiServiceImpl implements MiaodaApiService {

    @Resource
    private MiaodaApiClient miaodaApiClient;

    @Resource
    private MiaodaTokenManager miaodaTokenManager;

    @Resource
    private MiaodaComplaintMapper miaodaComplaintMapper;

    @Resource
    private MiaodaAutoReplyConfigService autoReplyConfigService;

    @Resource
    private ComplaintTypeMappingService complaintTypeMappingService;

    @Resource
    private WorkOrderService workOrderService;
    @Autowired
    private OpDictInfoConfMapper opDictInfoConfMapper;
    @Autowired
    private CustomerService customerService;
    @Resource
    private JedisCommands jedisCommands;

    @Override
    public String getToken() {
        return miaodaTokenManager.getValidToken();
    }

    @Override
    public List<MiaodaComplaintVO> queryComplaintDetails(MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("查询喵达投诉单详情，参数：{}", JSON.toJSONString(queryDTO));

            // 验证和转换时间参数
            MiaodaComplaintApiQueryDTO convertedQueryDTO = convertTimeFieldsForApi(queryDTO);
            log.info("时间转换后的参数：{}", JSON.toJSONString(convertedQueryDTO));

            String token = getToken();
            // 使用JSON请求体代替查询参数
            String response = miaodaApiClient.doGet(MiaodaApiConstant.QUERY_PATH,
                    convertedQueryDTO, token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("查询投诉单详情失败：{}", miaodaApiClient.extractErrorMessage(response));
                return new ArrayList<>();
            }

            JSONObject data = miaodaApiClient.extractData(response);
            if (data == null) {
                return new ArrayList<>();
            }

            JSONArray complaints = data.getJSONArray("complaints");
            if (complaints == null || complaints.isEmpty()) {
                return new ArrayList<>();
            }

            List<MiaodaComplaintVO> result = new ArrayList<>();
            for (int i = 0; i < complaints.size(); i++) {
                JSONObject complaint = complaints.getJSONObject(i);
                MiaodaComplaintVO vo = parseComplaintFromJson(complaint);
                if (vo != null) {
                    result.add(vo);
                }
            }

            log.info("查询到{}条投诉单", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询投诉单详情异常", e);
            throw new RuntimeException("查询投诉单详情失败：" + e.getMessage());
        }
    }

    @Override
    public Page<MiaodaComplaintVO> queryComplaintDetailsByPage(MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("分页查询喵达投诉单详情，参数：{}", JSON.toJSONString(queryDTO));

            // 验证和转换时间参数
            MiaodaComplaintApiQueryDTO convertedQueryDTO = convertTimeFieldsForApi(queryDTO);
            log.info("时间转换后的参数：{}", JSON.toJSONString(convertedQueryDTO));

            String token = getToken();

            // 确保分页参数有效
            if (convertedQueryDTO.getPage() == null || convertedQueryDTO.getPage() < 1) {
                convertedQueryDTO.setPage(1);
            }
            if (convertedQueryDTO.getPageSize() == null || convertedQueryDTO.getPageSize() < 1) {
                convertedQueryDTO.setPageSize(10);
            }
            // 限制最大页大小
            if (convertedQueryDTO.getPageSize() > MiaodaApiConstant.MAX_PAGE_SIZE) {
                convertedQueryDTO.setPageSize(MiaodaApiConstant.MAX_PAGE_SIZE);
            }

            // 使用JSON请求体发送分页查询请求
            String response = miaodaApiClient.doGet(MiaodaApiConstant.QUERY_PATH, convertedQueryDTO, token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("分页查询投诉单详情失败：{}", miaodaApiClient.extractErrorMessage(response));
                return new Page<>(convertedQueryDTO.getPage(), convertedQueryDTO.getPageSize());
            }

            JSONObject data = miaodaApiClient.extractData(response);
            if (data == null) {
                return new Page<>(convertedQueryDTO.getPage(), convertedQueryDTO.getPageSize());
            }

            // 解析投诉单列表
            JSONArray complaints = data.getJSONArray("complaints");
            List<MiaodaComplaintVO> records = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(complaints)) {
                for (int i = 0; i < complaints.size(); i++) {
                    JSONObject complaint = complaints.getJSONObject(i);
                    MiaodaComplaintVO vo = parseComplaintFromJson(complaint);
                    if (vo != null) {
                        records.add(vo);
                    }
                }
            }

            // 批量填充工单号
            fillWorkOrderNumbers(records);

            // 解析分页信息
            JSONObject pager = data.getJSONObject("pager");
            Page<MiaodaComplaintVO> pageResult = new Page<>(convertedQueryDTO.getPage(), convertedQueryDTO.getPageSize());

            if (pager != null) {
                // 设置总记录数
                Long itemCount = pager.getLong("item_count");
                if (itemCount != null) {
                    pageResult.setTotal(itemCount);
                }

                // 设置当前页码
                Integer current = pager.getInteger("current");
                if (current != null) {
                    pageResult.setCurrent(current);
                }

                // 设置每页大小
                Integer pageSize = pager.getInteger("page_size");
                if (pageSize != null) {
                    pageResult.setSize(pageSize);
                }

                // 计算总页数
                Integer pageAmount = pager.getInteger("page_amount");
                if (pageAmount != null) {
                    pageResult.setPages(pageAmount);
                }

                log.info("分页信息 - 当前页: {}, 每页大小: {}, 总记录数: {}, 总页数: {}",
                        current, pageSize, itemCount, pageAmount);
            } else {
                // 如果没有分页信息，设置默认值
                pageResult.setTotal(records.size());
                pageResult.setPages(1);
                log.warn("API响应中未包含分页信息，使用默认分页设置");
            }

            // 设置记录列表
            pageResult.setRecords(records);

            log.info("分页查询到{}条投诉单，总记录数: {}", records.size(), pageResult.getTotal());
            return pageResult;

        } catch (Exception e) {
            log.error("分页查询投诉单详情异常", e);
            return new Page<>(queryDTO.getPage() != null ? queryDTO.getPage() : 1,
                    queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10);
        }
    }

    @Override
    public List<MiaodaComplaintVO> updateComplaintStatus(List<String> sns, List<Integer> statusNos) {
        try {
            log.info("更新投诉单状态，投诉单号：{}", sns);

            if (CollectionUtils.isEmpty(sns) || CollectionUtils.isEmpty(statusNos)) {
                throw new IllegalArgumentException("投诉单号和状态版本号不能为空");
            }

            if (sns.size() != statusNos.size()) {
                throw new IllegalArgumentException("投诉单号和状态版本号数量不匹配");
            }

            // 构建更新请求
            JSONArray updateArray = new JSONArray();
            for (int i = 0; i < sns.size(); i++) {
                JSONObject updateItem = new JSONObject();
                updateItem.put("sn", sns.get(i));
                updateItem.put("status_no", statusNos.get(i));
                updateArray.add(updateItem);
            }

            String token = getToken();
            String response = miaodaApiClient.doGet(MiaodaApiConstant.UPDATE_PATH,
                    null, token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("更新投诉单状态失败：{}", miaodaApiClient.extractErrorMessage(response));
                return new ArrayList<>();
            }


            JSONArray updates = miaodaApiClient.extractDataArray(response);

            List<MiaodaComplaintVO> result = new ArrayList<>();
            for (int i = 0; i < updates.size(); i++) {
                JSONObject update = updates.getJSONObject(i);
                MiaodaComplaintVO vo = parseComplaintFromJson(update);
                if (vo != null) {
                    result.add(vo);
                }
            }

            return result;

        } catch (Exception e) {
            log.error("更新投诉单状态异常", e);
            throw new RuntimeException("更新投诉单状态失败：" + e.getMessage());
        }
    }

    @Override
    public List<MiaodaComplaintVO> updateComplaintStatusAndSave(List<String> sns, List<Integer> statusNos, AtomicInteger updatedCount) {

        List<MiaodaComplaintVO> updatedComplaints = updateComplaintStatus(sns, statusNos);

        for (MiaodaComplaintVO complaintVO : updatedComplaints) {
            try {
                MiaodaComplaint existing = miaodaComplaintMapper.selectBySn(complaintVO.getSn());
                if (existing != null) {
                    Integer userSupplement = detectUserSupplement(complaintVO);
                    if (userSupplement > 0) {
                        // 有新的用户补充，处理催单逻辑
                        handleReminderLogic(complaintVO, userSupplement);
                    }
                    updateComplaintFromVO(existing, complaintVO);
                    miaodaComplaintMapper.updateById(existing);
                    updatedCount.getAndIncrement();
                }
            } catch (Exception e) {
                log.error("更新投诉单状态失败：{}", complaintVO.getSn(), e);
            }
        }

        return updatedComplaints;
    }

    @Override
    public boolean replyComplaint(MiaodaReplyDTO replyDTO) {
        try {
            log.info("回复投诉单，参数：{}", JSON.toJSONString(replyDTO));

            String token = getToken();
            String response = miaodaApiClient.doPost(MiaodaApiConstant.REPLY_PATH,
                    JSON.toJSONString(replyDTO), token);

            boolean success = miaodaApiClient.isResponseSuccess(response);
            if (!success) {
                log.error("回复投诉单失败：{}", miaodaApiClient.extractErrorMessage(response));
            } else {
                log.info("回复投诉单成功");
            }

            return success;

        } catch (Exception e) {
            log.error("回复投诉单异常", e);
            throw new RuntimeException("回复投诉单失败：" + e.getMessage());
        }
    }

    @Override
    public boolean appealComplaint(MiaodaAppealDTO appealDTO) {
        try {
            log.info("申诉投诉单，参数：{}", JSON.toJSONString(appealDTO));

            String token = getToken();
            String response = miaodaApiClient.doPost(MiaodaApiConstant.APPEAL_PATH,
                    JSON.toJSONString(appealDTO), token);

            boolean success = miaodaApiClient.isResponseSuccess(response);
            if (!success) {
                log.error("申诉投诉单失败：{}", miaodaApiClient.extractErrorMessage(response));
            } else {
                log.info("申诉投诉单成功");
            }

            return success;

        } catch (Exception e) {
            log.error("申诉投诉单异常", e);
            throw new RuntimeException("申诉投诉单失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> queryAppealStatus(List<String> sns) {
        try {
            log.info("查询申诉状态，投诉单号：{}", sns);

            if (CollectionUtils.isEmpty(sns)) {
                return new ArrayList<>();
            }

            JSONObject requestBody = new JSONObject();
            requestBody.put("sns", String.join(",", sns));

            String token = getToken();
            // 使用JSON请求体发送申诉状态查询请求
            String response = miaodaApiClient.doGet(MiaodaApiConstant.APPEAL_STATUS_PATH,
                    requestBody, token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("查询申诉状态失败：{}", miaodaApiClient.extractErrorMessage(response));
                return new ArrayList<>();
            }

            JSONObject data = miaodaApiClient.extractData(response);
            if (data == null) {
                return new ArrayList<>();
            }

            JSONArray appealStatus = data.getJSONArray("appeal_status");
            if (appealStatus == null || appealStatus.isEmpty()) {
                return new ArrayList<>();
            }

            List<String> result = new ArrayList<>();
            for (int i = 0; i < appealStatus.size(); i++) {
                JSONObject status = appealStatus.getJSONObject(i);
                String statusDesc = status.getString("status");
                result.add(statusDesc);
            }

            return result;

        } catch (Exception e) {
            log.error("查询申诉状态异常", e);
            throw new RuntimeException("查询申诉状态失败：" + e.getMessage());
        }
    }

    @Override
    public boolean completeComplaint(MiaodaCompleteDTO completeDTO) {
        try {
            log.info("申请结案，参数：{}", JSON.toJSONString(completeDTO));

            String token = getToken();
            String response = miaodaApiClient.doPost(MiaodaApiConstant.COMPLETE_PATH,
                    JSON.toJSONString(completeDTO), token);

            boolean success = miaodaApiClient.isResponseSuccess(response);
            if (!success) {
                log.error("申请结案失败：{}", miaodaApiClient.extractErrorMessage(response));
            } else {
                log.info("申请结案成功");
            }

            return success;

        } catch (Exception e) {
            log.error("申请结案异常", e);
            throw new RuntimeException("申请结案失败：" + e.getMessage());
        }
    }

    @Override
    public String queryRapidSolveInfo(String month) {
        try {
            log.info("查询快速解决商家信息，月份：{}", month);

            JSONObject requestBody = new JSONObject();
            if (StringUtils.isNotBlank(month)) {
                requestBody.put("month", month);
            }

            String token = getToken();
            // 使用JSON请求体发送快速解决信息查询请求
            String response = miaodaApiClient.doGet(MiaodaApiConstant.RAPID_SOLVE_PATH,
                    requestBody.isEmpty() ? null : requestBody, token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("查询快速解决商家信息失败：{}", miaodaApiClient.extractErrorMessage(response));
                return null;
            }

            return response;

        } catch (Exception e) {
            log.error("查询快速解决商家信息异常", e);
            throw new RuntimeException("查询快速解决商家信息失败：" + e.getMessage());
        }
    }

    @Override
    public String queryPlanInfo() {
        try {
            log.info("查询套餐余量信息");

            String token = getToken();
            // 使用空的JSON对象作为请求体，保持一致性
            String response = miaodaApiClient.doGet(MiaodaApiConstant.PLAN_INFO_PATH,
                    new JSONObject(), token);

            if (!miaodaApiClient.isResponseSuccess(response)) {
                log.error("查询套餐余量信息失败：{}", miaodaApiClient.extractErrorMessage(response));
                return null;
            }

            return response;

        } catch (Exception e) {
            log.error("查询套餐余量信息异常", e);
            throw new RuntimeException("查询套餐余量信息失败：" + e.getMessage());
        }
    }

    @Override
    public boolean claimComplaint(String sn, String content, List<String> images, List<String> videos) {
        try {
            log.info("投诉认领，投诉单号：{}", sn);

            JSONObject requestBody = new JSONObject();
            requestBody.put("sn", sn);
            requestBody.put("content", content);
            if (CollectionUtils.isNotEmpty(images)) {
                requestBody.put("images", images);
            }
            if (CollectionUtils.isNotEmpty(videos)) {
                requestBody.put("videos", videos);
            }

            String token = getToken();
            String response = miaodaApiClient.doPost(MiaodaApiConstant.CLAIM_PATH,
                    JSON.toJSONString(requestBody), token);

            boolean success = miaodaApiClient.isResponseSuccess(response);
            if (!success) {
                log.error("投诉认领失败：{}", miaodaApiClient.extractErrorMessage(response));
            } else {
                log.info("投诉认领成功");
            }

            return success;

        } catch (Exception e) {
            log.error("投诉认领异常", e);
            throw new RuntimeException("投诉认领失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncComplaintsToLocal(String startTime, String endTime) {

        String lockKey = MiaodaApiConstant.REDIS_SYNC_LOCK_KEY + ":manualSync";
        try {
            log.info("同步投诉单到本地数据库，时间范围：{} - {}", startTime, endTime);

            // 获取分布式锁，防止重复执行
            boolean lockAcquired = RedisUtil.tryGetDistributedLock(jedisCommands, lockKey, "1",
                    MiaodaApiConstant.LOCK_EXPIRE_MINUTES * 60); // 转换为秒
            if (!lockAcquired) {
                log.info("获取同步锁失败，可能有其他实例正在执行同步任务");
                throw new FastRuntimeException("正在执行同步任务，请稍后再试");
            }

            // 构建查询条件
            MiaodaComplaintQueryDTO queryDTO = new MiaodaComplaintQueryDTO();
            queryDTO.setSt(startTime);
            queryDTO.setEt(endTime);
//            queryDTO.setStatus(MiaodaApiConstant.STATUS_PROCESSING); // 只同步处理中的投诉单
            queryDTO.setPageSize(MiaodaApiConstant.MAX_PAGE_SIZE);

            int totalSynced = 0;
            int currentPage = 1;
            boolean hasMore = true;

            while (hasMore) {
                queryDTO.setPage(currentPage);
                List<MiaodaComplaintVO> complaints = queryComplaintDetails(queryDTO);

                if (CollectionUtils.isEmpty(complaints)) {
                    hasMore = false;
                    continue;
                }

                for (MiaodaComplaintVO complaintVO : complaints) {
                    try {
                        // 检查是否已存在
                        MiaodaComplaint existing = miaodaComplaintMapper.selectBySn(complaintVO.getSn());
                        if (existing == null) {
                            // 新增
                            MiaodaComplaint complaint = convertVOToEntity(complaintVO);
                            complaint.setSyncStatus(MiaodaSyncStatusEnum.SUCCESS.getCode());
                            miaodaComplaintMapper.insert(complaint);
                            totalSynced++;
                            log.debug("新增投诉单：{}", complaintVO.getSn());
                            // 新投诉单自动创建工单
                            processNewComplaints(complaintVO);
                        } else {
                            Integer userSupplement = detectUserSupplement(complaintVO);
                            if (userSupplement > 0) {
                                // 有新的用户补充，处理催单逻辑
                                handleReminderLogic(complaintVO, userSupplement);
                            }
                            // 更新
                            updateComplaintFromVO(existing, complaintVO);
                            existing.setSyncStatus(MiaodaSyncStatusEnum.SUCCESS.getCode());
                            miaodaComplaintMapper.updateById(existing);
                            log.debug("更新投诉单：{}", complaintVO.getSn());
                        }
                        // 检查是否需要自动回复
                        processAutoReplyForNewComplaint(complaintVO);
                    } catch (Exception e) {
                        log.error("同步投诉单失败：{}", complaintVO.getSn(), e);
                        // 记录同步失败
                        MiaodaComplaint failedComplaint = new MiaodaComplaint();
                        failedComplaint.setSn(complaintVO.getSn());
                        failedComplaint.setSyncStatus(MiaodaSyncStatusEnum.FAILED.getCode());
                        failedComplaint.setSyncFailReason(e.getMessage());
                        miaodaComplaintMapper.insert(failedComplaint);
                    }
                }

                // 检查是否还有更多数据
                if (complaints.size() < MiaodaApiConstant.MAX_PAGE_SIZE) {
                    hasMore = false;
                } else {
                    currentPage++;
                }
            }

            log.info("同步投诉单完成，共同步{}条", totalSynced);
            return totalSynced;

        } catch (Exception e) {
            log.error("同步投诉单到本地数据库异常", e);
            throw new RuntimeException("同步投诉单失败：" + e.getMessage());
        } finally {
            // 释放锁
            try {
                RedisUtil.releaseDistributedLock(jedisCommands, lockKey);
            } catch (Exception e) {
                log.error("释放同步锁失败", e);
            }
        }
    }


    /**
     * 处理新投诉单，自动创建工单
     */
    private void processNewComplaints(MiaodaComplaintVO complaint) {
        try {
            log.info("开始处理新投诉单，自动创建工单");
            try {
                // 检查是否已经创建过工单
                if (hasExistingWorkOrder(complaint)) {
                    log.debug("投诉单{}已存在关联工单，跳过", complaint.getSn());
                    return;
                }

                // 重复投诉检查
                if (duplicateCheck(complaint)) {
                    log.info("投诉单{}为重复投诉，跳过", complaint.getSn());
                    return;
                }

                // 自动创建工单
                String workOrderNo = autoCreateWorkOrder(complaint);
                if (workOrderNo != null) {
                    log.info("为投诉单{}自动创建工单：{}", complaint.getSn(), workOrderNo);
                }



            } catch (Exception e) {
                log.error("处理投诉单{}异常", complaint.getSn(), e);
            }

            log.info("处理新投诉单完成，sn:{}, workOrderNo:{}", complaint.getSn(), complaint.getWorkOrderNo());

        } catch (Exception e) {
            log.error("处理新投诉单异常", e);
        }
    }

    /**
     * 重复工单处理
     * 只有在存在相同手机号、相同工单组合、并且工单还没结案的时候作为重复工单，不新建工单
     *
     * @param complaint
     */
    private boolean duplicateCheck(MiaodaComplaintVO complaint) {


        // 查询当前投诉单映射的工单组合
        OpDictInfoConf thisConf = queryOrderConfByComplaint(complaint.getIssue());
        if (thisConf == null) {
            throw new FastRuntimeException("没有合适的工单映射");
        }

        // 查询是否有相同的工单三类，相同手机号的未结案工单，有则更新关联关系，不新建工单
        WorkOrderSearchReqDTO searchReqDTO = new WorkOrderSearchReqDTO();
        List<String> mobileList = new ArrayList<>();
        if (StringUtils.isNotBlank(complaint.getPhone())) {
            mobileList.add(complaint.getPhone());
        }

        if (StringUtils.isNotBlank(complaint.getCompPhone())) {
            mobileList.add(complaint.getCompPhone());
        }
        searchReqDTO.setMobileList(mobileList);
        searchReqDTO.setOrderType(String.valueOf(thisConf.getWoTypeId()));
        searchReqDTO.setOrderOneClass(thisConf.getWoTypeFirId());
        searchReqDTO.setOrderTwoClass(thisConf.getWoTypeSecId());
        searchReqDTO.setOrderThreeClass(thisConf.getWoTypeThirId());
        ExecutionVO executionVO = workOrderService.queryExecutionByCondition(searchReqDTO);
        if (Objects.isNull(executionVO) || CommonUtil.checkIsEnd(executionVO.getOrderStatus())) {
            log.info("无重复投诉工单, sn:{}", complaint.getSn());
            return false;
        }
        String orderNo = executionVO.getOrderNo();
        // 更新喵达-工单关联信息
        updateMiaoDaComplaintOrderNo(complaint.getSn(), orderNo);

        // 查询工单信息，如果没有关联喵达订单，则进行更新
        WorkOrderInfoVO vo = workOrderService.queryOrderInfoByOrderNo(orderNo);
        if (StringUtils.isBlank(vo.getMiaodaSn())) {
            workOrderService.updateMiaodaInfo(complaint, orderNo);
        }

        // 重复投诉，进行催单
        reminderOrder(orderNo, buildDuplicateReminderContent(complaint));

        log.info("喵达重复投诉工单，sn:{}, orderNo:{}", complaint.getSn(), orderNo);

        return true;
    }

    private String buildDuplicateReminderContent(MiaodaComplaintVO complaint) {
        return "【喵达用户重复投诉催单】\n" +
                "投诉链接：" + complaint.getUri() + "\n" +
                "投诉时间：" + DateUtil.dateToString(complaint.getCreatedAt()) + "\n";
    }

    private void updateMiaoDaComplaintOrderNo(String sn, String workOrderNo) {
        miaodaComplaintMapper.updateWorkOrderNoBySn(sn, workOrderNo);
    }


    private OpDictInfoConf queryOrderConfByComplaint(String issue) {
        // 根据投诉问题类型查找映射
        ComplaintTypeMapping mapping = complaintTypeMappingService.getBestMatchMapping(Arrays.asList(issue.split(",")));
        if (mapping == null) {
            log.warn("未找到投诉类型映射，投诉问题：{}", issue);
            return null;
        }

        // 查询工单组合配置
        OpDictInfoConf orderConf = opDictInfoConfMapper.selectById(mapping.getOpDictInfoConfId());
        if (orderConf == null) {
            log.warn("未找到工单组合配置，配置ID：{}", mapping.getOpDictInfoConfId());
            return null;
        }

        return orderConf;
    }


    /**
     * 检查投诉单是否已存在关联工单
     */
    private boolean hasExistingWorkOrder(MiaodaComplaintVO complaint) {
        try {
            // 这里可以查询本地数据库检查是否已存在工单
            // 或者通过其他方式判断
            return workOrderService.queryOrderInfoByMiaoDaSn(complaint.getSn()) != null;
        } catch (Exception e) {
            log.error("检查投诉单是否存在关联工单异常", e);
            return false;
        }
    }

    @Override
    public int checkAndUpdateComplaintStatus() {
        try {
            log.info("检查并更新投诉单状态");

            // 查询需要更新状态的投诉单
            List<MiaodaComplaint> needUpdateComplaints = miaodaComplaintMapper.selectNeedSyncComplaints(null);
            if (CollectionUtils.isEmpty(needUpdateComplaints)) {
                log.info("没有需要更新状态的投诉单");
                return 0;
            }

            AtomicInteger updatedCount = new AtomicInteger();

            // 喵达一次最多更新 30 笔，分批处理
            ListUtils.splitList(needUpdateComplaints, MiaodaApiConstant.BATCH_MAX_SIZE).forEach(subList -> {
                List<String> sns = subList.stream()
                        .map(MiaodaComplaint::getSn)
                        .collect(Collectors.toList());
                List<Integer> statusNos = subList.stream()
                        .map(MiaodaComplaint::getStatusNo)
                        .collect(Collectors.toList());
                updateComplaintStatusAndSave(sns, statusNos, updatedCount);
            });


            log.info("更新投诉单状态完成，共更新{}条", updatedCount);
            return updatedCount.get();

        } catch (Exception e) {
            log.error("检查并更新投诉单状态异常", e);
            throw new RuntimeException("更新投诉单状态失败：" + e.getMessage());
        }
    }

    @Override
    public String autoCreateWorkOrder(MiaodaComplaintVO complaintVO) {
        try {
            log.info("自动创建工单，投诉单号：{}", complaintVO.getSn());

            // 根据投诉问题类型查找映射
            OpDictInfoConf opDictInfoConf = queryOrderConfByComplaint(complaintVO.getIssue());
            if (opDictInfoConf == null) {
                log.warn("未找到投诉类型映射，投诉问题：{}", complaintVO.getIssue());
                throw new FastRuntimeException("未找到投诉类型映射");
            }

            // 构建工单提交请求
            WorkOrderSubmitReqDTO workOrderDTO = new WorkOrderSubmitReqDTO();

            // 这里需要添加查询op_dict_info_conf表的逻辑来获取具体的工单类型ID
            workOrderDTO.setType(String.valueOf(opDictInfoConf.getWoTypeId()));
            workOrderDTO.setOrderOneClass(opDictInfoConf.getWoTypeFirId());
            workOrderDTO.setOrderTwoClass(opDictInfoConf.getWoTypeSecId());
            workOrderDTO.setOrderThreeClass(opDictInfoConf.getWoTypeThirId());
            workOrderDTO.setOrderCase(opDictInfoConf.getWoTypeChildId());

            // 设置客户信息
            setCustomerInfoFromComplaint(workOrderDTO, complaintVO);

            // 设置工单内容
            String description = buildWorkOrderDescription(complaintVO);
            workOrderDTO.setDescription(description);

            // 设置其他信息
            setOtherInfoFromComplaint(workOrderDTO, complaintVO);


            // 提交工单
            String orderNo = workOrderService.submitWorkOrder(workOrderDTO, workOrderService.getDefaultSubmitStaff());

            // 更新投诉单关联的工单编号
            MiaodaComplaint complaint = miaodaComplaintMapper.selectBySn(complaintVO.getSn());
            if (complaint != null) {
                complaint.setWorkOrderNo(orderNo);
                miaodaComplaintMapper.updateById(complaint);

                // 更新喵达信息到工单表
                workOrderService.updateMiaodaInfo(complaintVO, orderNo);
            }

            log.info("自动创建工单成功，工单编号：{}", workOrderDTO.getOrderNo());
            return workOrderDTO.getOrderNo();

        } catch (Exception e) {
            log.error("自动创建工单异常", e);
            throw new RuntimeException("自动创建工单失败：" + e.getMessage());
        }
    }

    private void setOtherInfoFromComplaint(WorkOrderSubmitReqDTO workOrderDTO, MiaodaComplaintVO complaintVO) {

        workOrderDTO.setUrgentFlag("0"); // 默认不加急
        workOrderDTO.setCallbackFlag("0"); // 默认不回访

        workOrderDTO.setExecutionDTO(workOrderService.getExecutionDTO(null));
        workOrderDTO.setAssignedDTO(workOrderService.getAssignedDTO());
        workOrderDTO.setTransitionDTO(workOrderService.getTransitionDTO());
        workOrderDTO.setProcessCode(workOrderService.getProcessCode());

    }

    @Override
    public boolean handleReminderLogic(MiaodaComplaintVO complaintVO, Integer userSupplement) {
        try {
            log.info("处理催单逻辑，投诉单号：{}", complaintVO.getSn());


            // 查找关联的工单
            MiaodaComplaint complaint = miaodaComplaintMapper.selectBySn(complaintVO.getSn());
            if (complaint == null || StringUtils.isBlank(complaint.getWorkOrderNo())) {
                log.warn("投诉单未关联工单，投诉单号：{}", complaintVO.getSn());
                return false;
            }

            List<MiaodaReplyDetailVO> replyDetails = complaintVO.getReplyDetails();
            int startIndex = Math.max(0, replyDetails.size() - userSupplement);

            replyDetails.subList(startIndex, replyDetails.size()).forEach(replyDetail -> {
                String reminderContent = buildReminderContent(replyDetail);
                reminderOrder(complaint.getWorkOrderNo(), reminderContent);
            });


            log.info("催单处理完成，投诉单号：{}", complaintVO.getSn());
            return true;

        } catch (Exception e) {
            log.error("处理催单逻辑异常", e);
            return false;
        }
    }

    private void reminderOrder(String workOrderNo, String reminderContent) {
        workOrderService.reminderOrder(workOrderNo, reminderContent, null);
    }

    @Override
    public Integer detectUserSupplement(MiaodaComplaintVO complaintVO) {
        try {
            if (CollectionUtils.isEmpty(complaintVO.getReplyDetails())) {
                return 0;
            }

            // 检查是否有新的用户补充（sender=1）
            long userSupplementCount = complaintVO.getReplyDetails().stream()
                    .filter(reply -> reply.getSender() != null && reply.getSender() == MiaodaApiConstant.SENDER_USER)
                    .count();

            // 查询本地记录的用户补充数量
            MiaodaComplaint localComplaint = miaodaComplaintMapper.selectBySn(complaintVO.getSn());
            if (localComplaint == null || localComplaint.getUserReplyCount() == null) {
                return Math.toIntExact(userSupplementCount);
            }

            return Math.toIntExact(userSupplementCount - localComplaint.getUserReplyCount());

        } catch (Exception e) {
            log.error("检测用户补充异常", e);
            return 0;
        }
    }

    @Override
    public List<MiaodaComplaintVO> queryComplaintsByWorkOrderNo(String workOrderNo) {
        try {
            log.info("根据工单编号查询投诉单，工单编号：{}", workOrderNo);

            List<MiaodaComplaint> complaints = miaodaComplaintMapper.selectByWorkOrderNo(workOrderNo);
            if (CollectionUtils.isEmpty(complaints)) {
                return new ArrayList<>();
            }

            List<String> sns = complaints.stream()
                    .map(MiaodaComplaint::getSn)
                    .collect(Collectors.toList());

            List<Integer> statusNoList = complaints.stream()
                    .map(MiaodaComplaint::getStatusNo)
                    .collect(Collectors.toList());

            AtomicInteger updatedCount = new AtomicInteger();
            // 实时查询最新投诉信息
            List<MiaodaComplaintVO> complaintVOList = updateComplaintStatusAndSave(sns, statusNoList, updatedCount);

            // 查询申诉状态
            List<String> appealStatusList = queryAppealStatus(sns);
            for (int i = 0; i < complaints.size(); i++) {
                MiaodaComplaintVO complaintVO = complaintVOList.get(i);
                complaintVO.setAppealStatus(appealStatusList.get(i));
                complaintVO.setUri(complaints.get(i).getUri());
            }

            log.info("根据工单编号查询投诉单完成，工单编号：{}，共更新{}条", workOrderNo, updatedCount);


            return complaintVOList;

        } catch (Exception e) {
            log.error("根据工单编号查询投诉单异常", e);
            throw new RuntimeException("根据工单编号查询投诉单失败：" + e.getMessage());
        }
    }

    @Override
    public void handlerCompleteCheck(WorkOrderSubmitReqDTO reqDTO) {
        // 校验是不是已解决结案
        WFConfigTransitionDTO transitionDTO = reqDTO.getTransitionDTO();
        if (transitionDTO == null || !transitionDTO.getTargetState().equals(Constant.RESOLVED_CLOSE)) {
            return;
        }

        String orderNo = reqDTO.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return;
        }

        List<MiaodaComplaintVO> complaints = queryComplaintsByWorkOrderNo(orderNo);
        if (CollectionUtils.isEmpty(complaints)) {
            return;
        }

        complaints.forEach(complaint -> {
            if (complaint.getCoCompleteChance() == MiaodaApiConstant.MAX_COMPLETE_TIME) {
                throw new FastRuntimeException("请先通过更多查询-操作-申请结案");
            }
        });


    }


    /**
     * 从JSON解析投诉单信息
     */
    private MiaodaComplaintVO parseComplaintFromJson(JSONObject json) {
        try {
            MiaodaComplaintVO vo = new MiaodaComplaintVO();

            vo.setSn(json.getString("sn"));
            vo.setTitle(json.getString("title"));
            vo.setNickname(json.getString("nickname"));
            vo.setPhone(json.getString("phone"));
            vo.setCompPhone(json.getString("comp_phone"));
            vo.setPrivacy(json.getString("privacy"));
            vo.setContent(json.getString("content"));
            vo.setIssue(json.getString("issue"));
            vo.setAppeal(json.getString("appeal"));
            vo.setCost(json.getString("cost"));
            vo.setStatus(json.getString("status"));
            vo.setStatusNo(json.getInteger("status_no"));
            vo.setCreatedAt(convertTimestampToDate(json.getLong("created_at")));
            vo.setAssignedAt(convertTimestampToDate(json.getLong("assigned_at")));
            vo.setCompletedAt(convertTimestampToDate(json.getLong("completed_at")));
            vo.setUri(json.getString("uri"));
            vo.setExposed(json.getInteger("exposed"));
            vo.setAppealChance(json.getInteger("appeal_chance"));
            vo.setCoCompleteChance(json.getInteger("co_complete_chance"));
            vo.setCoCompleteStatus(json.getString("co_complete_status"));
            vo.setCoCompleteAt(convertTimestampToDate(json.getLong("co_complete_at")));
            vo.setAutoCompleteAt(convertTimestampToDate(json.getLong("auto_complete_at")));
            vo.setUserCompleteAt(convertTimestampToDate(json.getLong("user_complete_at")));
            vo.setService(json.getString("service"));
            vo.setAttitude(json.getInteger("attitude"));
            vo.setProcess(json.getInteger("process"));
            vo.setSatisfaction(json.getInteger("satisfaction"));
            vo.setEvalContent(json.getString("evalContent"));
            vo.setEvalAt(convertTimestampToDate(json.getLong("eval_at")));

            // 解析回复详情
            parseReplyDetails(json, vo);

            // 解析结案信息
            parseCompleteInfo(json, vo);

            // 解析附件
            parseAttachments(json, vo);

            return vo;

        } catch (Exception e) {
            log.error("解析投诉单JSON失败", e);
            return null;
        }
    }

    private void parseReplyDetails(JSONObject json, MiaodaComplaintVO vo) {
        List<MiaodaReplyDetailVO> replyList = new ArrayList<>();
        JSONArray replyDetails = json.getJSONArray("reply_details");
        if (CollectionUtils.isNotEmpty(replyDetails)) {
            replyDetails.forEach(item -> {
                JSONObject replyDetail = (JSONObject) item;
                MiaodaReplyDetailVO detailVO = new MiaodaReplyDetailVO();
                detailVO.setSender(replyDetail.getInteger("sender"));
                detailVO.setSenderDesc(getSenderDesc(replyDetail.getInteger("sender")));
                detailVO.setContent(replyDetail.getString("content"));
                detailVO.setContentHide(replyDetail.getInteger("content_hide"));
                detailVO.setAttachHide(replyDetail.getInteger("attach_hide"));
                detailVO.setReplyedAt(convertTimestampToDate(replyDetail.getLong("replyed_at")));

                // 解析回复中的附件
                List<MiaodaAttachmentVO> attachments = parseAttachmentArray(replyDetail.getJSONArray("attaches"));
                detailVO.setAttaches(attachments);

                replyList.add(detailVO);
            });
        }

        vo.setReplyDetails(replyList);
    }

    /**
     * 解析结案信息
     */
    private void parseCompleteInfo(JSONObject json, MiaodaComplaintVO vo) {
        List<MiaodaCompleteInfoVO> completeInfoList = new ArrayList<>();
        JSONArray coCompleteInfo = json.getJSONArray("co_complete_info");
        if (CollectionUtils.isNotEmpty(coCompleteInfo)) {
            coCompleteInfo.forEach(item -> {
                JSONObject completeInfo = (JSONObject) item;
                MiaodaCompleteInfoVO infoVO = new MiaodaCompleteInfoVO();
                infoVO.setCoCompleteSolution(completeInfo.getString("co_complete_solution"));
                infoVO.setCoCompleteReason(completeInfo.getString("co_complete_reason"));
                infoVO.setCoCompleteBegin(completeInfo.getLong("co_complete_begin"));

                // 解析结案附件
                List<MiaodaAttachmentVO> attachments = parseAttachmentArray(completeInfo.getJSONArray("co_complete_attaches"));
                infoVO.setCoCompleteAttaches(attachments);

                completeInfoList.add(infoVO);
            });
        }

        vo.setCoCompleteInfo(completeInfoList);
    }

    /**
     * 解析附件信息
     */
    private void parseAttachments(JSONObject json, MiaodaComplaintVO vo) {
        List<MiaodaAttachmentVO> attachments = parseAttachmentArray(json.getJSONArray("attaches"));
        vo.setAttaches(attachments);
    }

    /**
     * 解析附件数组的通用方法
     */
    private List<MiaodaAttachmentVO> parseAttachmentArray(JSONArray attachArray) {
        List<MiaodaAttachmentVO> attachments = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachArray)) {
            attachArray.forEach(item -> {
                JSONObject attachment = (JSONObject) item;
                MiaodaAttachmentVO attachmentVO = new MiaodaAttachmentVO();
                attachmentVO.setType(attachment.getString("type"));
                attachmentVO.setSrc(attachment.getString("src"));
                attachmentVO.setName(attachment.getString("name"));
                attachmentVO.setSize(attachment.getLong("size"));
                attachments.add(attachmentVO);
            });
        }
        return attachments;
    }

    /**
     * 获取发送者描述
     */
    private String getSenderDesc(Integer sender) {
        if (sender == null) {
            return "未知";
        }
        switch (sender) {
            case MiaodaApiConstant.SENDER_USER:
                return "用户补充";
            case MiaodaApiConstant.SENDER_MERCHANT:
                return "商家回复";
            default:
                return "未知";
        }
    }

    /**
     * 将VO转换为实体
     */
    private MiaodaComplaint convertVOToEntity(MiaodaComplaintVO vo) {
        MiaodaComplaint entity = new MiaodaComplaint();
        BeanUtils.copyProperties(vo, entity);

        // 设置JSON字段：直接将VO中的列表字段赋值给实体的JSON字段
        entity.setReplyDetails(vo.getReplyDetails());
        entity.setCoCompleteInfo(vo.getCoCompleteInfo());
        entity.setAttaches(vo.getAttaches());

        // 计算用户回复数量，用于后续判断是否有新的用户补充
        if (CollectionUtils.isNotEmpty(vo.getReplyDetails())) {
            entity.setUserReplyCount((int) vo.getReplyDetails()
                    .stream().filter(reply -> reply.getSender() == MiaodaApiConstant.SENDER_USER).count());
        } else {
            entity.setUserReplyCount(0);
        }

        return entity;
    }

    /**
     * 从VO更新实体
     */
    private void updateComplaintFromVO(MiaodaComplaint entity, MiaodaComplaintVO vo) {
        entity.setStatus(vo.getStatus());
        entity.setStatusNo(vo.getStatusNo());
        entity.setCompletedAt(vo.getCompletedAt());
        entity.setCoCompleteStatus(vo.getCoCompleteStatus());
        entity.setCoCompleteAt(vo.getCoCompleteAt());
        entity.setAutoCompleteAt(vo.getAutoCompleteAt());
        entity.setUserCompleteAt(vo.getUserCompleteAt());
        entity.setAttitude(vo.getAttitude());
        entity.setProcess(vo.getProcess());
        entity.setSatisfaction(vo.getSatisfaction());
        entity.setEvalContent(vo.getEvalContent());
        entity.setEvalAt(vo.getEvalAt());

        // 更新JSON字段：直接将VO中的列表字段赋值给实体的JSON字段
        entity.setReplyDetails(vo.getReplyDetails());
        entity.setCoCompleteInfo(vo.getCoCompleteInfo());
        entity.setAttaches(vo.getAttaches());

        // 计算用户回复数量，用于后续判断是否有新的用户补充
        if (CollectionUtils.isNotEmpty(vo.getReplyDetails())) {
            entity.setUserReplyCount((int) vo.getReplyDetails()
                    .stream().filter(reply -> reply.getSender() == MiaodaApiConstant.SENDER_USER).count());
        } else {
            entity.setUserReplyCount(0);
        }

        entity.setGmtModify(new Date());
    }

    /**
     * 设置客户信息
     */
    private void setCustomerInfoFromComplaint(WorkOrderSubmitReqDTO workOrderDTO, MiaodaComplaintVO complaintVO) {
        // 根据手机号查询客户信息
        String phone = complaintVO.getPhone();
        String compPhone = complaintVO.getCompPhone();

        List<String> mobileList = new ArrayList<>();
        if (StringUtils.isNotBlank(phone)) {
            mobileList.add(phone);
        }

        if (StringUtils.isNotBlank(compPhone)) {
            mobileList.add(compPhone);
        }

        CashCustInfoVO cashCustInfoVO = customerService.queryCustomerByMobileList(mobileList);
        if (cashCustInfoVO != null) {
            workOrderDTO.setCustId(String.valueOf(cashCustInfoVO.getId()));
            // 备用号码设置为另一个号码
            workOrderDTO.setMobileBakList(mobileList.stream()
                    .filter(item -> !item.equals(cashCustInfoVO.getMobile())).collect(Collectors.toList()));

            // 设置贷款数量
            workOrderService.setLoanCount(workOrderDTO, cashCustInfoVO.getUuid());
        }


    }

    /**
     * 构建工单描述
     */
    private String buildWorkOrderDescription(MiaodaComplaintVO complaintVO) {
        StringBuilder description = new StringBuilder();

        description.append("【喵达投诉单自动创建】\n");
        description.append("投诉单号：").append(complaintVO.getSn()).append("\n");
        description.append("投诉标题：").append(complaintVO.getTitle()).append("\n");
        description.append("投诉内容：").append(complaintVO.getContent()).append("\n");
        description.append("投诉问题：").append(complaintVO.getIssue()).append("\n");
        description.append("投诉要求：").append(complaintVO.getAppeal()).append("\n");

        if (StringUtils.isNotBlank(complaintVO.getPrivacy())) {
            description.append("涉诉单号：").append(complaintVO.getPrivacy()).append("\n");
        }

        if (StringUtils.isNotBlank(complaintVO.getCost())) {
            description.append("涉诉金额：").append(complaintVO.getCost()).append("\n");
        }

        if (StringUtils.isNotBlank(complaintVO.getUri())) {
            description.append("投诉链接：").append(complaintVO.getUri()).append("\n");
        }

        return description.toString();
    }

    /**
     * 构建催单内容
     */
    private String buildReminderContent(MiaodaReplyDetailVO replyDetailVO) {
        return "【喵达用户催单】\n" +
                "催单时间：" + DateUtil.dateToString(replyDetailVO.getReplyedAt()) + "\n" +
                "用户补充：" + replyDetailVO.getContent() + "\n";
    }

    /**
     * 将时间戳转换为Date对象
     *
     * @param timestamp 时间戳（秒）
     * @return Date对象，如果时间戳为null或0则返回null
     */
    private Date convertTimestampToDate(Long timestamp) {
        if (timestamp == null || timestamp == 0) {
            return null;
        }
        // 喵达API返回的时间戳是秒级的，需要转换为毫秒
        return new Date(timestamp * 1000);
    }

    /**
     * 批量填充工单号
     * 根据投诉单号从本地数据库查询对应的工单号，并设置到MiaodaComplaintVO对象中
     *
     * @param records 投诉单VO列表
     */
    private void fillWorkOrderNumbers(List<MiaodaComplaintVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有投诉单号
            List<String> sns = records.stream()
                    .map(MiaodaComplaintVO::getSn)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(sns)) {
                log.debug("没有有效的投诉单号，跳过工单号填充");
                return;
            }

            log.debug("开始批量查询工单号，投诉单号数量: {}", sns.size());

            // 批量查询工单号
            Map<String, String> snToWorkOrderNoMap = miaodaComplaintMapper.selectWorkOrderNosBySns(sns);

            if (MapUtils.isEmpty(snToWorkOrderNoMap)) {
                log.debug("未查询到任何工单号映射关系");
                return;
            }

            log.debug("查询到工单号映射关系数量: {}", snToWorkOrderNoMap.size());

            // 填充工单号到VO对象
            int filledCount = 0;
            for (MiaodaComplaintVO record : records) {
                String sn = record.getSn();
                if (StringUtils.isNotBlank(sn) && snToWorkOrderNoMap.containsKey(sn)) {
                    String workOrderNo = snToWorkOrderNoMap.get(sn);
                    if (StringUtils.isNotBlank(workOrderNo)) {
                        record.setWorkOrderNo(workOrderNo);
                        filledCount++;
                    }
                }
            }

            log.debug("成功填充工单号数量: {}", filledCount);

        } catch (Exception e) {
            log.error("批量填充工单号失败", e);
            // 不抛出异常，确保分页查询功能不受影响
        }
    }

    /**
     * 转换时间字段用于API调用
     * 将String格式的时间转换为时间戳格式，用于调用喵达API
     *
     * @param queryDTO 原始查询DTO
     * @return 转换后的API查询DTO
     */
    private MiaodaComplaintApiQueryDTO convertTimeFieldsForApi(MiaodaComplaintQueryDTO queryDTO) {
        // 验证时间格式
        String validationError = DateTimeUtil.validateTimeRange(queryDTO.getSt(), queryDTO.getEt());
        if (validationError != null) {
            log.warn("时间参数验证失败: {}, 查询参数: {}", validationError, JSON.toJSONString(queryDTO));
            throw new IllegalArgumentException(validationError);
        }

        // 创建API查询DTO
        MiaodaComplaintApiQueryDTO apiQueryDTO = new MiaodaComplaintApiQueryDTO();

        // 复制基本字段
        BeanUtils.copyProperties(queryDTO, apiQueryDTO);

        // 转换时间字段
        if (StringUtils.isNotBlank(queryDTO.getSt())) {
            Long startTimestamp = DateTimeUtil.convertToTimestamp(queryDTO.getSt());
            if (startTimestamp != null) {
                apiQueryDTO.setSt(startTimestamp);
                log.debug("起始时间转换: {} -> {}", queryDTO.getSt(), startTimestamp);
            } else {
                log.warn("起始时间转换失败: {}", queryDTO.getSt());
                throw new IllegalArgumentException("起始时间格式不正确: " + queryDTO.getSt());
            }
        }

        if (StringUtils.isNotBlank(queryDTO.getEt())) {
            Long endTimestamp = DateTimeUtil.convertToTimestamp(queryDTO.getEt());
            if (endTimestamp != null) {
                apiQueryDTO.setEt(endTimestamp);
                log.debug("结束时间转换: {} -> {}", queryDTO.getEt(), endTimestamp);
            } else {
                log.warn("结束时间转换失败: {}", queryDTO.getEt());
                throw new IllegalArgumentException("结束时间格式不正确: " + queryDTO.getEt());
            }
        }

        // 记录时间范围信息
        String timeRangeLog = DateTimeUtil.formatTimeRangeForLog(queryDTO.getSt(), queryDTO.getEt());
        log.info("查询时间范围: {}", timeRangeLog);

        return apiQueryDTO;
    }

    @Override
    public Page<MiaodaComplaintVO> queryLocalComplaints(MiaodaComplaintQueryDTO queryDTO) {
        try {
            log.info("查询本地投诉单数据，参数：{}", JSON.toJSONString(queryDTO));

            // 强制验证时间参数
            validateTimeRangeParameters(queryDTO);

            // 验证和处理分页参数
            if (queryDTO.getPage() == null || queryDTO.getPage() < 1) {
                queryDTO.setPage(1);
            }
            if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
                queryDTO.setPageSize(10);
            }
            if (queryDTO.getPageSize() > 100) {
                queryDTO.setPageSize(100);
            }

            // 构建查询条件
            QueryWrapper<MiaodaComplaint> wrapper = buildLocalQueryWrapper(queryDTO);

            // 执行分页查询
            Page<MiaodaComplaint> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
            Page<MiaodaComplaint> resultPage = miaodaComplaintMapper.selectPage(page, wrapper);

            // 转换为VO对象
            List<MiaodaComplaintVO> voList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
                for (MiaodaComplaint entity : resultPage.getRecords()) {
                    MiaodaComplaintVO vo = convertEntityToVO(entity);
                    voList.add(vo);
                }
            }

            // 构建返回结果
            Page<MiaodaComplaintVO> result = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
            result.setRecords(voList);
            result.setTotal(resultPage.getTotal());
            result.setPages(resultPage.getPages());
            result.setCurrent(resultPage.getCurrent());
            result.setSize(resultPage.getSize());

            log.info("本地查询完成，返回{}条记录，总数：{}", voList.size(), result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("查询本地投诉单数据异常", e);
            return new Page<>(queryDTO.getPage() != null ? queryDTO.getPage() : 1,
                            queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10);
        }
    }

    @Override
    public int processAutoReply() {
        try {
            log.info("开始处理自动回复");

            // 查询需要自动回复的投诉单
            List<MiaodaComplaint> needReplyComplaints = findComplaintsNeedAutoReply();

            if (CollectionUtils.isEmpty(needReplyComplaints)) {
                log.debug("没有需要自动回复的投诉单");
                return 0;
            }

            log.info("找到{}条需要自动回复的投诉单", needReplyComplaints.size());

            String replyContent = autoReplyConfigService.getAutoReplyContent();
            int successCount = 0;
            int failCount = 0;

            for (MiaodaComplaint complaint : needReplyComplaints) {
                try {
                    boolean success = performAutoReply(complaint, replyContent);
                    if (success) {
                        // 更新自动回复状态
                        updateAutoReplyStatus(complaint.getSn(), 1, null);
                        successCount++;
                        log.info("投诉单自动回复成功: {}", complaint.getSn());
                    } else {
                        updateAutoReplyStatus(complaint.getSn(), 2, "回复失败");
                        failCount++;
                        log.warn("投诉单自动回复失败: {}", complaint.getSn());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("投诉单自动回复异常: {}", complaint.getSn(), e);
                    updateAutoReplyStatus(complaint.getSn(), 2, e.getMessage());
                }
            }

            log.info("自动回复执行完成，成功: {}, 失败: {}", successCount, failCount);
            return successCount + failCount;

        } catch (Exception e) {
            log.error("处理自动回复异常", e);
            return 0;
        }
    }

    /**
     * 为新投诉单处理自动回复
     */
    private void processAutoReplyForNewComplaint(MiaodaComplaintVO complaint) {
        try {
            // 检查自动回复功能是否启用
            if (!autoReplyConfigService.isAutoReplyEnabled()) {
                log.debug("自动回复功能已禁用，跳过投诉单: {}", complaint.getSn());
                return;
            }

            // 检查投诉单状态是否需要回复
            if (!isComplaintNeedReply(complaint)) {
                log.debug("投诉单{}状态不需要自动回复", complaint.getSn());
                return;
            }

            // 检查是否在时限内
//            if (!isWithinReplyTimeLimit(complaint)) {
//                log.debug("投诉单{}超出自动回复时限", complaint.getSn());
//                return;
//            }

            log.info("为新投诉单{}执行自动回复", complaint.getSn());

            String replyContent = autoReplyConfigService.getAutoReplyContent();
            boolean success = performAutoReplyBySn(complaint.getSn(), replyContent);

            if (success) {
                updateAutoReplyStatus(complaint.getSn(), 1, null);
                log.info("新投诉单自动回复成功: {}", complaint.getSn());
            } else {
                updateAutoReplyStatus(complaint.getSn(), 2, "回复失败");
                log.warn("新投诉单自动回复失败: {}", complaint.getSn());
            }

        } catch (Exception e) {
            log.error("新投诉单自动回复异常: {}", complaint.getSn(), e);
            updateAutoReplyStatus(complaint.getSn(), 2, e.getMessage());
        }
    }

    /**
     * 查询需要自动回复的投诉单
     */
    private List<MiaodaComplaint> findComplaintsNeedAutoReply() {
        QueryWrapper<MiaodaComplaint> wrapper = new QueryWrapper<>();
        wrapper.eq("status", "待回复") // 只处理待回复状态的投诉单
               .and(w -> w.isNull("auto_reply_status") // 未进行过自动回复
                        .or()
                        .eq("auto_reply_status", 2)) // 或者自动回复失败，需要重试
               .eq("sync_status", MiaodaSyncStatusEnum.SUCCESS.getCode()) // 同步成功的
               .orderByAsc("assigned_at"); // 按创建时间排序

        return miaodaComplaintMapper.selectList(wrapper);
    }

    /**
     * 执行自动回复
     */
    private boolean performAutoReply(MiaodaComplaint complaint, String content) {
        return performAutoReplyBySn(complaint.getSn(), content);
    }

    /**
     * 根据投诉单号执行自动回复
     */
    private boolean performAutoReplyBySn(String sn, String content) {
        try {
            MiaodaReplyDTO replyDTO = new MiaodaReplyDTO();
            replyDTO.setSns(sn);
            replyDTO.setContent(content);
            replyDTO.setHideAttach(autoReplyConfigService.getHideAttach());
            replyDTO.setHideContent(autoReplyConfigService.getHideContent());

            return replyComplaint(replyDTO);
        } catch (Exception e) {
            log.error("执行自动回复失败: {}", sn, e);
            return false;
        }
    }

    /**
     * 检查投诉单是否需要回复
     */
    private boolean isComplaintNeedReply(MiaodaComplaintVO complaint) {
        // 只对待回复状态的投诉单进行自动回复
        return "待回复".equals(complaint.getStatus());
    }

    /**
     * 检查是否在回复时限内
     */
    private boolean isWithinReplyTimeLimit(MiaodaComplaintVO complaint) {
        if (complaint.getAssignedAt() == null) {
            return false;
        }

        int timeLimit = autoReplyConfigService.getReplyTimeLimit();
        LocalDateTime assignedTime = complaint.getAssignedAt().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(timeLimit);

        return assignedTime.isAfter(cutoffTime);
    }

    /**
     * 更新自动回复状态
     */
    private void updateAutoReplyStatus(String sn, Integer status, String errorMsg) {
        try {
            UpdateWrapper<MiaodaComplaint> wrapper = new UpdateWrapper<>();
            wrapper.eq("sn", sn)
                    .set("auto_reply_status", status) // 1-成功，2-失败
                    .set("auto_reply_time", new Date())
                    .set("gmt_modify", new Date());

            if (StringUtils.isNotBlank(errorMsg)) {
                // 如果有错误信息，可以记录到备注字段或日志中
                log.warn("投诉单{}自动回复失败: {}", sn, errorMsg);
            }

            miaodaComplaintMapper.update(null, wrapper);
        } catch (Exception e) {
            log.error("更新自动回复状态失败: {}", sn, e);
        }
    }

    /**
     * 构建本地查询条件
     */
    private QueryWrapper<MiaodaComplaint> buildLocalQueryWrapper(MiaodaComplaintQueryDTO queryDTO) {
        QueryWrapper<MiaodaComplaint> wrapper = new QueryWrapper<>();

        // 投诉单号（精确匹配）
        if (StringUtils.isNotBlank(queryDTO.getSn())) {
            wrapper.eq("sn", queryDTO.getSn());
        }

        // 投诉状态
        if (Objects.nonNull(queryDTO.getStatus())) {
            wrapper.eq("status", MiaodaComplaintStatusEnum.getDescByCode(queryDTO.getStatus()));
        }

        // 工单编号
        if (StringUtils.isNotBlank(queryDTO.getWorkOrderNo())) {
            wrapper.eq("work_order_no", queryDTO.getWorkOrderNo());
        }

        // 投诉人昵称（模糊匹配）
        if (StringUtils.isNotBlank(queryDTO.getNickname())) {
            wrapper.like("nickname", queryDTO.getNickname());
        }

        // 联系方式（模糊匹配）
        if (StringUtils.isNotBlank(queryDTO.getPhone())) {
            wrapper.and(w -> w.like("phone", queryDTO.getPhone())
                            .or()
                            .like("comp_phone", queryDTO.getPhone()));
        }

        // 同步状态
        if (queryDTO.getSyncStatus() != null) {
            wrapper.eq("sync_status", queryDTO.getSyncStatus());
        }

        // 自动回复状态
        if (queryDTO.getAutoReplyStatus() != null) {
            wrapper.eq("auto_reply_status", queryDTO.getAutoReplyStatus());
        }

        // 自动回复状态
        if (queryDTO.getCoCompleteStatus() != null) {
            wrapper.eq("co_complete_status", queryDTO.getCoCompleteStatus());
        }

        // 分配时间范围
        if (StringUtils.isNotBlank(queryDTO.getSt())) {
            wrapper.ge("assigned_at", queryDTO.getSt());
        }
        if (StringUtils.isNotBlank(queryDTO.getEt())) {
            wrapper.le("assigned_at", queryDTO.getEt());
        }

        // 排序
        String orderBy = StringUtils.isNotBlank(queryDTO.getOrderBy()) ? queryDTO.getOrderBy() : "assigned_at";
        String orderDirection = StringUtils.isNotBlank(queryDTO.getOrderDirection()) ? queryDTO.getOrderDirection() : "DESC";

        if ("ASC".equalsIgnoreCase(orderDirection)) {
            wrapper.orderByAsc(orderBy);
        } else {
            wrapper.orderByDesc(orderBy);
        }

        return wrapper;
    }

    /**
     * 将实体转换为VO对象
     */
    private MiaodaComplaintVO convertEntityToVO(MiaodaComplaint entity) {
        MiaodaComplaintVO vo = new MiaodaComplaintVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置JSON字段：直接将实体中的JSON字段赋值给VO的列表字段
        vo.setReplyDetails(entity.getReplyDetails());
        vo.setCoCompleteInfo(entity.getCoCompleteInfo());
        vo.setAttaches(entity.getAttaches());

        return vo;
    }

    /**
     * 验证时间范围参数
     * 强制要求提供时间范围，并验证时间格式和间隔限制
     *
     * @param queryDTO 查询参数
     * @throws IllegalArgumentException 当时间参数不符合要求时
     */
    private void validateTimeRangeParameters(MiaodaComplaintQueryDTO queryDTO) {
        String startTime = queryDTO.getSt();
        String endTime = queryDTO.getEt();

        // 1. 强制验证时间参数必须同时提供
        if (StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("查询本地数据必须提供时间范围：起始时间（st）和结束时间（et）不能为空");
        }

        if (StringUtils.isBlank(startTime)) {
            throw new IllegalArgumentException("起始时间（st）不能为空，格式：yyyy-MM-dd HH:mm:ss");
        }

        if (StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("结束时间（et）不能为空，格式：yyyy-MM-dd HH:mm:ss");
        }

        // 2. 验证时间格式和范围
        String validationError = DateTimeUtil.validateTimeRange(startTime, endTime);
        if (validationError != null) {
            throw new IllegalArgumentException("时间参数验证失败：" + validationError);
        }

        // 3. 验证时间间隔不能超过31天
        try {
            Long startTimestamp = DateTimeUtil.convertToTimestamp(startTime);
            Long endTimestamp = DateTimeUtil.convertToTimestamp(endTime);

            long timeDiffDays = DateTimeUtil.getTimeDiffDays(startTimestamp, endTimestamp);

            log.debug("时间范围验证通过，查询范围：{}天", timeDiffDays);

        } catch (IllegalArgumentException e) {
            // 重新抛出已知的参数异常
            throw e;
        } catch (Exception e) {
            log.error("时间范围验证异常", e);
            throw new IllegalArgumentException("时间参数验证失败：" + e.getMessage());
        }
    }


}
