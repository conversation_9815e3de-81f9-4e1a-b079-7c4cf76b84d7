package com.welab.crm.operate.util;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {
    public static <T> List<List<T>> splitList(List<T> list, int x) {
        if (x <= 0) {
            throw new IllegalArgumentException("x must be positive");
        }
        List<List<T>> result = new ArrayList<>();
        if (list == null || list.isEmpty()) {
            return result;
        }
        int start = 0;
        while (start < list.size()) {
            int end = Math.min(start + x, list.size());
            List<T> sublist = new ArrayList<>(list.subList(start, end));
            result.add(sublist);
            start += x;
        }
        return result;
    }


}