package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.welab.crm.operate.domain.DataLoanApplication;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工单分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface DataLoanApplicationMapper extends BaseMapper<DataLoanApplication> {

    List<DataLoanApplication> selectByAppNoList(@Param("woTaskId") Long id, @Param("appNoList") List<String> appNoList);
}
