package com.welab.crm.operate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.ComplaintTypeMapping;
import com.welab.crm.operate.dto.miaoda.ComplaintTypeMappingDTO;
import com.welab.crm.operate.dto.miaoda.ComplaintTypeMappingQueryDTO;
import com.welab.crm.operate.vo.miaoda.ComplaintTypeMappingVO;

import java.util.List;

/**
 * 投诉类型映射服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface ComplaintTypeMappingService {

    /**
     * 分页查询投诉类型映射
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<ComplaintTypeMappingVO> queryMappingPage(ComplaintTypeMappingQueryDTO queryDTO);

    /**
     * 根据ID查询映射
     *
     * @param id 主键ID
     * @return 映射信息
     */
    ComplaintTypeMappingVO getMappingById(Long id);

    /**
     * 保存投诉类型映射
     *
     * @param mappingDTO 映射信息
     * @param operator 操作人
     * @return 是否成功
     */
    boolean saveMapping(ComplaintTypeMappingDTO mappingDTO, String operator);

    /**
     * 更新投诉类型映射
     *
     * @param mappingDTO 映射信息
     * @param operator 操作人
     * @return 是否成功
     */
    boolean updateMapping(ComplaintTypeMappingDTO mappingDTO, String operator);

    /**
     * 删除投诉类型映射
     *
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteMapping(Long id);

    /**
     * 批量删除投诉类型映射
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean batchDeleteMapping(List<Long> ids);

    /**
     * 启用/禁用映射
     *
     * @param id 主键ID
     * @param isActive 是否启用
     * @param operator 操作人
     * @return 是否成功
     */
    boolean toggleMappingStatus(Long id, Integer isActive, String operator);

    /**
     * 根据喵达投诉问题类型查询映射
     *
     * @param miaodaIssue 喵达投诉问题类型
     * @return 映射信息
     */
    ComplaintTypeMapping getMappingByMiaodaIssue(String miaodaIssue);

    /**
     * 智能匹配投诉类型映射
     *
     * @param issueList 投诉问题标签列表
     * @return 最佳匹配的映射
     */
    ComplaintTypeMapping getBestMatchMapping(List<String> issueList);

    /**
     * 查询所有启用的映射
     *
     * @return 映射列表
     */
    List<ComplaintTypeMapping> getActiveMappings();

    /**
     * 根据工单组合配置ID查询映射
     *
     * @param opDictInfoConfId 工单组合配置ID
     * @return 映射列表
     */
    List<ComplaintTypeMapping> getMappingsByOpDictInfoConfId(Long opDictInfoConfId);

    /**
     * 检查映射是否存在
     *
     * @param miaodaIssue 喵达投诉问题类型
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByMiaodaIssue(String miaodaIssue, Long excludeId);

    /**
     * 初始化默认映射配置
     *
     * @param operator 操作人
     * @return 初始化数量
     */
    int initDefaultMappings(String operator);

    /**
     * 导入映射配置
     *
     * @param mappings 映射列表
     * @param operator 操作人
     * @return 导入数量
     */
    int importMappings(List<ComplaintTypeMappingDTO> mappings, String operator);

    /**
     * 导出映射配置
     *
     * @param queryDTO 查询条件
     * @return 映射列表
     */
    List<ComplaintTypeMappingVO> exportMappings(ComplaintTypeMappingQueryDTO queryDTO);
}
