package com.welab.crm.operate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.welab.crm.operate.domain.MiaodaComplaint;
import com.welab.crm.operate.dto.miaoda.MiaodaComplaintQueryDTO;
import com.welab.crm.operate.vo.miaoda.MiaodaComplaintVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 喵达投诉单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface MiaodaComplaintMapper extends BaseMapper<MiaodaComplaint> {

    /**
     * 分页查询喵达投诉单列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 投诉单列表
     */
    Page<MiaodaComplaintVO> selectComplaintPage(Page<MiaodaComplaintVO> page, @Param("query") MiaodaComplaintQueryDTO queryDTO);

    /**
     * 根据投诉单号查询投诉单
     *
     * @param sn 投诉单号
     * @return 投诉单信息
     */
    MiaodaComplaint selectBySn(@Param("sn") String sn);

    /**
     * 根据工单编号查询投诉单
     *
     * @param workOrderNo 工单编号
     * @return 投诉单信息
     */
    List<MiaodaComplaint> selectByWorkOrderNo(@Param("workOrderNo") String workOrderNo);

    /**
     * 查询需要同步的投诉单
     *
     * @param limit 限制数量
     * @return 投诉单列表
     */
    List<MiaodaComplaint> selectNeedSyncComplaints(@Param("limit") Integer limit);

    /**
     * 批量更新投诉单状态
     *
     * @param complaints 投诉单列表
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("complaints") List<MiaodaComplaint> complaints);

    /**
     * 根据手机号查询相关投诉单
     *
     * @param phone 手机号
     * @return 投诉单列表
     */
    List<MiaodaComplaint> selectByPhone(@Param("phone") String phone);

    /**
     * 查询指定时间范围内的投诉单
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 投诉单列表
     */
    List<MiaodaComplaint> selectByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 统计投诉单数量
     *
     * @param queryDTO 查询条件
     * @return 数量
     */
    Long countComplaints(@Param("query") MiaodaComplaintQueryDTO queryDTO);

    /**
     * 更新工单编号
     * @param sn
     * @param workOrderNo
     */
    void updateWorkOrderNoBySn(@Param("sn") String sn, @Param("workOrderNo") String workOrderNo);

    /**
     * 批量根据投诉单号查询工单号
     *
     * @param sns 投诉单号列表
     * @return sn -> workOrderNo 的映射关系
     */
    Map<String, String> selectWorkOrderNosBySns(@Param("sns") List<String> sns);

    /**
     * 根据投诉单号查询投诉单
     *
     * @param sns 投诉单号列表
     * @return 投诉单列表
     */
    List<MiaodaComplaint> selectBySns(@Param("sns") List<String> sns);
}
