package com.welab.crm.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工单任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wo_task")
public class WoTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分单时间
     */
    private Date distributeTime;

    /**
     * 提单时间
     */
    private Date submitTime;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 身份证
     */
    private String cnid;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号
     */
    private String mobileBak;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单类型
     */
    private String type;

    /**
     * 工单状态
     */
    private String status;

    /**
     * 工单大类编码
     */
    private Long orderOneClass;

    /**
     * 工单二类编码
     */
    private Long orderTwoClass;

    /**
     * 工单三类编码
     */
    private Long orderThreeClass;

    /**
     * 子工单分类编码
     */
    private Long orderCase;

    /**
     * 加急标识,0否,1是
     */
    private String urgentFlag;

    /**
     * 回访标识,0否,1是
     */
    private String callbackFlag;

    /**
     * 创建人
     */
    private String createStaffId;

    /**
     * 创建人所在组
     */
    private String createGroupCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改人
     */
    private String modifyStaffId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date gmtModify;

    /**
     * 反馈内容
     */
    private String description;

    /**
     * 处理意见
     */
    private String opinion;

    /**
     * 回访小结
     */
    private String callbackNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 催单标识,0否,1是
     */
    private String reminderFlag;

    /**
     * 成功贷款笔数
     */
    private Integer sucLoanCount;

    /**
     * 投诉渠道
     */
    private String complaintsChannel;

    /**
     * 资方简称
     */
    private String fundName;

    /**
     * 投诉渠道与资金方映射关系
     */
    private String matchUp;

    /**
     * 备用手机号列表
     */
    private String mobileBaks;

    /**
     * 邮箱号
     */
    private String email;


    /**
     * 工单外部来源
     */
    private String externalSource;

    /**
     * 在途贷款笔数
     */
    private Integer currentLoanCount;

    /**
     * 是否超时未联系;1-是，0-否
     */
    private Boolean responseTimeOut;

    /**
     * 处理方案
     */
    private String resolveContent;

    /**
     * 喵达订单号
     */
    private String miaodaSn;

    /**
     * 喵达状态
     */
    private String miaodaStatus;

    /**
     * 喵达投诉链接
     */
    private String miaodaUri;
}
